// Package docs_jumpserver_sync Code generated by swaggo/swag. DO NOT EDIT
package docs_jumpserver_sync

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "Arboris Team",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/admin/dead-letter/messages/all": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取死信队列中的所有消息，不分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "获取所有死信消息",
                "responses": {
                    "200": {
                        "description": "成功获取所有死信消息",
                        "schema": {
                            "$ref": "#/definitions/http.DeadMessageListResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/messages/batch-reprocess": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量将多个死信消息重新放入处理队列进行处理，最多支持100个消息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "批量重新处理死信消息",
                "parameters": [
                    {
                        "description": "批量重新处理请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.BatchReprocessRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "批量处理完成",
                        "schema": {
                            "$ref": "#/definitions/http.BatchReprocessResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/messages/ids": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取死信队列中所有消息的ID列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "获取死信消息ID列表",
                "responses": {
                    "200": {
                        "description": "成功获取消息ID列表",
                        "schema": {
                            "$ref": "#/definitions/http.MessageIDsResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/messages/stream/{stream_id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据Redis Stream ID获取死信队列中的消息详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "根据Stream ID获取死信消息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Redis Stream ID",
                        "name": "stream_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取死信消息",
                        "schema": {
                            "$ref": "#/definitions/http.DeadMessageResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "消息不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/messages/stream/{stream_id}/reprocess": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据Redis Stream ID将指定的死信消息重新放入处理队列进行处理",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "根据Stream ID重新处理死信消息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Redis Stream ID",
                        "name": "stream_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功重新处理消息",
                        "schema": {
                            "$ref": "#/definitions/http.ReprocessResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "消息不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/messages/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据消息ID获取死信队列中的单个消息详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "获取单个死信消息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "消息ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取死信消息",
                        "schema": {
                            "$ref": "#/definitions/http.DeadMessageResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "消息不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "从死信队列中永久删除指定的消息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "删除死信消息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "消息ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功删除消息",
                        "schema": {
                            "$ref": "#/definitions/http.DeleteResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "消息不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/messages/{id}/reprocess": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "将指定的死信消息重新放入处理队列进行处理",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "重新处理死信消息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "消息ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功重新处理消息",
                        "schema": {
                            "$ref": "#/definitions/http.ReprocessResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "消息不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/dead-letter/stats": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取死信队列的统计信息，包括消息数量、处理状态等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeadLetter"
                ],
                "summary": "获取死信队列统计信息",
                "responses": {
                    "200": {
                        "description": "成功获取统计信息",
                        "schema": {
                            "$ref": "#/definitions/http.StatsResponse"
                        }
                    },
                    "500": {
                        "description": "内部服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/health": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "检查JumpServer同步服务管理接口的健康状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin"
                ],
                "summary": "管理接口健康检查",
                "responses": {
                    "200": {
                        "description": "服务健康",
                        "schema": {
                            "$ref": "#/definitions/http.AdminHealthResponse"
                        }
                    }
                }
            }
        },
        "/api/admin/status": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取JumpServer同步服务的整体运行状态，包括死信队列状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "获取系统状态",
                "responses": {
                    "200": {
                        "description": "系统状态信息",
                        "schema": {
                            "$ref": "#/definitions/http.SystemStatusResponse"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "检查JumpServer同步服务的健康状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "服务正常",
                        "schema": {
                            "$ref": "#/definitions/http.HealthResponse"
                        }
                    }
                }
            }
        },
        "/info": {
            "get": {
                "description": "获取JumpServer同步服务的版本、运行时间和配置信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "获取服务信息",
                "responses": {
                    "200": {
                        "description": "服务信息",
                        "schema": {
                            "$ref": "#/definitions/http.ServiceInfoResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "http.AdminHealthResponse": {
            "type": "object",
            "properties": {
                "service": {
                    "type": "string",
                    "example": "jumpserver-sync"
                },
                "status": {
                    "type": "string",
                    "example": "healthy"
                },
                "timestamp": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "http.BatchReprocessRequest": {
            "type": "object",
            "required": [
                "message_ids"
            ],
            "properties": {
                "message_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"msg-123\"",
                        "\"msg-456\"]"
                    ]
                }
            }
        },
        "http.BatchReprocessResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/http.BatchReprocessResponseData"
                },
                "message": {
                    "type": "string",
                    "example": "Batch reprocess completed"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.BatchReprocessResponseData": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "type": "integer",
                    "example": 2
                },
                "failed_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"msg-123\"",
                        "\"msg-456\"]"
                    ]
                },
                "success_count": {
                    "type": "integer",
                    "example": 8
                },
                "total_count": {
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "http.DeadMessageListData": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "example": 10
                },
                "limit": {
                    "type": "integer",
                    "example": 50
                },
                "messages": {
                    "type": "array",
                    "items": {}
                },
                "offset": {
                    "type": "integer",
                    "example": 0
                }
            }
        },
        "http.DeadMessageListResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/http.DeadMessageListData"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.DeadMessageResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.DeleteResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/http.DeleteResponseData"
                },
                "message": {
                    "type": "string",
                    "example": "Message deleted successfully"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.DeleteResponseData": {
            "type": "object",
            "properties": {
                "message_id": {
                    "type": "string",
                    "example": "msg-123456"
                }
            }
        },
        "http.ErrorResponse": {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "string",
                    "example": "connection timeout"
                },
                "error": {
                    "type": "string",
                    "example": "Failed to list dead messages"
                }
            }
        },
        "http.HealthResponse": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "example": "ok"
                },
                "timestamp": {
                    "type": "integer",
                    "example": **********
                }
            }
        },
        "http.JumpServerConfigInfo": {
            "type": "object",
            "properties": {
                "base_url": {
                    "type": "string",
                    "example": "http://jumpserver.example.com"
                },
                "organization": {
                    "type": "string",
                    "example": "Default"
                }
            }
        },
        "http.MessageIDsData": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "example": 2
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"1755594093213-0\"",
                        "\"1755594093214-0\"]"
                    ]
                }
            }
        },
        "http.MessageIDsResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/http.MessageIDsData"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.RedisConfigInfo": {
            "type": "object",
            "properties": {
                "addr": {
                    "type": "string",
                    "example": "127.0.0.1:6379"
                },
                "db": {
                    "type": "integer",
                    "example": 0
                }
            }
        },
        "http.ReprocessResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/http.ReprocessResponseData"
                },
                "message": {
                    "type": "string",
                    "example": "Message reprocessed successfully"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.ReprocessResponseData": {
            "type": "object",
            "properties": {
                "message_id": {
                    "type": "string",
                    "example": "msg-123456"
                }
            }
        },
        "http.ServiceConfigInfo": {
            "type": "object",
            "properties": {
                "jumpserver": {
                    "$ref": "#/definitions/http.JumpServerConfigInfo"
                },
                "redis": {
                    "$ref": "#/definitions/http.RedisConfigInfo"
                },
                "sync": {
                    "$ref": "#/definitions/http.SyncConfigInfo"
                }
            }
        },
        "http.ServiceInfoResponse": {
            "type": "object",
            "properties": {
                "config": {
                    "$ref": "#/definitions/http.ServiceConfigInfo"
                },
                "uptime": {
                    "type": "integer",
                    "example": **********
                },
                "version": {
                    "type": "string",
                    "example": "1.0.0"
                }
            }
        },
        "http.StatsResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "http.SyncConfigInfo": {
            "type": "object",
            "properties": {
                "batch_size": {
                    "type": "integer",
                    "example": 10
                },
                "consumer": {
                    "type": "string",
                    "example": "jumpserver-sync-1"
                },
                "group": {
                    "type": "string",
                    "example": "jumpserver-sync"
                },
                "poll_interval": {
                    "type": "string",
                    "example": "5s"
                },
                "stream": {
                    "type": "string",
                    "example": "arboris:sync:events"
                }
            }
        },
        "http.SystemStatusData": {
            "type": "object",
            "properties": {
                "dead_letter_queue": {},
                "service": {
                    "type": "string",
                    "example": "jumpserver-sync"
                },
                "status": {
                    "type": "string",
                    "example": "running"
                }
            }
        },
        "http.SystemStatusResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/http.SystemStatusData"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "API密钥认证，格式：Bearer {token}",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    },
    "tags": [
        {
            "description": "健康检查和服务信息接口",
            "name": "Health"
        },
        {
            "description": "管理接口，包括死信队列管理和系统状态监控",
            "name": "Admin"
        },
        {
            "description": "死信队列管理接口，用于处理同步失败的消息",
            "name": "DeadLetter"
        },
        {
            "description": "系统状态和监控接口",
            "name": "System"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "JumpServer同步服务API",
	Description:      "JumpServer同步服务，负责将Arboris系统中的资产信息同步到JumpServer堡垒机系统\n提供死信队列管理、系统监控、健康检查等功能",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
