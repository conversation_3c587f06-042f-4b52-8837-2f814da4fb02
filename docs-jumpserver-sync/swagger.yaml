basePath: /
definitions:
  http.AdminHealthResponse:
    properties:
      service:
        example: jumpserver-sync
        type: string
      status:
        example: healthy
        type: string
      timestamp:
        additionalProperties: true
        type: object
    type: object
  http.BatchReprocessRequest:
    properties:
      message_ids:
        example:
        - '["msg-123"'
        - '"msg-456"]'
        items:
          type: string
        type: array
    required:
    - message_ids
    type: object
  http.BatchReprocessResponse:
    properties:
      data:
        $ref: '#/definitions/http.BatchReprocessResponseData'
      message:
        example: Batch reprocess completed
        type: string
      success:
        example: true
        type: boolean
    type: object
  http.BatchReprocessResponseData:
    properties:
      failed_count:
        example: 2
        type: integer
      failed_ids:
        example:
        - '["msg-123"'
        - '"msg-456"]'
        items:
          type: string
        type: array
      success_count:
        example: 8
        type: integer
      total_count:
        example: 10
        type: integer
    type: object
  http.DeadMessageListData:
    properties:
      count:
        example: 10
        type: integer
      limit:
        example: 50
        type: integer
      messages:
        items: {}
        type: array
      offset:
        example: 0
        type: integer
    type: object
  http.DeadMessageListResponse:
    properties:
      data:
        $ref: '#/definitions/http.DeadMessageListData'
      success:
        example: true
        type: boolean
    type: object
  http.DeadMessageResponse:
    properties:
      data: {}
      success:
        example: true
        type: boolean
    type: object
  http.DeleteResponse:
    properties:
      data:
        $ref: '#/definitions/http.DeleteResponseData'
      message:
        example: Message deleted successfully
        type: string
      success:
        example: true
        type: boolean
    type: object
  http.DeleteResponseData:
    properties:
      message_id:
        example: msg-123456
        type: string
    type: object
  http.ErrorResponse:
    properties:
      detail:
        example: connection timeout
        type: string
      error:
        example: Failed to list dead messages
        type: string
    type: object
  http.HealthResponse:
    properties:
      status:
        example: ok
        type: string
      timestamp:
        example: **********
        type: integer
    type: object
  http.JumpServerConfigInfo:
    properties:
      base_url:
        example: http://jumpserver.example.com
        type: string
      organization:
        example: Default
        type: string
    type: object
  http.MessageIDsData:
    properties:
      count:
        example: 2
        type: integer
      ids:
        example:
        - '["1755594093213-0"'
        - '"1755594093214-0"]'
        items:
          type: string
        type: array
    type: object
  http.MessageIDsResponse:
    properties:
      data:
        $ref: '#/definitions/http.MessageIDsData'
      success:
        example: true
        type: boolean
    type: object
  http.RedisConfigInfo:
    properties:
      addr:
        example: 127.0.0.1:6379
        type: string
      db:
        example: 0
        type: integer
    type: object
  http.ReprocessResponse:
    properties:
      data:
        $ref: '#/definitions/http.ReprocessResponseData'
      message:
        example: Message reprocessed successfully
        type: string
      success:
        example: true
        type: boolean
    type: object
  http.ReprocessResponseData:
    properties:
      message_id:
        example: msg-123456
        type: string
    type: object
  http.ServiceConfigInfo:
    properties:
      jumpserver:
        $ref: '#/definitions/http.JumpServerConfigInfo'
      redis:
        $ref: '#/definitions/http.RedisConfigInfo'
      sync:
        $ref: '#/definitions/http.SyncConfigInfo'
    type: object
  http.ServiceInfoResponse:
    properties:
      config:
        $ref: '#/definitions/http.ServiceConfigInfo'
      uptime:
        example: **********
        type: integer
      version:
        example: 1.0.0
        type: string
    type: object
  http.StatsResponse:
    properties:
      data: {}
      success:
        example: true
        type: boolean
    type: object
  http.SyncConfigInfo:
    properties:
      batch_size:
        example: 10
        type: integer
      consumer:
        example: jumpserver-sync-1
        type: string
      group:
        example: jumpserver-sync
        type: string
      poll_interval:
        example: 5s
        type: string
      stream:
        example: arboris:sync:events
        type: string
    type: object
  http.SystemStatusData:
    properties:
      dead_letter_queue: {}
      service:
        example: jumpserver-sync
        type: string
      status:
        example: running
        type: string
    type: object
  http.SystemStatusResponse:
    properties:
      data:
        $ref: '#/definitions/http.SystemStatusData'
      success:
        example: true
        type: boolean
    type: object
info:
  contact:
    email: <EMAIL>
    name: Arboris Team
  description: |-
    JumpServer同步服务，负责将Arboris系统中的资产信息同步到JumpServer堡垒机系统
    提供死信队列管理、系统监控、健康检查等功能
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  title: JumpServer同步服务API
  version: 1.0.0
paths:
  /api/admin/dead-letter/messages/{id}:
    delete:
      consumes:
      - application/json
      description: 从死信队列中永久删除指定的消息
      parameters:
      - description: 消息ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功删除消息
          schema:
            $ref: '#/definitions/http.DeleteResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "404":
          description: 消息不存在
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 删除死信消息
      tags:
      - DeadLetter
    get:
      consumes:
      - application/json
      description: 根据消息ID获取死信队列中的单个消息详情
      parameters:
      - description: 消息ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取死信消息
          schema:
            $ref: '#/definitions/http.DeadMessageResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "404":
          description: 消息不存在
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取单个死信消息
      tags:
      - DeadLetter
  /api/admin/dead-letter/messages/{id}/reprocess:
    post:
      consumes:
      - application/json
      description: 将指定的死信消息重新放入处理队列进行处理
      parameters:
      - description: 消息ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功重新处理消息
          schema:
            $ref: '#/definitions/http.ReprocessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "404":
          description: 消息不存在
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 重新处理死信消息
      tags:
      - DeadLetter
  /api/admin/dead-letter/messages/all:
    get:
      consumes:
      - application/json
      description: 获取死信队列中的所有消息，不分页
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取所有死信消息
          schema:
            $ref: '#/definitions/http.DeadMessageListResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取所有死信消息
      tags:
      - DeadLetter
  /api/admin/dead-letter/messages/batch-reprocess:
    post:
      consumes:
      - application/json
      description: 批量将多个死信消息重新放入处理队列进行处理，最多支持100个消息
      parameters:
      - description: 批量重新处理请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/http.BatchReprocessRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 批量处理完成
          schema:
            $ref: '#/definitions/http.BatchReprocessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量重新处理死信消息
      tags:
      - DeadLetter
  /api/admin/dead-letter/messages/ids:
    get:
      consumes:
      - application/json
      description: 获取死信队列中所有消息的ID列表
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取消息ID列表
          schema:
            $ref: '#/definitions/http.MessageIDsResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取死信消息ID列表
      tags:
      - DeadLetter
  /api/admin/dead-letter/messages/stream/{stream_id}:
    get:
      consumes:
      - application/json
      description: 根据Redis Stream ID获取死信队列中的消息详情
      parameters:
      - description: Redis Stream ID
        in: path
        name: stream_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取死信消息
          schema:
            $ref: '#/definitions/http.DeadMessageResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "404":
          description: 消息不存在
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 根据Stream ID获取死信消息
      tags:
      - DeadLetter
  /api/admin/dead-letter/messages/stream/{stream_id}/reprocess:
    post:
      consumes:
      - application/json
      description: 根据Redis Stream ID将指定的死信消息重新放入处理队列进行处理
      parameters:
      - description: Redis Stream ID
        in: path
        name: stream_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功重新处理消息
          schema:
            $ref: '#/definitions/http.ReprocessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "404":
          description: 消息不存在
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 根据Stream ID重新处理死信消息
      tags:
      - DeadLetter
  /api/admin/dead-letter/stats:
    get:
      consumes:
      - application/json
      description: 获取死信队列的统计信息，包括消息数量、处理状态等
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取统计信息
          schema:
            $ref: '#/definitions/http.StatsResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取死信队列统计信息
      tags:
      - DeadLetter
  /api/admin/health:
    get:
      consumes:
      - application/json
      description: 检查JumpServer同步服务管理接口的健康状态
      produces:
      - application/json
      responses:
        "200":
          description: 服务健康
          schema:
            $ref: '#/definitions/http.AdminHealthResponse'
      security:
      - ApiKeyAuth: []
      summary: 管理接口健康检查
      tags:
      - Admin
  /api/admin/status:
    get:
      consumes:
      - application/json
      description: 获取JumpServer同步服务的整体运行状态，包括死信队列状态
      produces:
      - application/json
      responses:
        "200":
          description: 系统状态信息
          schema:
            $ref: '#/definitions/http.SystemStatusResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取系统状态
      tags:
      - System
  /health:
    get:
      consumes:
      - application/json
      description: 检查JumpServer同步服务的健康状态
      produces:
      - application/json
      responses:
        "200":
          description: 服务正常
          schema:
            $ref: '#/definitions/http.HealthResponse'
      summary: 健康检查
      tags:
      - Health
  /info:
    get:
      consumes:
      - application/json
      description: 获取JumpServer同步服务的版本、运行时间和配置信息
      produces:
      - application/json
      responses:
        "200":
          description: 服务信息
          schema:
            $ref: '#/definitions/http.ServiceInfoResponse'
      summary: 获取服务信息
      tags:
      - Health
securityDefinitions:
  ApiKeyAuth:
    description: API密钥认证，格式：Bearer {token}
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
tags:
- description: 健康检查和服务信息接口
  name: Health
- description: 管理接口，包括死信队列管理和系统状态监控
  name: Admin
- description: 死信队列管理接口，用于处理同步失败的消息
  name: DeadLetter
- description: 系统状态和监控接口
  name: System
