#!/bin/bash

# 测试数据库连接的脚本

echo "=== 测试数据库连接 ==="

# 测试AMS数据库连接
echo "1. 测试AMS数据库连接..."
mysql -h ********** -u root -pzTbMNuT9nbjDvEt2 -e "USE arboris_ams; SELECT id, ip, name FROM host WHERE id = 2;" 2>/dev/null || echo "AMS数据库连接失败"

echo ""

# 测试RDB数据库连接
echo "2. 测试RDB数据库连接..."
mysql -h ********** -u root -pzTbMNuT9nbjDvEt2 -e "USE arboris_rdb; SELECT id, name, ident, source_id, source_type FROM resource WHERE id = 2;" 2>/dev/null || echo "RDB数据库连接失败"

echo ""

# 检查jumpserver-sync的数据库初始化
echo "3. 检查jumpserver-sync进程..."
ps aux | grep jumpserver-sync | grep -v grep

echo ""
echo "4. 检查jumpserver-sync的数据库连接配置..."
cat etc/mysql.yml

echo ""
echo "如果AMS数据库连接正常，应该能看到："
echo "- 主机ID 2的IP地址：*******"
echo "- 资源ID 2的source_id：2，source_type：host"
echo ""
echo "这说明资源2确实对应AMS中的主机2，IP地址是*******"
