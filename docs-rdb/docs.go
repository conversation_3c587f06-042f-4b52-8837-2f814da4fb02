// Package docs_rdb Code generated by swaggo/swag. DO NOT EDIT
package docs_rdb

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/rdb/account-templates": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取账号模板列表，支持分页和按名称查询。name参数支持逗号分隔的多个名称进行批量查询",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取账号模板列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板名称，支持逗号分隔的多个名称，如: name=模板1,模板2,模板3",
                        "name": "name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "账号模板列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountTemplateListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建账号模板，用于批量创建账号",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "parameters": [
                    {
                        "description": "账号模板信息",
                        "name": "template",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AccountTemplate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建的账号模板",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.AccountTemplate"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/account-templates/apply": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将账号模板应用到多个资产，批量创建账号",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "应用账号模板",
                "parameters": [
                    {
                        "description": "模板应用请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.AccountTemplateApplyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "应用结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountCreateResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/account-templates/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据ID获取账号模板详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取账号模板详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "账号模板详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.AccountTemplate"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号模板不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新账号模板信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "更新账号模板",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "账号模板信息",
                        "name": "template",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AccountTemplate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新后的账号模板",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.AccountTemplate"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号模板不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除账号模板，删除前会检查是否有账号在使用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "删除账号模板",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误或有账号正在使用",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号模板不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/accounts": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取账号列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取账号列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "资源ID",
                        "name": "resource_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "模板ID",
                        "name": "template_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "账号列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "直接创建账号，支持单个或批量创建。如需使用模板创建账号，请使用模板应用接口。可以指定template_id使用模板创建，模板中的配置会作为默认值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "创建账号",
                "parameters": [
                    {
                        "description": "账号信息",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.AccountCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountCreateResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/accounts/bind": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将多个账号绑定到多个资源，建立绑定关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "批量绑定账号到资源",
                "parameters": [
                    {
                        "description": "绑定请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.AccountBindingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountBindingResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/accounts/statistics": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取账号的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取账号统计信息",
                "responses": {
                    "200": {
                        "description": "统计信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountStatistics"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/accounts/unbind": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "解除账号与资源的绑定关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "批量解绑账号与资源",
                "parameters": [
                    {
                        "description": "解绑请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.AccountBindingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解绑结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.AccountBindingResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/accounts/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据ID获取账号详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取账号详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "账号详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Account"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新账号信息（不包括用户名和资产绑定）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "更新账号",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "账号信息",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.AccountUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新后的账号",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Account"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除账号，删除前会检查是否有资源绑定关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "删除账号",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误或有资源绑定关系",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/accounts/{id}/bindings": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定账号的所有资产绑定关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取账号绑定关系",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "账号ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定关系列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.AccountResourceBinding"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "账号不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/login": {
            "post": {
                "description": "用户通过用户名和密码登录系统",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证管理"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "login",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.loginInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/logout": {
            "get": {
                "security": [
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "用户登出系统，清除会话信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证管理"
                ],
                "summary": "用户登出",
                "responses": {
                    "200": {
                        "description": "登出成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/white-list": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取IP白名单列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "获取白名单列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "白名单列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.WhiteListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的IP白名单条目，用于访问控制",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "创建白名单条目",
                "parameters": [
                    {
                        "description": "白名单信息",
                        "name": "whitelist",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.createWhiteListInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功，返回ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/white-list/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据ID获取指定的白名单条目详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "获取单个白名单条目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "白名单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "白名单详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.WhiteList"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "白名单不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新指定ID的白名单条目信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "更新白名单条目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "白名单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新的白名单信息",
                        "name": "whitelist",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.updateWhiteListInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "白名单不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定ID的白名单条目",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "删除白名单条目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "白名单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "白名单不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/can-do-global-op": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "检查指定用户是否有权限执行全局操作",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "检查全局操作权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "操作权限点",
                        "name": "op",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "是否有权限",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "boolean"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/can-do-node-op": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "检查指定用户是否有权限在指定节点上执行操作",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "检查节点操作权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "操作权限点",
                        "name": "op",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "nid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "是否有权限",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "boolean"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/can-do-node-ops": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "批量检查指定用户在指定节点上的多个操作权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "批量检查节点操作权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "操作权限点列表，逗号分隔",
                        "name": "ops",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "nid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "权限检查结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "boolean"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/configs/auth": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统认证配置信息，包括密码策略、会话管理等设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证配置"
                ],
                "summary": "获取认证配置",
                "responses": {
                    "200": {
                        "description": "认证配置信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.AuthConfig"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新系统认证配置信息，包括密码策略、会话管理等设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证配置"
                ],
                "summary": "更新认证配置",
                "parameters": [
                    {
                        "description": "认证配置信息",
                        "name": "config",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AuthConfig"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/counter": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统各项统计计数器，如登录次数等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统统计"
                ],
                "summary": "获取系统计数器",
                "responses": {
                    "200": {
                        "description": "统计计数器",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "integer"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/log/login": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员查看所有用户的登录记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日志管理"
                ],
                "summary": "获取登录日志",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "开始时间戳",
                        "name": "btime",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "结束时间戳",
                        "name": "etime",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录日志列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.LoginLogListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/log/operation": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员查看所有类型资源的操作记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日志管理"
                ],
                "summary": "获取操作日志",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "开始时间戳",
                        "name": "btime",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "结束时间戳",
                        "name": "etime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "res_type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作日志列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.OperationLogListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node-cate-field/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定节点分类字段的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类字段管理"
                ],
                "summary": "获取节点分类字段详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点分类字段详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.NodeCateField"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "字段不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node-cate-fields": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据分类获取节点分类的自定义字段列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类字段管理"
                ],
                "summary": "获取节点分类字段列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点分类标识",
                        "name": "cate",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点分类字段列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.NodeCateField"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "为节点分类创建新的自定义字段",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类字段管理"
                ],
                "summary": "创建节点分类字段",
                "parameters": [
                    {
                        "description": "字段信息",
                        "name": "field",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NodeCateField"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node-cate/{id}": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "修改指定节点分类的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类管理"
                ],
                "summary": "修改节点分类",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点分类ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "节点分类信息",
                        "name": "nodeCate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.nodeCatePutForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点分类不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定的节点分类",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类管理"
                ],
                "summary": "删除节点分类",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点分类ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点分类不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node-cates": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取所有节点分类的列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类管理"
                ],
                "summary": "获取节点分类列表",
                "responses": {
                    "200": {
                        "description": "节点分类列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.NodeCate"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的节点分类",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点分类管理"
                ],
                "summary": "创建节点分类",
                "parameters": [
                    {
                        "description": "节点分类信息",
                        "name": "nodeCate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.nodeCatePostForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定节点的详细信息，包括管理员信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Node"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "修改指定节点的信息，包括名称、分类、备注、管理员等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "修改节点",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "节点信息",
                        "name": "node",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.nodeForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改后的节点信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Node"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定的节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "删除节点",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}/fields": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定节点的所有字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点字段值管理"
                ],
                "summary": "获取节点字段值",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点字段值列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.NodeFieldValue"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "批量更新指定节点的字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点字段值管理"
                ],
                "summary": "批量更新节点字段值",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "字段值列表",
                        "name": "fields",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.NodeFieldValue"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}/resources/bind": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将资源绑定到指定节点，支持按ID、UUID、标识批量绑定",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "绑定资源到节点",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "资源绑定参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.resourceBindForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}/resources/cate-count": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定节点下各类资源的数量统计",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源统计"
                ],
                "summary": "获取节点资源分类统计",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "资源分类统计",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/http.ResourceCountByCate"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}/resources/unbind": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将资源从指定节点解绑",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "解绑节点资源",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "资源ID列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.idsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解绑成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}/roles": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定节点下的角色绑定列表，支持分页和用户筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点角色管理"
                ],
                "summary": "获取节点下的角色列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名筛选",
                        "name": "username",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "角色列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "list": {
                                                    "type": "array",
                                                    "items": {}
                                                },
                                                "total": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "为指定节点的用户分配角色权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点角色管理"
                ],
                "summary": "为节点分配角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "角色分配参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.roleUnderNodeForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定节点下用户的角色权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点角色管理"
                ],
                "summary": "删除节点角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "角色删除参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.roleUnderNodeDelForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/node/{id}/roles/try": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "测试当前用户是否有权限删除指定节点的角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点角色管理"
                ],
                "summary": "测试节点角色删除权限",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "有权限",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/nodes": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取节点列表，支持按分类、内部节点、ID列表筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点分类",
                        "name": "cate",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "是否包含内部节点，0-不包含，1-包含",
                        "name": "inner",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "节点ID列表，逗号分隔",
                        "name": "ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的节点，支持创建租户节点和普通节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "创建节点",
                "parameters": [
                    {
                        "description": "节点信息",
                        "name": "node",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.nodeForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建的节点信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Node"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/ops/global": {
            "get": {
                "description": "获取系统定义的页面权限配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取页面角色权限配置",
                "responses": {
                    "200": {
                        "description": "全局权限配置",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/config.OpsSystem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/rdb/ops/local": {
            "get": {
                "description": "获取系统定义的资源权限配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取资源角色权限配置",
                "responses": {
                    "200": {
                        "description": "本地权限配置",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/config.OpsSystem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/rdb/ping": {
            "get": {
                "description": "检查服务是否正常运行",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "pong",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/rdb/pwd-rules": {
            "get": {
                "description": "获取系统密码策略规则，用于前端显示密码要求",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取密码规则",
                "responses": {
                    "200": {
                        "description": "密码规则列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/rdb/resoplogs": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "查询具体某个资源的操作历史记录，一般用在资源详情页面",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "操作日志"
                ],
                "summary": "获取资源操作日志",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "开始时间戳",
                        "name": "btime",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "结束时间戳",
                        "name": "etime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类别",
                        "name": "rescl",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源ID",
                        "name": "resid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作日志列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "list": {
                                                    "type": "array",
                                                    "items": {
                                                        "$ref": "#/definitions/models.OperationLog"
                                                    }
                                                },
                                                "total": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/accounts": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定资源上的所有账号",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "账号管理"
                ],
                "summary": "获取资源的账号列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "资源ID",
                        "name": "resource_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "账号列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Account"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/bindings": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "查看资源的绑定关系，主要用在页面上查看资源挂载的节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "获取资源绑定关系",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源ID列表，逗号分隔",
                        "name": "ids",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源UUID列表，逗号分隔",
                        "name": "uuids",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源标识列表，逗号分隔",
                        "name": "idents",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "资源绑定关系列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.ResourceBinding"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/cate-count": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统中所有资源按分类的数量统计",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源统计"
                ],
                "summary": "获取全部资源分类统计",
                "responses": {
                    "200": {
                        "description": "资源分类统计",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/http.ResourceCountByCate"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/note": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "修改游离资源的备注信息，超级管理员或租户管理员有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "修改资源备注",
                "parameters": [
                    {
                        "description": "资源备注修改参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.resourceNotePutForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/note/try": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "测试当前用户是否有权限修改游离资源的备注，超级管理员或租户管理员有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "测试资源备注修改权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户路径",
                        "name": "tenant",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "有权限",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/orphan": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取游离资源列表，即已经分配给某个租户但没有挂载在服务树的资源",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "获取游离资源列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "批量搜索条件",
                        "name": "batch",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "ident",
                        "description": "搜索字段",
                        "name": "field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "租户筛选",
                        "name": "tenant",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "游离资源列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "list": {
                                                    "type": "array",
                                                    "items": {
                                                        "$ref": "#/definitions/models.Resource"
                                                    }
                                                },
                                                "tenant": {
                                                    "type": "string"
                                                },
                                                "total": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/resources/search": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据批量条件和字段搜索资源",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "搜索资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "批量搜索条件",
                        "name": "batch",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "ident",
                        "description": "搜索字段",
                        "name": "field",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "资源列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Resource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/role/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定角色的详细信息，包括角色信息和权限操作列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "角色详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "operations": {
                                                    "type": "array",
                                                    "items": {
                                                        "type": "string"
                                                    }
                                                },
                                                "role": {
                                                    "$ref": "#/definitions/models.Role"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "修改角色的基本信息，只有超级管理员有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "修改角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "角色信息",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.roleForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定角色，会删除相关联的数据，只有超级管理员有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/role/{id}/users": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定全局角色下绑定的用户列表，支持分页和搜索，只有管理员可以查看",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色绑定的用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "角色用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "list": {
                                                    "type": "array",
                                                    "items": {
                                                        "$ref": "#/definitions/models.User"
                                                    }
                                                },
                                                "role": {
                                                    "$ref": "#/definitions/models.Role"
                                                },
                                                "total": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/role/{id}/users/bind": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将指定用户绑定到全局角色，把某些用户设置为某个全局角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "角色绑定用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户ID列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.idsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/role/{id}/users/unbind": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将指定用户从全局角色中解绑，把某些用户从某个全局角色中移除",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "角色解绑用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户ID列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.idsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解绑成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/roles": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的角色，只有超级管理员有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "创建角色",
                "parameters": [
                    {
                        "description": "角色信息",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.roleForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功，返回角色ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/roles/global": {
            "get": {
                "description": "获取系统中所有全局角色的列表，无权限限制",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取页面角色列表",
                "responses": {
                    "200": {
                        "description": "全局角色列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Role"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/roles/local": {
            "get": {
                "description": "获取系统中所有局部角色的列表，无权限限制",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取资源角色列表",
                "responses": {
                    "200": {
                        "description": "局部角色列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Role"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/root/teams/all": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员获取系统中所有团队的列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取所有团队列表(超管)",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "团队列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/password": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "用户修改自己的登录密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "修改个人密码",
                "parameters": [
                    {
                        "description": "密码修改信息",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.selfPasswordForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/perms/global": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前登录用户的全局权限操作列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "获取用户全局权限",
                "responses": {
                    "200": {
                        "description": "用户权限操作列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/profile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前登录用户的个人信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "获取个人信息",
                "responses": {
                    "200": {
                        "description": "个人信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新当前登录用户的个人信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "更新个人信息",
                "parameters": [
                    {
                        "description": "个人信息",
                        "name": "profile",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.selfProfileForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/token": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前用户的所有API Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "获取个人Token列表",
                "responses": {
                    "200": {
                        "description": "Token列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.UserToken"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "重置指定的API Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "重置个人Token",
                "parameters": [
                    {
                        "description": "Token信息",
                        "name": "token",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.selfTokenForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "重置后的Token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.UserToken"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "为当前用户创建新的API Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "创建个人Token",
                "responses": {
                    "200": {
                        "description": "新创建的Token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.UserToken"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/team/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定团队的详细信息，包括团队成员列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取团队详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "团队详情和成员列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新指定团队的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "更新团队信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "团队信息",
                        "name": "team",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定的团队",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "删除团队",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/team/{id}/users/bind": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "为团队添加管理员和普通成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "绑定团队成员",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户绑定信息",
                        "name": "users",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamUserBindForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/team/{id}/users/unbind": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "从团队中移除指定的成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "解绑团队成员",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户解绑信息",
                        "name": "users",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamUserUnbindForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解绑成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/teams": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的团队",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "创建团队",
                "parameters": [
                    {
                        "description": "团队信息",
                        "name": "team",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建的团队信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Team"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/teams/all": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统中所有团队的列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取所有团队列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "团队列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/teams/mine": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前用户所属的团队列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取我的团队列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "我的团队列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/tree": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取用户有权限的完整节点树，支持搜索功能",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点树管理"
                ],
                "summary": "获取完整节点树",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键词，支持节点名称和资源标识搜索",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点树列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/tree/orgs": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取用户有权限的节点树，只展示到组织级别",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点树管理"
                ],
                "summary": "获取组织级节点树",
                "responses": {
                    "200": {
                        "description": "组织级节点树",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/tree/projs": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取用户有权限的节点树，只展示到项目级别",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点树管理"
                ],
                "summary": "获取项目级节点树",
                "responses": {
                    "200": {
                        "description": "项目级节点树",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员删除指定用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}/disable": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员禁用指定用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "禁用用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "禁用成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}/enable": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员启用指定用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "启用用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "启用成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}/password": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员重置指定用户的密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "重置用户密码",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "新密码",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userPasswordForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码重置成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}/profile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员获取指定用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员更新指定用户的信息，支持更新临时账号的有效期\n用户类型(typ): 0-长期账号, 1-临时账号\n用户状态(status): 0-激活, 1-未激活, 2-锁定, 3-冻结, 4-注销\n临时账号需要设置active_begin和active_end字段",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userProfileForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/users": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    },
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统用户列表，支持分页和搜索\n获取系统用户列表，支持分页和搜索",
                "consumes": [
                    "application/json",
                    "application/json"
                ],
                "produces": [
                    "application/json",
                    "application/json"
                ],
                "tags": [
                    "用户管理",
                    "用户管理"
                ],
                "summary": "获取用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "查询条件",
                        "name": "conditions",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "组织",
                        "name": "org",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户ID列表，逗号分隔",
                        "name": "ids",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量，默认20",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码，默认1",
                        "name": "p",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "组织筛选",
                        "name": "org",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户ID列表，逗号分隔",
                        "name": "ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "list": {
                                                    "type": "array",
                                                    "items": {
                                                        "$ref": "#/definitions/models.User"
                                                    }
                                                },
                                                "total": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员创建新用户，支持创建长期账号和临时账号\n用户类型(typ): 0-长期账号, 1-临时账号\n用户状态(status): 0-激活, 1-未激活, 2-锁定, 3-冻结, 4-注销\n临时账号需要设置active_begin和active_end字段",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userProfileForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/users/invite": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "生成用户注册邀请Token，用于用户自助注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "生成用户邀请Token",
                "responses": {
                    "200": {
                        "description": "邀请Token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "使用邀请Token进行用户自助注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "通过邀请Token注册用户",
                "parameters": [
                    {
                        "description": "用户注册信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userInviteForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/container/sync": {
            "post": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "同步容器资源信息，用于容器管理系统数据同步",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "同步容器资源",
                "parameters": [
                    {
                        "description": "容器同步参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.containerSyncForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "同步成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/containers/bind": {
            "post": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "批量绑定容器资源到节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "绑定容器资源",
                "parameters": [
                    {
                        "description": "容器资源列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/http.v1ContainersRegisterItem"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/get-user-by-uuid": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "根据用户UUID获取用户详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "根据UUID获取用户(V1接口)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户UUID",
                        "name": "uuid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/get-users-by-uuids": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "根据用户UUID列表批量获取用户信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "批量根据UUID获取用户(V1接口)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户UUID列表，逗号分隔",
                        "name": "uuids",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.User"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/node-include-trash/{id}": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "获取节点信息，包括已删除的节点，主要用于补全信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "获取节点信息(包含回收站)",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "节点信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Node"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/node/{id}/projs": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "根据指定节点ID获取项目级别的节点树",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "根据节点ID获取项目树(V1接口)",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "项目节点树",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/node/{id}/resources": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "获取指定节点下的所有资源，不受用户权限限制，主要供内部服务调用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "获取节点下的所有资源(V1接口)",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "资源列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Resource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "节点不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/resources/register": {
            "post": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "第三方系统注册资源到RDB，支持批量注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "注册资源(V1接口)",
                "parameters": [
                    {
                        "description": "资源注册列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.ResourceRegisterItem"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/resources/unregister": {
            "post": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "第三方系统注销资源，从RDB中删除资源及其绑定关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "注销资源(V1接口)",
                "parameters": [
                    {
                        "description": "资源UUID列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注销成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/table/sync/role-global-user": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "获取所有全局角色与用户的绑定关系，用于数据同步",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "获取全局角色用户关系",
                "responses": {
                    "200": {
                        "description": "角色用户关系列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.RoleGlobalUser"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/tree": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "根据用户名和分类获取用户有权限的节点树",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "获取用户分类树(V1接口)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点分类",
                        "name": "cate",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "是否只返回指定分类",
                        "name": "onlyCate",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": -1,
                        "description": "父节点ID",
                        "name": "pid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户分类节点树",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/tree/projs": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "根据用户名获取用户有权限的项目级节点树",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "获取用户项目树(V1接口)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户项目节点树",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.Node"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/v1/rdb/users": {
            "get": {
                "security": [
                    {
                        "ServiceAuth": []
                    }
                ],
                "description": "获取用户列表，支持分页、搜索、组织筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "V1接口"
                ],
                "summary": "获取用户列表(V1接口)",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "组织筛选",
                        "name": "org",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户ID列表，逗号分隔",
                        "name": "ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "properties": {
                                                "list": {
                                                    "type": "array",
                                                    "items": {
                                                        "$ref": "#/definitions/models.User"
                                                    }
                                                },
                                                "total": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "config.Op": {
            "type": "object",
            "properties": {
                "cn": {
                    "type": "string"
                },
                "en": {
                    "type": "string"
                }
            }
        },
        "config.OpsGroup": {
            "type": "object",
            "properties": {
                "ops": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.Op"
                    }
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "config.OpsSystem": {
            "type": "object",
            "properties": {
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.OpsGroup"
                    }
                },
                "system": {
                    "type": "string"
                }
            }
        },
        "http.AccountBindingError": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "integer"
                },
                "error": {
                    "type": "string"
                },
                "resource_id": {
                    "type": "integer"
                }
            }
        },
        "http.AccountBindingRequest": {
            "type": "object",
            "required": [
                "account_ids",
                "resource_ids"
            ],
            "properties": {
                "account_ids": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                },
                "resource_ids": {
                    "description": "统一使用resource.id",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.AccountBindingResponse": {
            "type": "object",
            "properties": {
                "failed": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/http.AccountBindingError"
                    }
                },
                "success": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AccountResourceBinding"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.AccountCreateRequest": {
            "type": "object",
            "required": [
                "name",
                "resource_ids",
                "username"
            ],
            "properties": {
                "comment": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "on_invalid": {
                    "type": "string"
                },
                "passphrase": {
                    "type": "string"
                },
                "privileged": {
                    "type": "boolean"
                },
                "push_now": {
                    "type": "boolean"
                },
                "resource_ids": {
                    "description": "统一使用resource.id",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                },
                "secret": {
                    "type": "string"
                },
                "secret_reset": {
                    "type": "boolean"
                },
                "secret_type": {
                    "type": "string"
                },
                "su_from": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.AccountCreateResponse": {
            "type": "object",
            "properties": {
                "account": {
                    "$ref": "#/definitions/models.Account"
                },
                "results": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/http.AccountCreateResult"
                    }
                }
            }
        },
        "http.AccountCreateResult": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                },
                "resource_id": {
                    "description": "资源ID",
                    "type": "integer"
                },
                "state": {
                    "description": "success, error",
                    "type": "string"
                }
            }
        },
        "http.AccountListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Account"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.AccountStatistics": {
            "type": "object",
            "properties": {
                "active_accounts": {
                    "type": "integer"
                },
                "inactive_accounts": {
                    "type": "integer"
                },
                "manual_accounts": {
                    "type": "integer"
                },
                "template_accounts": {
                    "type": "integer"
                },
                "total_accounts": {
                    "type": "integer"
                },
                "total_assets": {
                    "type": "integer"
                },
                "total_bindings": {
                    "type": "integer"
                }
            }
        },
        "http.AccountTemplateApplyRequest": {
            "type": "object",
            "required": [
                "resource_ids",
                "template_id"
            ],
            "properties": {
                "comment": {
                    "description": "可选，覆盖模板中的备注",
                    "type": "string"
                },
                "resource_ids": {
                    "description": "统一使用resource.id",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                },
                "template_id": {
                    "type": "integer"
                }
            }
        },
        "http.AccountTemplateListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AccountTemplate"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.AccountUpdateRequest": {
            "type": "object",
            "properties": {
                "comment": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "on_invalid": {
                    "type": "string"
                },
                "passphrase": {
                    "type": "string"
                },
                "privileged": {
                    "type": "boolean"
                },
                "push_now": {
                    "type": "boolean"
                },
                "secret": {
                    "type": "string"
                },
                "secret_reset": {
                    "type": "boolean"
                },
                "secret_type": {
                    "type": "string"
                },
                "su_from": {
                    "type": "integer"
                }
            }
        },
        "http.ApiResponse": {
            "type": "object",
            "properties": {
                "dat": {},
                "err": {
                    "type": "string"
                }
            }
        },
        "http.LoginLogListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.LoginLog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.OperationLogListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OperationLog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.ResourceCountByCate": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "http.TeamDetailResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.User"
                    }
                },
                "team": {
                    "$ref": "#/definitions/models.Team"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.TeamListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Team"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.UserListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.User"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.WhiteListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.WhiteList"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.containerSyncForm": {
            "type": "object",
            "required": [
                "name",
                "type"
            ],
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/http.v1ContainersRegisterItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "http.createWhiteListInput": {
            "type": "object",
            "properties": {
                "endIp": {
                    "type": "string"
                },
                "endTime": {
                    "type": "integer"
                },
                "startIp": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                }
            }
        },
        "http.idsForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.loginInput": {
            "type": "object",
            "properties": {
                "args": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "password": {
                    "type": "string"
                },
                "remote_addr": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.nodeCatePostForm": {
            "type": "object",
            "properties": {
                "icon_color": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "http.nodeCatePutForm": {
            "type": "object",
            "properties": {
                "icon_color": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "http.nodeForm": {
            "type": "object",
            "properties": {
                "admin_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "cate": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "leaf": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "pid": {
                    "type": "integer"
                },
                "proxy": {
                    "type": "integer"
                }
            }
        },
        "http.resourceBindForm": {
            "type": "object",
            "properties": {
                "field": {
                    "type": "string"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "http.resourceNotePutForm": {
            "type": "object",
            "required": [
                "ids"
            ],
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "http.roleForm": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "operations": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "http.roleUnderNodeDelForm": {
            "type": "object",
            "properties": {
                "role_id": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.roleUnderNodeForm": {
            "type": "object",
            "properties": {
                "role_id": {
                    "type": "integer"
                },
                "usernames": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "http.selfPasswordForm": {
            "type": "object",
            "required": [
                "newpass",
                "oldpass",
                "username"
            ],
            "properties": {
                "newpass": {
                    "type": "string"
                },
                "oldpass": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.selfProfileForm": {
            "type": "object",
            "properties": {
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "im": {
                    "type": "string"
                },
                "intro": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "portrait": {
                    "type": "string"
                }
            }
        },
        "http.selfTokenForm": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "http.teamForm": {
            "type": "object",
            "properties": {
                "ident": {
                    "type": "string"
                },
                "mgmt": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "http.teamUserBindForm": {
            "type": "object",
            "properties": {
                "admin_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "member_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.teamUserUnbindForm": {
            "type": "object",
            "properties": {
                "user_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.updateWhiteListInput": {
            "type": "object",
            "properties": {
                "endIp": {
                    "type": "string"
                },
                "endTime": {
                    "type": "integer"
                },
                "startIp": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                }
            }
        },
        "http.userInviteForm": {
            "type": "object",
            "required": [
                "password",
                "token",
                "username"
            ],
            "properties": {
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "im": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.userPasswordForm": {
            "type": "object",
            "required": [
                "password"
            ],
            "properties": {
                "password": {
                    "type": "string"
                }
            }
        },
        "http.userProfileForm": {
            "type": "object",
            "properties": {
                "active_begin": {
                    "description": "临时账号开始时间（Unix时间戳）",
                    "type": "integer"
                },
                "active_end": {
                    "description": "临时账号结束时间（Unix时间戳）",
                    "type": "integer"
                },
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "im": {
                    "type": "string"
                },
                "is_root": {
                    "type": "integer"
                },
                "leader_id": {
                    "type": "integer"
                },
                "organization": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "status": {
                    "description": "用户状态：0-激活，1-未激活，2-锁定，3-冻结，4-注销",
                    "type": "integer"
                },
                "typ": {
                    "description": "用户类型：0-长期账号，1-临时账号",
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.v1ContainersRegisterItem": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "extend": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "labels": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "nid": {
                    "type": "integer"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.Account": {
            "type": "object",
            "properties": {
                "change_secret_status": {
                    "type": "string"
                },
                "comment": {
                    "type": "string"
                },
                "connectivity": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "date_change_secret": {
                    "type": "string"
                },
                "date_last_login": {
                    "type": "string"
                },
                "date_verified": {
                    "type": "string"
                },
                "has_secret": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "on_invalid": {
                    "type": "string"
                },
                "passphrase": {
                    "type": "string"
                },
                "privileged": {
                    "type": "boolean"
                },
                "push_now": {
                    "type": "boolean"
                },
                "secret": {
                    "type": "string"
                },
                "secret_reset": {
                    "type": "boolean"
                },
                "secret_type": {
                    "type": "string"
                },
                "source": {
                    "type": "string"
                },
                "source_id": {
                    "type": "string"
                },
                "su_from": {
                    "type": "integer"
                },
                "template_id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "version": {
                    "type": "integer"
                }
            }
        },
        "models.AccountResourceBinding": {
            "type": "object",
            "properties": {
                "account_id": {
                    "description": "账号ID",
                    "type": "integer"
                },
                "binding_type": {
                    "description": "绑定类型：manual, auto, template",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "creator": {
                    "description": "创建者",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "description": "绑定是否激活",
                    "type": "boolean"
                },
                "node_id": {
                    "description": "节点ID（预留）",
                    "type": "integer"
                },
                "resource_id": {
                    "description": "资源ID",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "models.AccountTemplate": {
            "type": "object",
            "properties": {
                "auto_push": {
                    "type": "boolean"
                },
                "comment": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "passphrase": {
                    "type": "string"
                },
                "privileged": {
                    "type": "boolean"
                },
                "secret": {
                    "type": "string"
                },
                "secret_strategy": {
                    "type": "string"
                },
                "secret_type": {
                    "type": "string"
                },
                "su_from": {
                    "description": "改为int64，引用账号ID",
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.AuthConfig": {
            "type": "object",
            "properties": {
                "lockTime": {
                    "type": "integer"
                },
                "maxConnIdleTime": {
                    "type": "integer"
                },
                "maxNumErr": {
                    "type": "integer"
                },
                "maxSessionNumber": {
                    "type": "integer"
                },
                "pwdExpiresIn": {
                    "type": "integer"
                },
                "pwdHistorySize": {
                    "type": "integer"
                },
                "pwdMinLenght": {
                    "type": "integer"
                },
                "pwdMustInclude": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "pwdMustIncludeFlag": {
                    "type": "integer"
                }
            }
        },
        "models.LoginLog": {
            "type": "object",
            "properties": {
                "client": {
                    "type": "string"
                },
                "clock": {
                    "type": "integer"
                },
                "err": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "loginout": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.Node": {
            "type": "object",
            "properties": {
                "admins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.User"
                    }
                },
                "cate": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "icon_char": {
                    "type": "string"
                },
                "icon_color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "ident": {
                    "type": "string"
                },
                "last_updated": {
                    "type": "string"
                },
                "leaf": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "pid": {
                    "type": "integer"
                },
                "proxy": {
                    "type": "integer"
                }
            }
        },
        "models.NodeCate": {
            "type": "object",
            "properties": {
                "icon_color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "ident": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "protected": {
                    "type": "integer"
                }
            }
        },
        "models.NodeCateField": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "field_extra": {
                    "type": "string"
                },
                "field_ident": {
                    "type": "string"
                },
                "field_name": {
                    "type": "string"
                },
                "field_required": {
                    "type": "integer"
                },
                "field_type": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "last_updated": {
                    "type": "string"
                }
            }
        },
        "models.NodeFieldValue": {
            "type": "object",
            "properties": {
                "field_ident": {
                    "type": "string"
                },
                "field_value": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "node_id": {
                    "type": "integer"
                }
            }
        },
        "models.OperationLog": {
            "type": "object",
            "properties": {
                "clock": {
                    "type": "integer"
                },
                "detail": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "res_cl": {
                    "type": "string"
                },
                "res_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.Resource": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "extend": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "ident": {
                    "type": "string"
                },
                "labels": {
                    "type": "string"
                },
                "last_updated": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "source_id": {
                    "type": "integer"
                },
                "source_type": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.ResourceBinding": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "ident": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "nodes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Node"
                    }
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.ResourceRegisterItem": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "extend": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "labels": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "nid": {
                    "type": "integer"
                },
                "source_id": {
                    "type": "integer"
                },
                "source_type": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.Role": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "models.RoleGlobalUser": {
            "type": "object",
            "properties": {
                "role_id": {
                    "type": "integer"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "models.Team": {
            "type": "object",
            "properties": {
                "creator": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "ident": {
                    "type": "string"
                },
                "last_updated": {
                    "type": "string"
                },
                "mgmt": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "properties": {
                "active_begin": {
                    "type": "integer"
                },
                "active_end": {
                    "type": "integer"
                },
                "create_at": {
                    "type": "string"
                },
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "im": {
                    "type": "string"
                },
                "intro": {
                    "type": "string"
                },
                "is_root": {
                    "type": "integer"
                },
                "leader_id": {
                    "type": "integer"
                },
                "leader_name": {
                    "type": "string"
                },
                "locked_at": {
                    "type": "integer"
                },
                "logged_at": {
                    "type": "integer"
                },
                "login_err_num": {
                    "type": "integer"
                },
                "organization": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "portrait": {
                    "type": "string"
                },
                "pwd_expires_at": {
                    "type": "integer"
                },
                "pwd_updated_at": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.UserToken": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.WhiteList": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "integer"
                },
                "creator": {
                    "type": "string"
                },
                "endIp": {
                    "type": "string"
                },
                "endTime": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "startIp": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                },
                "updateAt": {
                    "type": "integer"
                },
                "updater": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "User token for API authentication",
            "type": "apiKey",
            "name": "X-User-Token",
            "in": "header"
        },
        "CookieAuth": {
            "description": "Session cookie for web authentication",
            "type": "apiKey",
            "name": "ecmc-sid",
            "in": "cookie"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "Arboris RDB API",
	Description:      "Arboris RDB 模块 API 文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
