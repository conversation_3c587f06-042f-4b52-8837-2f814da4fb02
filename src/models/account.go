package models

import (
	"fmt"
	"time"
)

// Account JumpServer账号
type Account struct {
	Id                 int64     `json:"id"`
	Name               string    `json:"name"`
	Username           string    `json:"username"`
	SecretType         string    `json:"secret_type"`
	Secret             string    `json:"secret,omitempty"`
	Passphrase         string    `json:"passphrase,omitempty"`
	SuFrom             int64     `json:"su_from"`
	TemplateId         int64     `json:"template_id"`
	Version            int       `json:"version"`
	Source             string    `json:"source"`
	SourceId           string    `json:"source_id"`
	OnInvalid          string    `json:"on_invalid"`
	Privileged         bool      `json:"privileged"`
	IsActive           bool      `json:"is_active"`
	SecretReset        bool      `json:"secret_reset"`
	PushNow            bool      `json:"push_now"`
	Connectivity       string    `json:"connectivity"`
	ChangeSecretStatus string    `json:"change_secret_status"`
	HasSecret          bool      `json:"has_secret"`
	DateLastLogin      time.Time `json:"date_last_login" xorm:"<-"`
	DateVerified       time.Time `json:"date_verified" xorm:"<-"`
	DateChangeSecret   time.Time `json:"date_change_secret" xorm:"<-"`
	Comment            string    `json:"comment"`
	CreatedAt          time.Time `json:"created_at" xorm:"<-"`
	UpdatedAt          time.Time `json:"updated_at" xorm:"<-"`
	Creator            string    `json:"creator"`
}

// TableName 表名
func (a *Account) TableName() string {
	return "account"
}

// BeforeInsert 插入前处理
func (a *Account) BeforeInsert() {
	if a.SecretType == "" {
		a.SecretType = "password"
	}
	if a.Source == "" {
		a.Source = "local"
	}
	if a.OnInvalid == "" {
		a.OnInvalid = "error"
	}
	if a.Connectivity == "" {
		a.Connectivity = "-"
	}
}

// Save 保存账号
func (a *Account) Save() error {
	a.BeforeInsert()
	_, err := DB["rdb"].InsertOne(a)
	return err
}

// Update 更新账号
func (a *Account) Update(cols ...string) error {
	_, err := DB["rdb"].Where("id=?", a.Id).Cols(cols...).Update(a)
	return err
}

// Delete 删除账号
func (a *Account) Delete() error {
	_, err := DB["rdb"].Where("id=?", a.Id).Delete(a)
	return err
}

// AccountGet 根据条件获取账号
func AccountGet(where string, args ...interface{}) (*Account, error) {
	var obj Account
	has, err := DB["rdb"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// AccountGetById 根据ID获取账号
func AccountGetById(id int64) (*Account, error) {
	return AccountGet("id=?", id)
}

// AccountGets 获取账号列表
func AccountGets(where string, args ...interface{}) ([]Account, error) {
	var objs []Account
	session := DB["rdb"].OrderBy("created_at DESC")
	if where != "" {
		session = session.Where(where, args...)
	}
	err := session.Find(&objs)
	return objs, err
}

// AccountTotal 获取账号总数
func AccountTotal(where string, args ...interface{}) (int64, error) {
	if where != "" {
		return DB["rdb"].Where(where, args...).Count(new(Account))
	}
	return DB["rdb"].Count(new(Account))
}

// AccountGetsWithPaging 分页获取账号列表
func AccountGetsWithPaging(where string, limit, offset int, args ...interface{}) ([]Account, error) {
	var objs []Account
	session := DB["rdb"].OrderBy("created_at DESC").Limit(limit, offset)
	if where != "" {
		session = session.Where(where, args...)
	}
	err := session.Find(&objs)
	return objs, err
}

// Validate 验证账号数据
func (a *Account) Validate() error {
	if a.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}
	if a.SecretType == "" {
		a.SecretType = "password"
	}
	if a.Source == "" {
		a.Source = "local"
	}
	if a.OnInvalid == "" {
		a.OnInvalid = "error"
	}

	// 验证密文类型
	validSecretTypes := map[string]bool{
		"password":   true,
		"ssh_key":    true,
		"access_key": true,
		"token":      true,
		"api_key":    true,
	}
	if !validSecretTypes[a.SecretType] {
		return fmt.Errorf("无效的密文类型: %s", a.SecretType)
	}

	// 验证来源
	validSources := map[string]bool{
		"local":     true,
		"collected": true,
		"template":  true,
	}
	if !validSources[a.Source] {
		return fmt.Errorf("无效的来源: %s", a.Source)
	}

	// 验证账号存在策略
	validOnInvalid := map[string]bool{
		"skip":   true,
		"update": true,
		"error":  true,
	}
	if !validOnInvalid[a.OnInvalid] {
		return fmt.Errorf("无效的账号存在策略: %s", a.OnInvalid)
	}

	return nil
}

// GetTemplate 获取关联的账号模板
func (a *Account) GetTemplate() (*AccountTemplate, error) {
	if a.TemplateId == 0 {
		return nil, nil
	}
	return AccountTemplateGetById(a.TemplateId)
}

// GetBoundResources 获取账号绑定的所有资源
func (a *Account) GetBoundResources() ([]Resource, error) {
	bindings, err := AccountResourceBindingGets("account_id=? AND is_active=1", a.Id)
	if err != nil {
		return nil, err
	}

	if len(bindings) == 0 {
		return []Resource{}, nil
	}

	var resourceIds []int64
	for _, binding := range bindings {
		resourceIds = append(resourceIds, binding.ResourceId)
	}

	return ResourceGets("id IN (?)", resourceIds)
}

// AccountResourceBinding 账号与资源绑定关系
type AccountResourceBinding struct {
	Id          int64     `json:"id"`
	AccountId   int64     `json:"account_id"`           // 账号ID
	ResourceId  int64     `json:"resource_id"`          // 资源ID
	NodeId      int64     `json:"node_id"`              // 节点ID（预留）
	BindingType string    `json:"binding_type"`         // 绑定类型：manual, auto, template
	IsActive    bool      `json:"is_active"`            // 绑定是否激活
	CreatedAt   time.Time `json:"created_at" xorm:"<-"` // 创建时间
	UpdatedAt   time.Time `json:"updated_at" xorm:"<-"` // 更新时间
	Creator     string    `json:"creator"`              // 创建者
}

// TableName 表名
func (b *AccountResourceBinding) TableName() string {
	return "account_resource_binding"
}

// BeforeInsert 插入前处理
func (b *AccountResourceBinding) BeforeInsert() {
	if b.BindingType == "" {
		b.BindingType = "manual"
	}
}

// Save 保存绑定关系
func (b *AccountResourceBinding) Save() error {
	b.BeforeInsert()
	_, err := DB["rdb"].InsertOne(b)
	return err
}

// Update 更新绑定关系
func (b *AccountResourceBinding) Update(cols ...string) error {
	_, err := DB["rdb"].Where("id=?", b.Id).Cols(cols...).Update(b)
	return err
}

// Delete 删除绑定关系
func (b *AccountResourceBinding) Delete() error {
	_, err := DB["rdb"].Where("id=?", b.Id).Delete(b)
	return err
}

// AccountResourceBindingGet 根据条件获取绑定关系
func AccountResourceBindingGet(where string, args ...interface{}) (*AccountResourceBinding, error) {
	var obj AccountResourceBinding
	has, err := DB["rdb"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// AccountResourceBindingGets 获取绑定关系列表
func AccountResourceBindingGets(where string, args ...interface{}) ([]AccountResourceBinding, error) {
	var objs []AccountResourceBinding
	session := DB["rdb"].OrderBy("created_at DESC")
	if where != "" {
		session = session.Where(where, args...)
	}
	err := session.Find(&objs)
	return objs, err
}

// GetAccountBindings 获取账号的绑定关系
func (a *Account) GetAccountBindings() ([]AccountResourceBinding, error) {
	return AccountResourceBindingGets("account_id=?", a.Id)
}
