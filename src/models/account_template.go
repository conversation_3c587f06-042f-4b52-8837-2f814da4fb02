package models

import (
	"fmt"
	"time"
)

// AccountTemplate 账号模板
type AccountTemplate struct {
	Id             int64     `json:"id"`
	Name           string    `json:"name"`
	Username       string    `json:"username"`
	SecretType     string    `json:"secret_type"`
	Secret         string    `json:"secret,omitempty"`
	Passphrase     string    `json:"passphrase,omitempty"`
	SecretStrategy string    `json:"secret_strategy"`
	SuFrom         int64     `json:"su_from"` // 改为int64，引用账号ID
	Privileged     bool      `json:"privileged"`
	IsActive       bool      `json:"is_active"`
	AutoPush       bool      `json:"auto_push"`
	Comment        string    `json:"comment"`
	CreatedAt      time.Time `json:"created_at" xorm:"<-"`
	UpdatedAt      time.Time `json:"updated_at" xorm:"<-"`
	Creator        string    `json:"creator"`
}

// TableName 表名
func (t *AccountTemplate) TableName() string {
	return "account_template"
}

// BeforeInsert 插入前处理
func (t *AccountTemplate) BeforeInsert() {
	if t.SecretType == "" {
		t.SecretType = "password"
	}
	if t.SecretStrategy == "" {
		t.SecretStrategy = "specific"
	}
}

// Save 保存账号模板
func (t *AccountTemplate) Save() error {
	t.BeforeInsert()
	_, err := DB["rdb"].InsertOne(t)
	return err
}

// Update 更新账号模板
func (t *AccountTemplate) Update(cols ...string) error {
	_, err := DB["rdb"].Where("id=?", t.Id).Cols(cols...).Update(t)
	return err
}

// Delete 删除账号模板
func (t *AccountTemplate) Delete() error {
	_, err := DB["rdb"].Where("id=?", t.Id).Delete(t)
	return err
}

// AccountTemplateGet 根据条件获取账号模板
func AccountTemplateGet(where string, args ...interface{}) (*AccountTemplate, error) {
	var obj AccountTemplate
	has, err := DB["rdb"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// AccountTemplateGetById 根据ID获取账号模板
func AccountTemplateGetById(id int64) (*AccountTemplate, error) {
	return AccountTemplateGet("id=?", id)
}

// AccountTemplateGetByName 根据名称获取账号模板
func AccountTemplateGetByName(name string) (*AccountTemplate, error) {
	return AccountTemplateGet("name=?", name)
}

// AccountTemplateGets 获取账号模板列表
func AccountTemplateGets(where string, args ...interface{}) ([]AccountTemplate, error) {
	var objs []AccountTemplate
	session := DB["rdb"].OrderBy("created_at DESC")
	if where != "" {
		session = session.Where(where, args...)
	}
	err := session.Find(&objs)
	return objs, err
}

// AccountTemplateTotal 获取账号模板总数
func AccountTemplateTotal(where string, args ...interface{}) (int64, error) {
	if where != "" {
		return DB["rdb"].Where(where, args...).Count(new(AccountTemplate))
	}
	return DB["rdb"].Count(new(AccountTemplate))
}

// AccountTemplateGetsWithPaging 分页获取账号模板列表
func AccountTemplateGetsWithPaging(where string, limit, offset int, args ...interface{}) ([]AccountTemplate, error) {
	var objs []AccountTemplate
	session := DB["rdb"].OrderBy("created_at DESC").Limit(limit, offset)
	if where != "" {
		session = session.Where(where, args...)
	}
	err := session.Find(&objs)
	return objs, err
}

// Validate 验证账号模板数据
func (t *AccountTemplate) Validate() error {
	if t.Name == "" {
		return fmt.Errorf("账号模板名称不能为空")
	}
	if t.SecretType == "" {
		t.SecretType = "password"
	}
	if t.SecretStrategy == "" {
		t.SecretStrategy = "specific"
	}

	// 验证密文类型
	validSecretTypes := map[string]bool{
		"password":   true,
		"ssh_key":    true,
		"access_key": true,
		"token":      true,
		"api_key":    true,
	}
	if !validSecretTypes[t.SecretType] {
		return fmt.Errorf("无效的密文类型: %s", t.SecretType)
	}

	// 验证密文策略
	validStrategies := map[string]bool{
		"specific": true,
		"random":   true,
	}
	if !validStrategies[t.SecretStrategy] {
		return fmt.Errorf("无效的密文策略: %s", t.SecretStrategy)
	}

	return nil
}
