package sync

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/modules/jumpserver-sync/mapper"
)

// Handler 同步处理器
type Handler struct {
	jsClient *jumpserver.Client
	mapper   *mapper.Mapper
	config   *config.ConfigT
	db       *sql.DB
}

// NewHandler 创建新的同步处理器
func NewHandler(cfg *config.ConfigT) (*Handler, error) {
	// 创建JumpServer客户端
	jsClient := jumpserver.NewClient(
		cfg.JumpServer.BaseURL,
		cfg.JumpServer.Username,
		cfg.JumpServer.Password,
		cfg.JumpServer.Token,
		cfg.JumpServer.Organization,
		cfg.JumpServer.Timeout,
	)

	// 测试连接
	if err := jsClient.TestConnection(); err != nil {
		return nil, fmt.Errorf("failed to connect to JumpServer: %v", err)
	}

	// 获取数据库连接
	db := models.DB["rdb"]
	if db == nil {
		return nil, fmt.Errorf("RDB database connection not initialized")
	}

	handler := &Handler{
		jsClient: jsClient,
		mapper:   mapper.NewMapper(cfg),
		config:   cfg,
		db:       db.DB().DB, // 获取底层的*sql.DB
	}

	return handler, nil
}

// HandleEvent 处理事件
func (h *Handler) HandleEvent(event *events.Event) error {
	logger.Infof("Processing event: event_id=%s, event_type=%s, source=%s",
		event.ID, event.Type, event.Source)

	switch event.Type {
	case events.EventNodeCreate:
		return h.handleNodeCreate(event)
	case events.EventNodeUpdate:
		return h.handleNodeUpdate(event)
	case events.EventNodeDelete:
		return h.handleNodeDelete(event)
	case events.EventHostCreate:
		return h.handleHostCreate(event)
	case events.EventHostUpdate:
		return h.handleHostUpdate(event)
	case events.EventHostDelete:
		return h.handleHostDelete(event)
	case events.EventResourceCreate:
		return h.handleResourceCreate(event)
	case events.EventResourceUpdate:
		return h.handleResourceUpdate(event)
	case events.EventResourceDelete:
		return h.handleResourceDelete(event)
	case events.EventResourceBind:
		return h.handleResourceBind(event)
	case events.EventResourceUnbind:
		return h.handleResourceUnbind(event)
	// 用户管理事件
	case events.EventUserCreate:
		return h.handleUserCreate(event)
	case events.EventUserUpdate:
		return h.handleUserUpdate(event)
	case events.EventUserDelete:
		return h.handleUserDelete(event)
	case events.EventUserChangePassword:
		return h.handleUserChangePassword(event)
	case events.EventUserEnable:
		return h.handleUserEnable(event)
	case events.EventUserDisable:
		return h.handleUserDisable(event)
	// 用户组事件
	case events.EventTeamCreate:
		return h.handleTeamCreate(event)
	case events.EventTeamUpdate:
		return h.handleTeamUpdate(event)
	case events.EventTeamDelete:
		return h.handleTeamDelete(event)
	case events.EventTeamAddMember:
		return h.handleTeamAddUser(event)
	case events.EventTeamRemoveMember:
		return h.handleTeamRemoveUser(event)
	// 权限事件
	case events.EventPermissionGrant:
		return h.handlePermissionGrant(event)
	case events.EventPermissionRevoke:
		return h.handlePermissionRevoke(event)
	// 账号模板事件
	case events.EventAccountTemplateCreate:
		return h.handleAccountTemplateCreate(event)
	case events.EventAccountTemplateUpdate:
		return h.handleAccountTemplateUpdate(event)
	case events.EventAccountTemplateDelete:
		return h.handleAccountTemplateDelete(event)
	// 账号事件
	case events.EventAccountCreate:
		return h.handleAccountCreate(event)
	case events.EventAccountUpdate:
		return h.handleAccountUpdate(event)
	case events.EventAccountDelete:
		return h.handleAccountDelete(event)
	// 账号绑定事件
	case events.EventAccountBind:
		return h.handleAccountBind(event)
	case events.EventAccountUnbind:
		return h.handleAccountUnbind(event)
	default:
		logger.Infof("Unknown event type: %s", event.Type)
		return nil
	}
}

// handleNodeCreate 处理节点创建事件
func (h *Handler) handleNodeCreate(event *events.Event) error {
	nodeData, err := h.parseNodeEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncNode(nodeData) {
		logger.Infof("Node filtered out by sync rules: node_path=%s", nodeData.Path)
		return nil
	}

	// 映射为JumpServer节点
	jsNode, err := h.mapper.MapNodeToJumpServer(nodeData)
	if err != nil {
		return fmt.Errorf("failed to map node: %v", err)
	}

	// 检查节点是否已存在 - 使用父节点ID和节点名称来确保唯一性
	// 这样可以避免不同层级的同名节点冲突
	var parentID string
	if nodeData.GetNodePID() != 0 {
		// 获取父节点信息
		parentNodeData, err := h.getNodeByID(nodeData.GetNodePID())
		if err != nil {
			return fmt.Errorf("failed to get parent node: %v", err)
		}

		logger.Infof("Creating node: current_node=%s (path=%s), parent_node=%s (path=%s)",
			nodeData.Name, nodeData.Path, parentNodeData.Name, parentNodeData.Path)

		// 递归确保父节点存在 - 使用层级查找而不是全局查找
		parentID, err = h.ensureParentNodeExists(parentNodeData)
		if err != nil {
			return fmt.Errorf("failed to ensure parent node exists: %v", err)
		}

		logger.Infof("Parent node resolved: parent_id=%s, parent_name=%s", parentID, parentNodeData.Name)
	}

	existingNode, err := h.jsClient.GetNodeByParentAndValue(parentID, jsNode.Value)
	if err != nil {
		return fmt.Errorf("failed to check existing node: %v", err)
	}

	if existingNode != nil {
		logger.Infof("Node already exists under parent, skipping creation: parent_id=%s, value=%s, node_id=%s",
			parentID, jsNode.Value, existingNode.ID)
		return nil
	}

	// 设置父节点ID（已在上面的检查中处理）
	jsNode.Parent = parentID

	// 创建节点
	createdNode, err := h.jsClient.CreateNode(jsNode)
	if err != nil {
		return fmt.Errorf("failed to create node in JumpServer: %v", err)
	}

	logger.Infof("Node created successfully: rdb_path=%s, js_key=%s, js_id=%s",
		nodeData.Path, createdNode.Key, createdNode.ID)

	return nil
}

// handleHostCreate 处理主机创建事件
func (h *Handler) handleHostCreate(event *events.Event) error {
	hostData, err := h.parseHostEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncHost(hostData) {
		logger.Infof("Host filtered out by sync rules: host_ip=%s", hostData.IP)
		return nil
	}

	// 映射为JumpServer资产
	jsAsset, err := h.mapper.MapHostToJumpServer(hostData)
	if err != nil {
		return fmt.Errorf("failed to map host: %v", err)
	}

	// 检查资产是否已存在
	existingAsset, err := h.jsClient.GetAssetByIP(jsAsset.IP)
	if err != nil {
		return fmt.Errorf("failed to check existing asset: %v", err)
	}

	if existingAsset != nil {
		logger.Infof("Asset already exists, skipping creation: ip=%s", jsAsset.IP)
		return nil
	}

	// 创建资产
	createdAsset, err := h.jsClient.CreateAsset(jsAsset)
	if err != nil {
		return fmt.Errorf("failed to create asset in JumpServer: %v", err)
	}

	logger.Infof("Asset created successfully: host_ip=%s, js_hostname=%s, js_id=%s",
		hostData.IP, createdAsset.Hostname, createdAsset.ID)

	return nil
}

// parseNodeEventData 解析节点事件数据
func (h *Handler) parseNodeEventData(event *events.Event) (*events.NodeEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var nodeData events.NodeEventData
	if err := json.Unmarshal(data, &nodeData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal node event data: %v", err)
	}

	return &nodeData, nil
}

// parseHostEventData 解析主机事件数据
func (h *Handler) parseHostEventData(event *events.Event) (*events.HostEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var hostData events.HostEventData
	if err := json.Unmarshal(data, &hostData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal host event data: %v", err)
	}

	return &hostData, nil
}

// parseResourceEventData 解析资源事件数据
func (h *Handler) parseResourceEventData(event *events.Event) (*events.ResourceEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var resourceData events.ResourceEventData
	if err := json.Unmarshal(data, &resourceData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal resource event data: %v", err)
	}

	return &resourceData, nil
}

// parseResourceBindEventData 解析资源绑定事件数据
func (h *Handler) parseResourceBindEventData(event *events.Event) (*events.ResourceBindEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var bindData events.ResourceBindEventData
	if err := json.Unmarshal(data, &bindData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal resource bind event data: %v", err)
	}

	return &bindData, nil
}

// handleNodeUpdate 处理节点更新事件
func (h *Handler) handleNodeUpdate(event *events.Event) error {
	nodeData, err := h.parseNodeEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("=== NODE UPDATE DEBUG ===")
	logger.Infof("Node update event: rdb_id=%d, rdb_path=%s, current_name=%s, original_name=%s",
		nodeData.GetNodeID(), nodeData.Path, nodeData.Name, nodeData.OriginalName)

	// 检查是否应该同步
	if !h.mapper.ShouldSyncNode(nodeData) {
		logger.Infof("Node filtered out by sync rules: node_path=%s", nodeData.Path)
		return nil
	}

	// 映射为JumpServer节点
	jsNode, err := h.mapper.MapNodeToJumpServer(nodeData)
	if err != nil {
		return fmt.Errorf("failed to map node: %v", err)
	}

	logger.Infof("Mapped JumpServer node: key=%s, value=%s, full_path=%s",
		jsNode.Key, jsNode.Value, jsNode.FullPath)

	// 获取父节点ID用于查找
	var parentID string
	if nodeData.GetNodePID() != 0 {
		parentNodeData, err := h.getNodeByID(nodeData.GetNodePID())
		if err != nil {
			return fmt.Errorf("failed to get parent node: %v", err)
		}

		logger.Infof("Parent node info: rdb_id=%d, rdb_path=%s, name=%s",
			parentNodeData.ID, parentNodeData.Path, parentNodeData.Name)

		// 递归确保父节点存在并获取正确的父节点ID
		parentID, err = h.ensureParentNodeExists(parentNodeData)
		if err != nil {
			return fmt.Errorf("failed to ensure parent node exists: %v", err)
		}

		logger.Infof("Parent node resolved: parent_id=%s", parentID)
	} else {
		logger.Infof("Node is root level, no parent needed")
	}

	// 确定要查找的节点名称：优先使用原始名称，如果没有则使用当前名称
	searchName := nodeData.OriginalName
	if searchName == "" {
		searchName = nodeData.Name
		logger.Infof("No original name provided, using current name for search")
	} else {
		logger.Infof("Using original name for search: %s", searchName)
	}

	// 通过父节点ID和节点原始名称查找节点，避免同名节点冲突
	logger.Infof("Searching for existing node by parent and name: parent_id=%s, search_name=%s, current_name=%s",
		parentID, searchName, nodeData.Name)
	existingNode, err := h.jsClient.GetNodeByParentAndValue(parentID, searchName)
	if err != nil {
		logger.Errorf("Failed to search node by parent and value: %v", err)
		return fmt.Errorf("failed to search node by parent and value: %v", err)
	}

	if existingNode != nil {
		logger.Infof("Found existing node: js_id=%s, js_value=%s, js_key=%s",
			existingNode.ID, existingNode.Value, existingNode.Key)
	} else {
		logger.Infof("No existing node found with search criteria")

		// 尝试其他查找方式作为调试
		logger.Infof("DEBUG: Trying alternative search methods...")

		// 尝试用当前名称查找（如果原始名称不同的话）
		if searchName != nodeData.Name {
			altNode, altErr := h.jsClient.GetNodeByParentAndValue(parentID, nodeData.Name)
			if altErr == nil && altNode != nil {
				logger.Infof("DEBUG: Found node using current name: js_id=%s, js_value=%s",
					altNode.ID, altNode.Value)
			} else {
				logger.Infof("DEBUG: No node found using current name either")
			}
		}

		// 列出父节点下的所有子节点进行调试
		if parentID != "" {
			children, childErr := h.jsClient.GetNodeChildren(parentID)
			if childErr == nil {
				logger.Infof("DEBUG: Parent node has %d children:", len(children))
				for i, child := range children {
					logger.Infof("DEBUG:   Child %d: id=%s, value=%s, key=%s",
						i+1, child.ID, child.Value, child.Key)
				}
			} else {
				logger.Infof("DEBUG: Failed to get parent children: %v", childErr)
			}
		}
	}

	if existingNode == nil {
		// 节点不存在，创建新节点
		logger.Infof("Node not found in JumpServer, creating new node: search_name=%s, current_name=%s, path=%s",
			searchName, nodeData.Name, jsNode.FullPath)
		return h.handleNodeCreate(event)
	}

	logger.Infof("Found existing node for update: js_id=%s, js_key=%s, js_value=%s, js_path=%s",
		existingNode.ID, existingNode.Key, existingNode.Value, existingNode.FullPath)
	logger.Infof("Update details: rdb_id=%d, rdb_path=%s, old_name=%s, new_name=%s",
		nodeData.GetNodeID(), nodeData.Path, searchName, nodeData.Name)

	// 重新映射节点数据用于更新
	jsNodeForUpdate, err := h.mapper.MapNodeToJumpServer(nodeData)
	if err != nil {
		return fmt.Errorf("failed to map node for update: %v", err)
	}

	// 更新节点
	updatedNode, err := h.jsClient.UpdateNode(existingNode.ID, jsNodeForUpdate)
	if err != nil {
		return fmt.Errorf("failed to update node in JumpServer: %v", err)
	}

	logger.Infof("Node updated successfully: rdb_path=%s, js_key=%s, js_id=%s",
		nodeData.Path, updatedNode.Key, updatedNode.ID)

	return nil
}

// handleNodeDelete 处理节点删除事件
func (h *Handler) handleNodeDelete(event *events.Event) error {
	nodeData, err := h.parseNodeEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("=== NODE DELETE DEBUG ===")
	logger.Infof("Node delete event: rdb_id=%d, rdb_path=%s, name=%s",
		nodeData.GetNodeID(), nodeData.Path, nodeData.Name)

	// 获取父节点ID用于精确查找
	var parentID string
	if nodeData.GetNodePID() != 0 {
		parentNodeData, err := h.getNodeByID(nodeData.GetNodePID())
		if err != nil {
			return fmt.Errorf("failed to get parent node: %v", err)
		}

		logger.Infof("Parent node info: rdb_id=%d, rdb_path=%s, name=%s",
			parentNodeData.ID, parentNodeData.Path, parentNodeData.Name)

		// 递归确保父节点存在并获取正确的父节点ID
		parentID, err = h.ensureParentNodeExists(parentNodeData)
		if err != nil {
			logger.Errorf("Failed to find parent node, but continuing with deletion: %v", err)
			// 不返回错误，继续尝试删除
		} else {
			logger.Infof("Parent node resolved: parent_id=%s", parentID)
		}
	} else {
		logger.Infof("Node is root level, no parent needed")
	}

	// 通过父节点ID和节点名称精确查找要删除的节点
	logger.Infof("Searching for node to delete: parent_id=%s, node_name=%s", parentID, nodeData.Name)
	existingNode, err := h.jsClient.GetNodeByParentAndValue(parentID, nodeData.Name)
	if err != nil {
		return fmt.Errorf("failed to find node for deletion: %v", err)
	}

	if existingNode == nil {
		logger.Infof("Node not found in JumpServer, skipping deletion: node_path=%s", nodeData.Path)

		// 尝试通过Meta信息查找节点
		logger.Infof("Attempting to find node by Meta information...")
		if parentID != "" {
			children, childErr := h.jsClient.GetNodeChildren(parentID)
			if childErr == nil {
				logger.Infof("Parent node has %d children, checking for Meta matches:", len(children))

				targetRdbID := fmt.Sprintf("%d", nodeData.GetNodeID())
				for _, child := range children {
					logger.Infof("  Child: ID=%s, Value=%s, Meta=%+v", child.ID, child.Value, child.Meta)

					// 通过 rdb_id 匹配
					if childRdbID, exists := child.Meta["rdb_id"]; exists && childRdbID == targetRdbID {
						logger.Infof("✓ Found node to delete by RDB ID match: ID=%s, Value=%s",
							child.ID, child.Value)
						existingNode = &child
						break
					}
				}
			}
		}

		if existingNode == nil {
			return nil
		}
	}

	logger.Infof("Found node to delete: js_id=%s, js_key=%s, js_value=%s",
		existingNode.ID, existingNode.Key, existingNode.Value)

	// 删除节点
	if err := h.jsClient.DeleteNode(existingNode.ID); err != nil {
		return fmt.Errorf("failed to delete node from JumpServer: %v", err)
	}

	logger.Infof("Node deleted successfully: rdb_path=%s, js_key=%s, js_id=%s",
		nodeData.Path, existingNode.Key, existingNode.ID)

	return nil
}

// handleHostUpdate 处理主机更新事件
func (h *Handler) handleHostUpdate(event *events.Event) error {
	hostData, err := h.parseHostEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncHost(hostData) {
		logger.Infof("Host filtered out by sync rules: host_ip=%s", hostData.IP)
		return nil
	}

	// 查找现有资产
	existingAsset, err := h.jsClient.GetAssetByIP(hostData.IP)
	if err != nil {
		return fmt.Errorf("failed to find existing asset: %v", err)
	}

	if existingAsset == nil {
		// 资产不存在，创建新资产
		return h.handleHostCreate(event)
	}

	// 映射为JumpServer资产
	jsAsset, err := h.mapper.MapHostToJumpServer(hostData)
	if err != nil {
		return fmt.Errorf("failed to map host: %v", err)
	}

	// 保留现有资产的节点绑定，避免清空节点
	jsAsset.Nodes = existingAsset.Nodes

	// 更新资产
	updatedAsset, err := h.jsClient.UpdateAsset(existingAsset.ID, jsAsset)
	if err != nil {
		return fmt.Errorf("failed to update asset in JumpServer: %v", err)
	}

	logger.Infof("Asset updated successfully: host_ip=%s, js_hostname=%s, js_id=%s",
		hostData.IP, updatedAsset.Hostname, updatedAsset.ID)

	return nil
}

// handleHostDelete 处理主机删除事件
func (h *Handler) handleHostDelete(event *events.Event) error {
	hostData, err := h.parseHostEventData(event)
	if err != nil {
		return err
	}

	// 查找现有资产
	existingAsset, err := h.jsClient.GetAssetByIP(hostData.IP)
	if err != nil {
		return fmt.Errorf("failed to find asset for deletion: %v", err)
	}

	if existingAsset == nil {
		logger.Infof("Asset not found in JumpServer, skipping deletion: host_ip=%s", hostData.IP)
		return nil
	}

	// 删除资产
	if err := h.jsClient.DeleteAsset(existingAsset.ID); err != nil {
		return fmt.Errorf("failed to delete asset from JumpServer: %v", err)
	}

	logger.Infof("Asset deleted successfully: host_ip=%s, js_hostname=%s, js_id=%s",
		hostData.IP, existingAsset.Hostname, existingAsset.ID)

	return nil
}

// handleResourceCreate, handleResourceUpdate, handleResourceDelete 处理资源事件
// 这些方法的实现类似于主机事件处理，但处理的是RDB中的resource表数据

func (h *Handler) handleResourceCreate(event *events.Event) error {
	// 实现资源创建逻辑
	logger.Infof("Resource create event received: %s", event.ID)
	return nil
}

func (h *Handler) handleResourceUpdate(event *events.Event) error {
	// 实现资源更新逻辑
	logger.Infof("Resource update event received: %s", event.ID)
	return nil
}

func (h *Handler) handleResourceDelete(event *events.Event) error {
	// 实现资源删除逻辑
	logger.Infof("Resource delete event received: %s", event.ID)
	return nil
}

func (h *Handler) handleResourceBind(event *events.Event) error {
	bindData, err := h.parseResourceBindEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Resource bind event: node_path=%s, resource_uuid=%s",
		bindData.NodePath, bindData.ResourceUUID)

	// 获取RDB中的资源信息
	resource, err := h.getResourceByUUID(bindData.ResourceUUID)
	if err != nil {
		return fmt.Errorf("failed to get resource by UUID %s: %v", bindData.ResourceUUID, err)
	}

	if resource == nil {
		logger.Infof("Resource not found in RDB: uuid=%s", bindData.ResourceUUID)
		return nil
	}

	// 检查资源类型，只处理host类型的资源
	if resource.SourceType != "host" {
		logger.Infof("Resource is not a host, skipping: uuid=%s, source_type=%s",
			resource.UUID, resource.SourceType)
		return nil
	}

	// 根据source_id从AMS库获取主机信息
	host, err := h.getHostBySourceID(resource.SourceId)
	if err != nil {
		return fmt.Errorf("failed to get host by source_id %d: %v", resource.SourceId, err)
	}

	if host == nil {
		logger.Infof("Host not found in AMS: source_id=%d", resource.SourceId)
		return nil
	}

	// 获取JumpServer中对应的节点
	nodePK, _ := h.convertNodePathToJSNodePK(bindData.NodePath)
	if nodePK == "" {
		return fmt.Errorf("failed to find JumpServer node for path: %s", bindData.NodePath)
	}

	// 检查JumpServer中是否已存在该资产
	var asset *jumpserver.Asset

	// 首先尝试通过主机IP查找
	if host.IP != "" {
		asset, err = h.jsClient.GetAssetByIP(host.IP)
		if err != nil {
			logger.Errorf("Failed to get asset by IP %s: %v", host.IP, err)
		}
	}

	// 如果通过IP没找到，尝试通过主机名查找
	if asset == nil && host.Name != "" {
		asset, err = h.jsClient.GetAssetByName(host.Name)
		if err != nil {
			logger.Errorf("Failed to get asset by name %s: %v", host.Name, err)
		}
	}

	//// 如果通过主机名没找到，尝试通过标识查找
	//if asset == nil && host.Ident != "" {
	//	asset, err = h.jsClient.GetAssetByName(host.Ident)
	//	if err != nil {
	//		logger.Errorf("Failed to get asset by ident %s: %v", host.Ident, err)
	//	}
	//}

	// 如果资产不存在，创建新资产
	if asset == nil {
		logger.Infof("Asset not found in JumpServer, creating new asset: name=%s, ip=%s, ident=%s",
			host.Name, host.IP, host.Ident)

		newAsset := &jumpserver.Asset{
			Hostname: host.Name,
			Name:     host.Name,
			IP:       host.IP,
			Address:  host.IP,
			Comment:  fmt.Sprintf("Auto-synced from AMS - Host ID: %d, Resource UUID: %s", host.Id, resource.UUID),
			Nodes:    []string{nodePK}, // 直接绑定到目标节点
			IsActive: true,
		}

		asset, err = h.jsClient.CreateAsset(newAsset)
		if err != nil {
			return fmt.Errorf("failed to create asset in JumpServer: %v", err)
		}

		logger.Infof("Asset created in JumpServer: id=%s, name=%s, ip=%s, node=%s",
			asset.ID, asset.Hostname, asset.IP, nodePK)
	} else {
		// 资产已存在，更新其节点绑定
		logger.Infof("Asset found in JumpServer: id=%s, name=%s, hostname=%s, address=%s, ip=%s",
			asset.ID, asset.Name, asset.Hostname, asset.Address, asset.IP)

		// 检查资产是否已经绑定到该节点
		nodeAlreadyBound := false
		var existingNodeIDs []string

		// 处理JumpServer返回的节点格式：[{"id":"node-id","name":"node-name"}]
		if nodesInterface, ok := asset.Nodes.([]interface{}); ok {
			for _, nodeInterface := range nodesInterface {
				if nodeMap, ok := nodeInterface.(map[string]interface{}); ok {
					if nodeID, ok := nodeMap["id"].(string); ok {
						existingNodeIDs = append(existingNodeIDs, nodeID)
						if nodeID == nodePK {
							nodeAlreadyBound = true
						}
					}
				}
			}
		} else if nodes, ok := asset.Nodes.([]string); ok {
			// 兼容[]string格式
			existingNodeIDs = nodes
			for _, nodeID := range nodes {
				if nodeID == nodePK {
					nodeAlreadyBound = true
					break
				}
			}
		}

		if !nodeAlreadyBound {
			// 添加新节点到现有节点列表
			existingNodeIDs = append(existingNodeIDs, nodePK)
			asset.Nodes = existingNodeIDs

			logger.Infof("Adding node to asset: asset_id=%s, existing_nodes=%v, new_node=%s, final_nodes=%v",
				asset.ID, existingNodeIDs[:len(existingNodeIDs)-1], nodePK, existingNodeIDs)

			// 更新JumpServer中的资产节点绑定
			_, err := h.jsClient.UpdateAsset(asset.ID, asset)
			if err != nil {
				return fmt.Errorf("failed to update asset nodes in JumpServer: %v", err)
			}

			logger.Infof("Asset updated with new node binding: asset_id=%s, node=%s, total_nodes=%d",
				asset.ID, nodePK, len(existingNodeIDs))
		} else {
			logger.Infof("Asset already bound to node: asset_id=%s, node=%s",
				asset.ID, nodePK)
		}
	}

	return nil
}

func (h *Handler) handleResourceUnbind(event *events.Event) error {
	bindData, err := h.parseResourceBindEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Resource unbind event: node_path=%s, resource_uuid=%s",
		bindData.NodePath, bindData.ResourceUUID)

	// 获取RDB中的资源信息
	resource, err := h.getResourceByUUID(bindData.ResourceUUID)
	if err != nil {
		return fmt.Errorf("failed to get resource by UUID %s: %v", bindData.ResourceUUID, err)
	}

	if resource == nil {
		logger.Infof("Resource not found in RDB: uuid=%s", bindData.ResourceUUID)
		return nil
	}

	// 检查资源类型，只处理host类型的资源
	if resource.SourceType != "host" {
		logger.Infof("Resource is not a host, skipping: uuid=%s, source_type=%s",
			resource.UUID, resource.SourceType)
		return nil
	}

	// 根据source_id从AMS库获取主机信息
	host, err := h.getHostBySourceID(resource.SourceId)
	if err != nil {
		return fmt.Errorf("failed to get host by source_id %d: %v", resource.SourceId, err)
	}

	if host == nil {
		logger.Infof("Host not found in AMS: source_id=%d", resource.SourceId)
		return nil
	}

	// 获取JumpServer中对应的节点
	nodePK, _ := h.convertNodePathToJSNodePK(bindData.NodePath)
	if nodePK == "" {
		return fmt.Errorf("failed to find JumpServer node for path: %s", bindData.NodePath)
	}

	// 查找JumpServer中对应的资产
	var asset *jumpserver.Asset

	// 首先尝试通过主机IP查找
	if host.IP != "" {
		asset, err = h.jsClient.GetAssetByIP(host.IP)
		if err != nil {
			logger.Errorf("Failed to get asset by IP %s: %v", host.IP, err)
		}
	}

	// 如果通过IP没找到，尝试通过主机名查找
	if asset == nil && host.Name != "" {
		asset, err = h.jsClient.GetAssetByName(host.Name)
		if err != nil {
			logger.Errorf("Failed to get asset by name %s: %v", host.Name, err)
		}
	}

	// 如果通过主机名没找到，尝试通过标识查找
	if asset == nil && host.Ident != "" {
		asset, err = h.jsClient.GetAssetByName(host.Ident)
		if err != nil {
			logger.Errorf("Failed to get asset by ident %s: %v", host.Ident, err)
		}
	}

	if asset == nil {
		logger.Infof("Asset not found in JumpServer: name=%s, ip=%s, ident=%s",
			host.Name, host.IP, host.Ident)
		return nil
	}

	logger.Infof("Asset found in JumpServer: id=%s, name=%s, hostname=%s, address=%s, ip=%s",
		asset.ID, asset.Name, asset.Hostname, asset.Address, asset.IP)

	// 从资产的节点列表中移除指定节点
	var updatedNodes []string
	nodeRemoved := false

	// 处理JumpServer返回的节点格式：[{"id":"node-id","name":"node-name"}]
	if nodesInterface, ok := asset.Nodes.([]interface{}); ok {
		for _, nodeInterface := range nodesInterface {
			if nodeMap, ok := nodeInterface.(map[string]interface{}); ok {
				if nodeID, ok := nodeMap["id"].(string); ok {
					if nodeID != nodePK {
						updatedNodes = append(updatedNodes, nodeID)
					} else {
						nodeRemoved = true
						logger.Infof("Found node to remove: node_id=%s, node_name=%s",
							nodeID, nodeMap["name"])
					}
				}
			}
		}
	} else if nodes, ok := asset.Nodes.([]string); ok {
		// 兼容[]string格式
		for _, nodeID := range nodes {
			if nodeID != nodePK {
				updatedNodes = append(updatedNodes, nodeID)
			} else {
				nodeRemoved = true
				logger.Infof("Found node to remove: node_id=%s", nodeID)
			}
		}
	}

	// 计算原始节点数量
	originalCount := 0
	if nodesInterface, ok := asset.Nodes.([]interface{}); ok {
		originalCount = len(nodesInterface)
	} else if nodes, ok := asset.Nodes.([]string); ok {
		originalCount = len(nodes)
	}

	logger.Infof("Node removal analysis: asset_id=%s, target_node=%s, node_removed=%t, original_count=%d, updated_count=%d",
		asset.ID, nodePK, nodeRemoved, originalCount, len(updatedNodes))

	if !nodeRemoved {
		logger.Infof("Asset was not bound to node: asset_id=%s, node=%s",
			asset.ID, nodePK)
		return nil
	}

	// 如果资产没有绑定到任何节点了，删除资产
	if len(updatedNodes) == 0 {
		logger.Infof("Asset has no more node bindings, deleting asset: asset_id=%s", asset.ID)

		err = h.jsClient.DeleteAsset(asset.ID)
		if err != nil {
			return fmt.Errorf("failed to delete asset from JumpServer: %v", err)
		}

		logger.Infof("Asset deleted from JumpServer: asset_id=%s", asset.ID)
	} else {
		// 更新资产的节点绑定
		asset.Nodes = updatedNodes

		_, err = h.jsClient.UpdateAsset(asset.ID, asset)
		if err != nil {
			return fmt.Errorf("failed to update asset nodes in JumpServer: %v", err)
		}

		logger.Infof("Asset updated, node binding removed: asset_id=%s, removed_node=%s, remaining_nodes=%v",
			asset.ID, nodePK, updatedNodes)
	}

	return nil
}

// ensureParentNodeExists 递归确保父节点存在，返回父节点的JumpServer ID
func (h *Handler) ensureParentNodeExists(nodeData *NodeInfo) (string, error) {
	logger.Infof("Ensuring parent node exists: name=%s, path=%s, pid=%d",
		nodeData.Name, nodeData.Path, nodeData.PID)

	// 如果是根节点，直接查找
	if nodeData.PID == 0 {
		logger.Infof("Node is root level: %s", nodeData.Name)
		existingNode, err := h.jsClient.GetNodeByParentAndValue("", nodeData.Name)
		if err != nil {
			return "", fmt.Errorf("failed to find root node: %v", err)
		}

		if existingNode != nil {
			logger.Infof("Root node found: id=%s, name=%s", existingNode.ID, existingNode.Value)
			return existingNode.ID, nil
		}

		// 根节点不存在，创建它
		logger.Infof("Root node not found, creating: %s", nodeData.Name)
		return h.createNodeFromNodeData(nodeData, "")
	}

	// 非根节点，需要先确保其父节点存在
	grandParentData, err := h.getNodeByID(nodeData.PID)
	if err != nil {
		return "", fmt.Errorf("failed to get grandparent node (id=%d): %v", nodeData.PID, err)
	}

	logger.Infof("Getting grandparent for node %s: grandparent=%s (path=%s)",
		nodeData.Name, grandParentData.Name, grandParentData.Path)

	// 递归确保祖父节点存在
	grandParentID, err := h.ensureParentNodeExists(grandParentData)
	if err != nil {
		return "", fmt.Errorf("failed to ensure grandparent exists: %v", err)
	}

	logger.Infof("Grandparent resolved: id=%s, now looking for parent %s under grandparent",
		grandParentID, nodeData.Name)

	// 在祖父节点下查找当前节点
	existingNode, err := h.jsClient.GetNodeByParentAndValue(grandParentID, nodeData.Name)
	if err != nil {
		return "", fmt.Errorf("failed to find node %s under parent %s: %v",
			nodeData.Name, grandParentID, err)
	}

	if existingNode != nil {
		logger.Infof("Node found under correct parent: id=%s, name=%s, parent_id=%s",
			existingNode.ID, existingNode.Value, grandParentID)
		return existingNode.ID, nil
	}

	// 节点不存在，创建它
	logger.Infof("Node not found under parent, creating: name=%s, parent_id=%s",
		nodeData.Name, grandParentID)
	return h.createNodeFromNodeData(nodeData, grandParentID)
}

// createNodeFromNodeData 从NodeData创建JumpServer节点
func (h *Handler) createNodeFromNodeData(nodeData *NodeInfo, parentID string) (string, error) {
	// 转换为mapper.NodeInfo类型
	mapperNodeData := &mapper.NodeInfo{
		ID:          nodeData.ID,
		PID:         nodeData.PID,
		Ident:       nodeData.Ident,
		Name:        nodeData.Name,
		Note:        nodeData.Note,
		Path:        nodeData.Path,
		Leaf:        nodeData.Leaf,
		Cate:        nodeData.Cate,
		IconColor:   nodeData.IconColor,
		IconChar:    nodeData.IconChar,
		Proxy:       nodeData.Proxy,
		Creator:     nodeData.Creator,
		LastUpdated: nodeData.LastUpdated,
	}

	// 映射为JumpServer节点
	jsNode, err := h.mapper.MapNodeInfoToJumpServer(mapperNodeData)
	if err != nil {
		return "", fmt.Errorf("failed to map node: %v", err)
	}

	jsNode.Parent = parentID

	logger.Infof("Creating JumpServer node: key=%s, value=%s, parent=%s",
		jsNode.Key, jsNode.Value, jsNode.Parent)

	createdNode, err := h.jsClient.CreateNode(jsNode)
	if err != nil {
		return "", fmt.Errorf("failed to create node in JumpServer: %v", err)
	}

	logger.Infof("Node created successfully: id=%s, key=%s, value=%s",
		createdNode.ID, createdNode.Key, createdNode.Value)

	return createdNode.ID, nil
}

// ==================== 用户管理事件处理 ====================

// handleUserCreate 处理用户创建事件
func (h *Handler) handleUserCreate(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Creating user: username=%s, dispname=%s, type=%d", userData.Username, userData.Dispname, userData.GetType())

	// 检查临时用户是否已过期
	if userData.GetType() == 1 { // USER_T_TEMP
		now := time.Now().Unix()
		activeBegin := userData.GetActiveBeginTimestamp()
		activeEnd := userData.GetActiveEndTimestamp()

		if activeEnd > 0 && (now < activeBegin || activeEnd < now) {
			logger.Infof("Temporary user %s is expired (valid: %d - %d, now: %d), skipping creation",
				userData.Username, activeBegin, activeEnd, now)
			return nil
		}
	}

	// 映射RDB用户到JumpServer用户
	jsUser := &jumpserver.User{
		Username:    userData.Username,
		Name:        userData.Dispname,
		Email:       userData.Email,
		Phone:       userData.Phone,
		Password:    userData.Password,         // 包含密码，由CreateUserWithPassword方法处理
		IsActive:    userData.GetStatus() == 0, // RDB中0表示active
		IsSuperuser: userData.GetIsRoot() == 1,
		Source:      "local",         // 使用"local"而不是"rdb"
		Groups:      []interface{}{}, // 初始化为空数组
		SystemRoles: []interface{}{}, // 初始化为空数组
		OrgRoles:    []interface{}{}, // 初始化为空数组
	}

	// 处理用户类型和失效时间
	// 统一使用默认组织信息，不依赖RDB中的Organization字段

	if userData.GetType() == 1 { // USER_T_TEMP 临时用户
		activeEnd := userData.GetActiveEndTimestamp()
		if activeEnd > 0 {
			// 将时间戳转换为JumpServer需要的ISO 8601格式
			expiredTime := time.Unix(activeEnd, 0).UTC().Format("2006-01-02T15:04:05Z")
			jsUser.DateExpired = expiredTime
			jsUser.Comment = fmt.Sprintf("Temporary user (expires: %s)",
				time.Unix(activeEnd, 0).Format("2006-01-02 15:04:05"))
		} else {
			jsUser.Comment = "Temporary user"
		}
	} else {
		// 永久用户
		jsUser.Comment = "Permanent user"
	}

	// 使用两步操作创建用户并设置密码
	createdUser, err := h.jsClient.CreateUserWithPassword(jsUser)
	if err != nil {
		return fmt.Errorf("failed to create user in JumpServer: %v", err)
	}

	logger.Infof("User creation completed: username=%s, js_id=%s, type=%s",
		userData.Username, createdUser.ID,
		map[int]string{0: "permanent", 1: "temporary"}[userData.GetType()])
	return nil
}

// handleUserUpdate 处理用户更新事件
func (h *Handler) handleUserUpdate(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Updating user: username=%s, type=%d", userData.Username, userData.GetType())

	// 检查临时用户是否已过期
	if userData.GetType() == 1 { // USER_T_TEMP
		now := time.Now().Unix()
		activeBegin := userData.GetActiveBeginTimestamp()
		activeEnd := userData.GetActiveEndTimestamp()

		if activeEnd > 0 && (now < activeBegin || activeEnd < now) {
			logger.Infof("Temporary user %s is expired (valid: %d - %d, now: %d), disabling user",
				userData.Username, activeBegin, activeEnd, now)
			// 如果用户已过期，将其设为非活跃状态
			userData.Status = 1 // USER_S_INACTIVE
		}
	}

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(userData.Username)
	if err != nil {
		logger.Infof("User not found in JumpServer, creating new user: %s", userData.Username)
		return h.handleUserCreate(event)
	}

	// 更新用户信息
	existingUser.Name = userData.Dispname

	// 只有当RDB中有有效邮箱时才更新，避免用空字符串覆盖JumpServer中的邮箱
	if userData.Email != "" {
		logger.Infof("Updating user email from RDB: %s -> %s", existingUser.Username, userData.Email)
		existingUser.Email = userData.Email
	} else {
		logger.Infof("RDB email is empty for user %s, keeping existing email: %s",
			existingUser.Username, existingUser.Email)
	}

	existingUser.Phone = userData.Phone
	existingUser.IsActive = userData.GetStatus() == 0
	existingUser.IsSuperuser = userData.GetIsRoot() == 1

	// 处理用户类型和失效时间
	// 统一使用默认组织信息，不依赖RDB中的Organization字段

	if userData.GetType() == 1 { // USER_T_TEMP 临时用户
		activeEnd := userData.GetActiveEndTimestamp()
		if activeEnd > 0 {
			// 将时间戳转换为JumpServer需要的ISO 8601格式
			expiredTime := time.Unix(activeEnd, 0).UTC().Format("2006-01-02T15:04:05Z")
			existingUser.DateExpired = expiredTime
			existingUser.Comment = fmt.Sprintf("Temporary user (expires: %s)",
				time.Unix(activeEnd, 0).Format("2006-01-02 15:04:05"))
		} else {
			existingUser.Comment = "Temporary user"
		}
	} else {
		// 永久用户，清除失效时间
		existingUser.DateExpired = nil
		existingUser.Comment = "Permanent user"
	}

	// 执行更新
	_, err = h.jsClient.UpdateUser(existingUser.ID, existingUser)
	if err != nil {
		return fmt.Errorf("failed to update user in JumpServer: %v", err)
	}

	logger.Infof("User updated successfully: username=%s, js_id=%s, type=%s",
		userData.Username, existingUser.ID,
		map[int]string{0: "permanent", 1: "temporary"}[userData.GetType()])
	return nil
}

// CheckExpiredTempUsers 检查并处理过期的临时用户
func (h *Handler) CheckExpiredTempUsers() error {
	logger.Infof("Starting expired temporary users check")

	now := time.Now().Unix()

	// 查询所有过期的临时用户
	var expiredUsers []models.User
	err := models.DB["rdb"].Where("type=1 AND active_end>0 AND active_end<?", now).Find(&expiredUsers)
	if err != nil {
		return fmt.Errorf("failed to query expired temporary users: %v", err)
	}

	if len(expiredUsers) == 0 {
		logger.Infof("No expired temporary users found")
		return nil
	}

	logger.Infof("Found %d expired temporary users", len(expiredUsers))

	for _, user := range expiredUsers {
		// 检查用户在RDB中是否已经被禁用
		if user.Status == 0 { // 如果用户在RDB中仍然是活跃状态，则禁用它
			logger.Infof("Disabling expired temporary user in RDB: %s (expired at: %s)",
				user.Username, time.Unix(user.ActiveEnd, 0).Format("2006-01-02 15:04:05"))

			user.Status = 1 // USER_S_INACTIVE
			if err := user.Update("status"); err != nil {
				logger.Errorf("Failed to disable expired user %s in RDB: %v", user.Username, err)
				continue
			}
		}

		// 在JumpServer中禁用用户
		jsUser, err := h.jsClient.GetUser(user.Username)
		if err != nil {
			logger.Warningf("User %s not found in JumpServer, skipping: %v", user.Username, err)
			continue
		}

		if jsUser.IsActive {
			logger.Infof("Disabling expired temporary user in JumpServer: %s", user.Username)
			jsUser.IsActive = false
			jsUser.Comment = fmt.Sprintf("Temporary user expired at %s - Org: %s",
				time.Unix(user.ActiveEnd, 0).Format("2006-01-02 15:04:05"), user.Organization)

			_, err = h.jsClient.UpdateUser(jsUser.ID, jsUser)
			if err != nil {
				logger.Errorf("Failed to disable expired user %s in JumpServer: %v", user.Username, err)
				continue
			}

			logger.Infof("Successfully disabled expired temporary user: %s", user.Username)
		}
	}

	logger.Infof("Expired temporary users check completed")
	return nil
}

// StartExpiredUserChecker 启动过期用户检查定时任务
func (h *Handler) StartExpiredUserChecker() {
	// 每小时检查一次过期用户
	ticker := time.NewTicker(1 * time.Hour)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				if err := h.CheckExpiredTempUsers(); err != nil {
					logger.Errorf("Failed to check expired temporary users: %v", err)
				}
			}
		}
	}()

	logger.Infof("Expired temporary users checker started (interval: 1 hour)")
}

// handleUserDelete 处理用户删除事件
func (h *Handler) handleUserDelete(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Deleting user: username=%s", userData.Username)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(userData.Username)
	if err != nil {
		logger.Infof("User not found in JumpServer, skipping deletion: %s", userData.Username)
		return nil
	}

	// 直接从JumpServer删除用户
	err = h.jsClient.DeleteUser(existingUser.ID)
	if err != nil {
		return fmt.Errorf("failed to delete user from JumpServer: %v", err)
	}

	logger.Infof("User deleted successfully: username=%s, js_id=%s", userData.Username, existingUser.ID)
	return nil
}

// handleUserEnable 处理用户启用事件
func (h *Handler) handleUserEnable(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	return h.updateUserStatus(userData.Username, true)
}

// handleUserDisable 处理用户禁用事件
func (h *Handler) handleUserDisable(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	return h.updateUserStatus(userData.Username, false)
}

// updateUserStatus 更新用户状态的辅助方法
func (h *Handler) updateUserStatus(username string, isActive bool) error {
	logger.Infof("Updating user status: username=%s, active=%v", username, isActive)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(username)
	if err != nil {
		return fmt.Errorf("user not found in JumpServer: %s", username)
	}

	// 使用PATCH方法只更新状态字段
	err = h.jsClient.UpdateUserStatus(existingUser.ID, isActive)
	if err != nil {
		return fmt.Errorf("failed to update user status in JumpServer: %v", err)
	}

	logger.Infof("User status updated successfully: username=%s, active=%v, js_id=%s", username, isActive, existingUser.ID)
	return nil
}

// handleUserChangePassword 处理用户密码修改事件
func (h *Handler) handleUserChangePassword(event *events.Event) error {
	passwordData, err := h.parsePasswordChangeEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Changing password for user: username=%s", passwordData.Username)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(passwordData.Username)
	if err != nil {
		return fmt.Errorf("user not found in JumpServer: %s", passwordData.Username)
	}

	// 修改密码
	err = h.jsClient.ChangeUserPassword(existingUser.ID, passwordData.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to change user password in JumpServer: %v", err)
	}

	logger.Infof("Password changed successfully: username=%s, js_id=%s", passwordData.Username, existingUser.ID)
	return nil
}

// ==================== 事件数据解析辅助方法 ====================

// parseUserEventData 解析用户事件数据
func (h *Handler) parseUserEventData(event *events.Event) (*events.UserEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var userData events.UserEventData
	err = json.Unmarshal(dataBytes, &userData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal user event data: %v", err)
	}

	return &userData, nil
}

// parsePasswordChangeEventData 解析密码变更事件数据
func (h *Handler) parsePasswordChangeEventData(event *events.Event) (*events.PasswordChangeEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var passwordData events.PasswordChangeEventData
	err = json.Unmarshal(dataBytes, &passwordData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal password change event data: %v", err)
	}

	return &passwordData, nil
}

// parseTeamEventData 解析团队事件数据
func (h *Handler) parseTeamEventData(event *events.Event) (*events.TeamEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var teamData events.TeamEventData
	err = json.Unmarshal(dataBytes, &teamData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal team event data: %v", err)
	}

	return &teamData, nil
}

// parsePermissionEventData 解析权限事件数据
func (h *Handler) parsePermissionEventData(event *events.Event) (*events.PermissionEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var permissionData events.PermissionEventData
	err = json.Unmarshal(dataBytes, &permissionData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal permission event data: %v", err)
	}

	return &permissionData, nil
}

// ==================== 资源管理辅助方法 ====================

// getResourceByUUID 根据UUID获取资源信息
func (h *Handler) getResourceByUUID(uuid string) (*models.Resource, error) {
	// 检查RDB数据库是否可用
	if models.DB["rdb"] == nil {
		return nil, fmt.Errorf("RDB database not connected")
	}

	// 查询资源信息
	var resource models.Resource
	has, err := models.DB["rdb"].Where("uuid = ?", uuid).Get(&resource)
	if err != nil {
		return nil, fmt.Errorf("failed to query resource by UUID %s: %v", uuid, err)
	}

	if !has {
		return nil, nil // 资源不存在
	}

	logger.Infof("Found RDB resource: uuid=%s, name=%s, ident=%s, source_id=%d, source_type=%s",
		resource.UUID, resource.Name, resource.Ident, resource.SourceId, resource.SourceType)
	return &resource, nil
}

// getHostBySourceID 根据source_id从AMS库获取主机信息
func (h *Handler) getHostBySourceID(sourceID int64) (*models.Host, error) {
	// 检查AMS数据库是否可用
	if models.DB["ams"] == nil {
		return nil, fmt.Errorf("AMS database not connected")
	}

	// 查询主机信息
	var host models.Host
	has, err := models.DB["ams"].Where("id = ?", sourceID).Get(&host)
	if err != nil {
		return nil, fmt.Errorf("failed to query host by ID %d: %v", sourceID, err)
	}

	if !has {
		return nil, nil // 主机不存在
	}

	logger.Infof("Found AMS host: id=%d, name=%s, ip=%s, ident=%s",
		host.Id, host.Name, host.IP, host.Ident)
	return &host, nil
}
