package sync

import (
	"arboris/src/models"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/toolkits/pkg/logger"

	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// 账号模板事件处理

// handleAccountTemplateCreate 处理账号模板创建事件
func (h *Handler) handleAccountTemplateCreate(event *events.Event) error {
	templateData, err := h.parseAccountTemplateEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account template event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account template create: event_id=%s, rdb_id=%d, name=%s",
		event.ID, templateData.GetID(), templateData.Name)

	// 检查是否已存在
	existing, err := h.jsClient.GetAccountTemplates(templateData.Name)
	if err != nil {
		logger.Errorf("Failed to query existing template: name=%s, error=%v", templateData.Name, err)
		return fmt.E<PERSON><PERSON>("failed to query existing template: %v", err)
	}

	if len(existing) > 0 {
		logger.Infof("Account template already exists, updating: name=%s, js_id=%s",
			templateData.Name, existing[0].ID)
		return h.updateAccountTemplate(templateData, existing[0].ID)
	}

	// 映射数据
	jsTemplate, err := h.mapAccountTemplateToJS(templateData)
	if err != nil {
		logger.Errorf("Failed to map template data: rdb_id=%d, error=%v", templateData.GetID(), err)
		return fmt.Errorf("failed to map template data: %v", err)
	}

	logger.Debugf("Mapped template data: rdb_id=%d, js_template=%+v", templateData.GetID(), jsTemplate)

	// 创建模板
	created, err := h.jsClient.CreateAccountTemplate(jsTemplate)
	if err != nil {
		logger.Errorf("Failed to create account template in JumpServer: rdb_id=%d, name=%s, error=%v",
			templateData.GetID(), templateData.Name, err)
		return fmt.Errorf("failed to create account template: %v", err)
	}

	logger.Infof("Account template created successfully: rdb_id=%d, js_id=%s, name=%s",
		templateData.GetID(), created.ID, templateData.Name)
	return nil
}

// handleAccountTemplateUpdate 处理账号模板更新事件
func (h *Handler) handleAccountTemplateUpdate(event *events.Event) error {
	templateData, err := h.parseAccountTemplateEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account template event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account template update: event_id=%s, rdb_id=%d, name=%s, original_name=%s",
		event.ID, templateData.GetID(), templateData.Name, templateData.OriginalName)

	// 调试：打印完整的事件数据
	logger.Debugf("Template event data: %+v", templateData)

	// 查找现有模板 - 优先使用原始名称
	var existing []jumpserver.AccountTemplate

	if templateData.OriginalName != "" {
		logger.Infof("Searching template by original name: original_name=%s", templateData.OriginalName)
		existing, err = h.jsClient.GetAccountTemplates(templateData.OriginalName)
		if err != nil {
			logger.Errorf("Failed to query by original name: name=%s, error=%v", templateData.OriginalName, err)
		} else if len(existing) > 0 {
			logger.Infof("Found template by original name: js_id=%s, name=%s",
				existing[0].ID, existing[0].Name)
			return h.updateAccountTemplate(templateData, existing[0].ID)
		} else {
			logger.Infof("Template not found by original name: %s", templateData.OriginalName)
		}
	}

	// 如果原始名称没找到，尝试使用当前名称查找
	logger.Infof("Searching template by current name: name=%s", templateData.Name)
	existing, err = h.jsClient.GetAccountTemplates(templateData.Name)
	if err != nil {
		logger.Errorf("Failed to query existing template: name=%s, error=%v", templateData.Name, err)
		return fmt.Errorf("failed to query existing template: %v", err)
	}

	if len(existing) == 0 {
		logger.Infof("Template not found by any name, creating new one: name=%s", templateData.Name)
		return h.handleAccountTemplateCreate(event)
	}

	logger.Infof("Found template by current name: js_id=%s, name=%s",
		existing[0].ID, existing[0].Name)
	return h.updateAccountTemplate(templateData, existing[0].ID)
}

// handleAccountTemplateDelete 处理账号模板删除事件
func (h *Handler) handleAccountTemplateDelete(event *events.Event) error {
	templateData, err := h.parseAccountTemplateEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account template event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account template delete: event_id=%s, rdb_id=%d, name=%s",
		event.ID, templateData.GetID(), templateData.Name)

	// 查找现有模板
	existing, err := h.jsClient.GetAccountTemplates(templateData.Name)
	if err != nil {
		logger.Errorf("Failed to query existing template: name=%s, error=%v", templateData.Name, err)
		return fmt.Errorf("failed to query existing template: %v", err)
	}

	if len(existing) == 0 {
		logger.Infof("Template not found in JumpServer, skipping delete: name=%s", templateData.Name)
		return nil
	}

	// 删除模板
	err = h.jsClient.DeleteAccountTemplate(existing[0].ID)
	if err != nil {
		logger.Errorf("Failed to delete account template: js_id=%s, name=%s, error=%v",
			existing[0].ID, templateData.Name, err)
		return fmt.Errorf("failed to delete account template: %v", err)
	}

	logger.Infof("Account template deleted successfully: rdb_id=%d, js_id=%s, name=%s",
		templateData.GetID(), existing[0].ID, templateData.Name)
	return nil
}

// updateAccountTemplate 更新账号模板
func (h *Handler) updateAccountTemplate(templateData *events.AccountTemplateEventData, jsID string) error {
	jsTemplate, err := h.mapAccountTemplateToJS(templateData)
	if err != nil {
		logger.Errorf("Failed to map template data for update: rdb_id=%d, error=%v", templateData.GetID(), err)
		return fmt.Errorf("failed to map template data: %v", err)
	}

	logger.Debugf("Updating template: js_id=%s, js_template=%+v", jsID, jsTemplate)

	updated, err := h.jsClient.UpdateAccountTemplate(jsID, jsTemplate)
	if err != nil {
		logger.Errorf("Failed to update account template: js_id=%s, rdb_id=%d, error=%v",
			jsID, templateData.GetID(), err)
		return fmt.Errorf("failed to update account template: %v", err)
	}

	logger.Infof("Account template updated successfully: rdb_id=%d, js_id=%s, name=%s",
		templateData.GetID(), updated.ID, templateData.Name)
	return nil
}

// mapAccountTemplateToJS 映射账号模板数据到JumpServer格式
func (h *Handler) mapAccountTemplateToJS(templateData *events.AccountTemplateEventData) (*jumpserver.AccountTemplate, error) {
	jsTemplate := &jumpserver.AccountTemplate{
		Name:       templateData.Name,
		Username:   templateData.Username,
		Privileged: templateData.GetPrivileged(),
		IsActive:   templateData.GetIsActive(),
		AutoPush:   templateData.GetAutoPush(),
		Comment:    templateData.Comment,
		// 设置必需的默认值
		PasswordRules: map[string]interface{}{
			"length":          16,
			"lowercase":       true,
			"uppercase":       true,
			"digit":           true,
			"symbol":          true,
			"exclude_symbols": "",
		},
		PushParams: map[string]interface{}{},
		Platforms:  []interface{}{},
		Labels:     []interface{}{},
		SpecInfo:   map[string]interface{}{},
	}

	// 设置密文类型
	jsTemplate.SetSecretType(templateData.SecretType)

	// 设置密文策略
	jsTemplate.SetSecretStrategy(templateData.SecretStrategy)

	// 处理密文
	if templateData.Secret != "" {
		jsTemplate.Secret = templateData.Secret
	}
	if templateData.Passphrase != "" {
		jsTemplate.Passphrase = templateData.Passphrase
	}

	// 处理SuFrom引用
	if templateData.GetSuFrom() > 0 {
		// 这里需要查询RDB获取引用的账号信息，然后在JumpServer中查找对应的账号
		// 暂时先记录日志，后续可以实现更复杂的引用解析
		logger.Debugf("Template has su_from reference: rdb_id=%d, su_from=%d",
			templateData.GetID(), templateData.GetSuFrom())
	} else {
		jsTemplate.SuFrom = nil
	}

	return jsTemplate, nil
}

// parseAccountTemplateEventData 解析账号模板事件数据
func (h *Handler) parseAccountTemplateEventData(event *events.Event) (*events.AccountTemplateEventData, error) {
	// 事件数据直接包含模板信息，不需要特定的键
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal template data: %v", err)
	}

	var templateData events.AccountTemplateEventData
	if err := json.Unmarshal(dataBytes, &templateData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal template data: %v", err)
	}

	return &templateData, nil
}

// parseInt64 安全地转换interface{}为int64
func parseInt64(value interface{}) int64 {
	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		if num, err := strconv.ParseInt(v, 10, 64); err == nil {
			return num
		}
		return 0
	default:
		return 0
	}
}

// parseBool 安全地转换interface{}为bool
func parseBool(value interface{}) bool {
	switch v := value.(type) {
	case bool:
		return v
	case int:
		return v != 0
	case int64:
		return v != 0
	case string:
		return v == "true" || v == "1"
	default:
		return false
	}
}

// 账号事件处理

// handleAccountCreate 处理账号创建事件
func (h *Handler) handleAccountCreate(event *events.Event) error {
	accountData, err := h.parseAccountEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account create: event_id=%s, rdb_id=%d, name=%s, username=%s",
		event.ID, accountData.GetID(), accountData.Name, accountData.Username)

	// 解析绑定的资源并获取资产ID
	assetIDs, err := h.resolveAccountAssets(accountData)
	if err != nil {
		logger.Errorf("Failed to resolve assets for account: rdb_id=%d, error=%v",
			accountData.GetID(), err)
		return fmt.Errorf("failed to resolve assets: %v", err)
	}

	if len(assetIDs) == 0 {
		logger.Errorf("No valid assets found for account: rdb_id=%d, name=%s",
			accountData.GetID(), accountData.Name)
		return nil
	}

	logger.Infof("Resolved %d assets for account: rdb_id=%d, assets=%v",
		len(assetIDs), accountData.GetID(), assetIDs)

	// 为每个资产创建账号
	successCount := 0
	for _, assetID := range assetIDs {
		if err := h.createAccountForAsset(accountData, assetID); err != nil {
			logger.Errorf("Failed to create account for asset: rdb_id=%d, asset_id=%s, error=%v",
				accountData.GetID(), assetID, err)
			// 继续处理其他资产，不中断
		} else {
			successCount++
			logger.Infof("Account created for asset: rdb_id=%d, asset_id=%s",
				accountData.GetID(), assetID)
		}
	}

	logger.Infof("Account creation completed: rdb_id=%d, total_assets=%d, success=%d, failed=%d",
		accountData.GetID(), len(assetIDs), successCount, len(assetIDs)-successCount)

	return nil
}

// handleAccountUpdate 处理账号更新事件
func (h *Handler) handleAccountUpdate(event *events.Event) error {
	accountData, err := h.parseAccountEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account update: event_id=%s, rdb_id=%d, name=%s, username=%s",
		event.ID, accountData.GetID(), accountData.Name, accountData.Username)

	// 获取当前绑定的资产
	assetIDs, err := h.resolveAccountAssets(accountData)
	if err != nil {
		logger.Errorf("Failed to resolve assets for account update: rdb_id=%d, error=%v",
			accountData.GetID(), err)
		return fmt.Errorf("failed to resolve assets: %v", err)
	}

	if len(assetIDs) == 0 {
		logger.Errorf("No assets found for account update: rdb_id=%d", accountData.GetID())
		return nil
	}

	// 更新每个资产上的账号
	successCount := 0
	for _, assetID := range assetIDs {
		if err := h.updateAccountForAsset(accountData, assetID); err != nil {
			logger.Errorf("Failed to update account for asset: rdb_id=%d, asset_id=%s, error=%v",
				accountData.GetID(), assetID, err)
		} else {
			successCount++
			logger.Infof("Account updated for asset: rdb_id=%d, asset_id=%s",
				accountData.GetID(), assetID)
		}
	}

	logger.Infof("Account update completed: rdb_id=%d, total_assets=%d, success=%d, failed=%d",
		accountData.GetID(), len(assetIDs), successCount, len(assetIDs)-successCount)

	return nil
}

// handleAccountDelete 处理账号删除事件
func (h *Handler) handleAccountDelete(event *events.Event) error {
	accountData, err := h.parseAccountEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account delete: event_id=%s, rdb_id=%d, name=%s, username=%s",
		event.ID, accountData.GetID(), accountData.Name, accountData.Username)

	// 查找所有相关的JumpServer账号并删除
	accounts, err := h.jsClient.GetAccounts(accountData.Name, "")
	if err != nil {
		logger.Errorf("Failed to query accounts for deletion: name=%s, error=%v",
			accountData.Name, err)
		return fmt.Errorf("failed to query accounts: %v", err)
	}

	if len(accounts) == 0 {
		logger.Infof("No accounts found in JumpServer for deletion: name=%s", accountData.Name)
		return nil
	}

	// 删除所有匹配的账号
	successCount := 0
	for _, account := range accounts {
		if err := h.jsClient.DeleteAccount(account.ID); err != nil {
			logger.Errorf("Failed to delete account: js_id=%s, name=%s, error=%v",
				account.ID, account.Name, err)
		} else {
			successCount++
			logger.Infof("Account deleted: js_id=%s, name=%s, asset=%s",
				account.ID, account.Name, account.Asset)
		}
	}

	logger.Infof("Account deletion completed: rdb_id=%d, total_accounts=%d, success=%d, failed=%d",
		accountData.GetID(), len(accounts), successCount, len(accounts)-successCount)

	return nil
}

// handleAccountBind 处理账号绑定事件
func (h *Handler) handleAccountBind(event *events.Event) error {
	bindData, err := h.parseAccountBindEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account bind event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account bind: event_id=%s, account_id=%d, resource_id=%d",
		event.ID, bindData.GetAccountID(), bindData.GetResourceID())

	// 获取资源IP并查找JumpServer资产
	resourceIP, err := h.getResourceIP(bindData.GetResourceID())
	if err != nil {
		logger.Errorf("Failed to get resource IP: resource_id=%d, error=%v",
			bindData.GetResourceID(), err)
		return fmt.Errorf("failed to get resource IP: %v", err)
	}

	logger.Debugf("Resource IP resolved: resource_id=%d, ip=%s",
		bindData.GetResourceID(), resourceIP)

	assets, err := h.jsClient.GetAssetsByAddress(resourceIP)
	if err != nil {
		logger.Errorf("Failed to find asset by IP: ip=%s, error=%v", resourceIP, err)
		return fmt.Errorf("failed to find asset by IP %s: %v", resourceIP, err)
	}

	if len(assets) == 0 {
		logger.Errorf("No asset found for IP: ip=%s, resource_id=%d",
			resourceIP, bindData.GetResourceID())
		return nil
	}

	asset := assets[0]
	logger.Infof("Asset found for binding: ip=%s, asset_id=%s, asset_name=%s",
		resourceIP, asset.ID, asset.Name)

	// 获取账号信息并创建/更新
	account, err := h.getAccountFromRDB(bindData.GetAccountID())
	if err != nil {
		logger.Errorf("Failed to get account from RDB: account_id=%d, error=%v",
			bindData.GetAccountID(), err)
		return fmt.Errorf("failed to get account from RDB: %v", err)
	}

	err = h.createOrUpdateAccountForAsset(account, asset.ID)
	if err != nil {
		logger.Errorf("Failed to create/update account for asset: account_id=%d, asset_id=%s, error=%v",
			bindData.GetAccountID(), asset.ID, err)
		return err
	}

	logger.Infof("Account bind completed successfully: account_id=%d, asset_id=%s",
		bindData.GetAccountID(), asset.ID)
	return nil
}

// handleAccountUnbind 处理账号解绑事件
func (h *Handler) handleAccountUnbind(event *events.Event) error {
	bindData, err := h.parseAccountBindEventData(event)
	if err != nil {
		logger.Errorf("Failed to parse account unbind event: event_id=%s, error=%v", event.ID, err)
		return err
	}

	logger.Infof("Processing account unbind: event_id=%s, account_id=%d, resource_id=%d",
		event.ID, bindData.GetAccountID(), bindData.GetResourceID())

	// 获取资源IP并查找JumpServer资产
	resourceIP, err := h.getResourceIP(bindData.GetResourceID())
	if err != nil {
		logger.Errorf("Failed to get resource IP: resource_id=%d, error=%v",
			bindData.GetResourceID(), err)
		return fmt.Errorf("failed to get resource IP: %v", err)
	}

	assets, err := h.jsClient.GetAssetsByAddress(resourceIP)
	if err != nil {
		logger.Errorf("Failed to find asset by IP: ip=%s, error=%v", resourceIP, err)
		return fmt.Errorf("failed to find asset by IP %s: %v", resourceIP, err)
	}

	if len(assets) == 0 {
		logger.Errorf("No asset found for IP: ip=%s, resource_id=%d",
			resourceIP, bindData.GetResourceID())
		return nil
	}

	asset := assets[0]
	logger.Infof("Asset found for unbinding: ip=%s, asset_id=%s, asset_name=%s",
		resourceIP, asset.ID, asset.Name)

	// 获取账号信息
	account, err := h.getAccountFromRDB(bindData.GetAccountID())
	if err != nil {
		logger.Errorf("Failed to get account from RDB: account_id=%d, error=%v",
			bindData.GetAccountID(), err)
		return fmt.Errorf("failed to get account from RDB: %v", err)
	}

	// 查找并删除JumpServer中的账号
	accounts, err := h.jsClient.GetAccounts(account.Name, asset.ID)
	if err != nil {
		logger.Errorf("Failed to query accounts for unbind: name=%s, asset_id=%s, error=%v",
			account.Name, asset.ID, err)
		return fmt.Errorf("failed to query accounts: %v", err)
	}

	if len(accounts) == 0 {
		logger.Infof("No account found for unbind: name=%s, asset_id=%s", account.Name, asset.ID)
		return nil
	}

	// 删除账号
	for _, jsAccount := range accounts {
		if err := h.jsClient.DeleteAccount(jsAccount.ID); err != nil {
			logger.Errorf("Failed to delete account for unbind: js_id=%s, error=%v",
				jsAccount.ID, err)
		} else {
			logger.Infof("Account unbound successfully: js_id=%s, name=%s, asset_id=%s",
				jsAccount.ID, jsAccount.Name, asset.ID)
		}
	}

	return nil
}

// 辅助方法

// parseAccountEventData 解析账号事件数据
func (h *Handler) parseAccountEventData(event *events.Event) (*events.AccountEventData, error) {
	// 事件数据直接包含账号信息，不需要特定的键
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal account data: %v", err)
	}

	var accountData events.AccountEventData
	if err := json.Unmarshal(dataBytes, &accountData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal account data: %v", err)
	}

	return &accountData, nil
}

// parseAccountBindEventData 解析账号绑定事件数据
func (h *Handler) parseAccountBindEventData(event *events.Event) (*events.AccountBindEventData, error) {
	// 事件数据直接包含绑定信息，不需要特定的键
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal binding data: %v", err)
	}

	var bindData events.AccountBindEventData
	if err := json.Unmarshal(dataBytes, &bindData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal binding data: %v", err)
	}

	return &bindData, nil
}

// resolveAccountAssets 解析账号绑定的资产
func (h *Handler) resolveAccountAssets(accountData *events.AccountEventData) ([]string, error) {
	logger.Debugf("Resolving assets for account: rdb_id=%d", accountData.GetID())

	// 从RDB查询账号绑定的资源
	bindings, err := h.getAccountResourceBindings(accountData.GetID())
	if err != nil {
		logger.Errorf("Failed to get account bindings: account_id=%d, error=%v",
			accountData.GetID(), err)
		return nil, err
	}

	logger.Infof("Found %d resource bindings for account: account_id=%d",
		len(bindings), accountData.GetID())

	// 调试：打印绑定详情
	for i, binding := range bindings {
		logger.Infof("Binding %d: resource_id=%d, is_active=%t",
			i+1, binding.ResourceID, binding.IsActive)
	}

	// 获取资源的IP地址
	var resourceIPs []string
	var activeBindings []ResourceBinding

	for _, binding := range bindings {
		if !binding.IsActive {
			logger.Debugf("Skipping inactive binding: account_id=%d, resource_id=%d",
				accountData.GetID(), binding.ResourceID)
			continue
		}

		logger.Infof("Processing binding: resource_id=%d", binding.ResourceID)

		resource, err := h.getResourceFromRDB(binding.ResourceID)
		if err != nil {
			logger.Errorf("Failed to get resource: resource_id=%d, error=%v",
				binding.ResourceID, err)
			continue
		}

		logger.Infof("Resource found: resource_id=%d, name=%s, ident=%s",
			binding.ResourceID, resource.Name, resource.Ident)

		ip := h.extractIPFromResource(resource)
		if ip == "" {
			logger.Errorf("No IP found for resource: resource_id=%d, resource_name=%s",
				binding.ResourceID, resource.Name)
			continue
		}

		logger.Infof("IP extracted: resource_id=%d, ip=%s", binding.ResourceID, ip)

		resourceIPs = append(resourceIPs, ip)
		activeBindings = append(activeBindings, binding)

		logger.Debugf("Resource IP extracted: resource_id=%d, ip=%s",
			binding.ResourceID, ip)
	}

	if len(resourceIPs) == 0 {
		logger.Errorf("No valid resource IPs found for account: account_id=%d",
			accountData.GetID())
		return []string{}, nil
	}

	logger.Infof("Extracted %d resource IPs for account: account_id=%d, ips=%v",
		len(resourceIPs), accountData.GetID(), resourceIPs)

	// 批量查询JumpServer资产
	logger.Infof("Starting batch asset query: account_id=%d, ips=%v", accountData.GetID(), resourceIPs)

	assets, err := h.jsClient.BatchGetAssetsByAddresses(resourceIPs)
	if err != nil {
		logger.Errorf("Failed to batch query assets: ips=%v, error=%v", resourceIPs, err)
		return nil, err
	}

	logger.Infof("Batch asset query completed: account_id=%d, query_results=%d",
		accountData.GetID(), len(assets))

	// 调试：显示查询结果详情
	for ip, asset := range assets {
		if asset != nil {
			logger.Infof("Asset found: ip=%s, asset_id=%s, asset_name=%s",
				ip, asset.ID, asset.Name)
		} else {
			logger.Errorf("No asset found for IP: %s", ip)
		}
	}

	// 记录查询结果的详细信息
	for ip, asset := range assets {
		if asset != nil {
			logger.Infof("Asset query result: ip=%s, found=true, asset_id=%s, asset_name=%s",
				ip, asset.ID, asset.Name)
		} else {
			logger.Errorf("Asset query result: ip=%s, found=false", ip)
		}
	}

	// 提取资产ID并记录映射结果
	var assetIDs []string
	for i, ip := range resourceIPs {
		binding := activeBindings[i]

		logger.Infof("Processing asset mapping: resource_id=%d, ip=%s", binding.ResourceID, ip)

		if asset, exists := assets[ip]; exists && asset != nil {
			assetIDs = append(assetIDs, asset.ID)
			logger.Infof("Asset mapping successful: resource_id=%d, ip=%s, asset_id=%s, asset_name=%s",
				binding.ResourceID, ip, asset.ID, asset.Name)
		} else {
			logger.Errorf("Asset mapping failed: resource_id=%d, ip=%s, exists=%t, asset_nil=%t",
				binding.ResourceID, ip, exists, asset == nil)
		}
	}

	logger.Infof("Asset resolution completed: account_id=%d, total_resources=%d, mapped_assets=%d",
		accountData.GetID(), len(resourceIPs), len(assetIDs))

	return assetIDs, nil
}

// createAccountForAsset 为指定资产创建账号
func (h *Handler) createAccountForAsset(accountData *events.AccountEventData, assetID string) error {
	logger.Debugf("Creating account for asset: rdb_id=%d, asset_id=%s",
		accountData.GetID(), assetID)

	// 映射账号数据
	jsAccount, err := h.mapAccountToJS(accountData, assetID)
	if err != nil {
		logger.Errorf("Failed to map account data: rdb_id=%d, asset_id=%s, error=%v",
			accountData.GetID(), assetID, err)
		return err
	}

	logger.Debugf("Mapped account data: rdb_id=%d, asset_id=%s, js_account=%+v",
		accountData.GetID(), assetID, jsAccount)

	// 检查是否已存在
	existing, err := h.jsClient.GetAccounts(jsAccount.Name, assetID)
	if err != nil {
		logger.Errorf("Failed to check existing account: name=%s, asset_id=%s, error=%v",
			jsAccount.Name, assetID, err)
		return err
	}

	if len(existing) > 0 {
		logger.Infof("Account already exists, updating: name=%s, asset_id=%s, js_id=%s",
			jsAccount.Name, assetID, existing[0].ID)
		return h.updateExistingAccount(existing[0].ID, jsAccount)
	}

	// 创建新账号
	created, err := h.jsClient.CreateAccount(jsAccount)
	if err != nil {
		logger.Errorf("Failed to create account in JumpServer: rdb_id=%d, asset_id=%s, name=%s, error=%v",
			accountData.GetID(), assetID, jsAccount.Name, err)
		return err
	}

	logger.Infof("Account created successfully: rdb_id=%d, js_id=%s, asset_id=%s, name=%s",
		accountData.GetID(), created.ID, assetID, jsAccount.Name)
	return nil
}

// updateAccountForAsset 为指定资产更新账号
func (h *Handler) updateAccountForAsset(accountData *events.AccountEventData, assetID string) error {
	logger.Debugf("Updating account for asset: rdb_id=%d, asset_id=%s",
		accountData.GetID(), assetID)

	// 查找现有账号 - 使用更可靠的查找策略
	existing, err := h.findExistingAccountForUpdate(accountData, assetID)
	if err != nil {
		logger.Errorf("Failed to query existing account: error=%v", err)
		return err
	}

	if len(existing) == 0 {
		logger.Infof("Account not found, creating new one: name=%s, username=%s, asset_id=%s",
			accountData.Name, accountData.Username, assetID)
		return h.createAccountForAsset(accountData, assetID)
	}

	logger.Infof("Found existing account for update: js_id=%s, current_name=%s, new_name=%s, username=%s",
		existing[0].ID, existing[0].Name, accountData.Name, accountData.Username)

	// 映射账号数据
	jsAccount, err := h.mapAccountToJS(accountData, assetID)
	if err != nil {
		logger.Errorf("Failed to map account data for update: rdb_id=%d, asset_id=%s, error=%v",
			accountData.GetID(), assetID, err)
		return err
	}

	// 更新账号
	return h.updateExistingAccount(existing[0].ID, jsAccount)
}

// updateExistingAccount 更新现有账号
func (h *Handler) updateExistingAccount(jsID string, jsAccount *jumpserver.Account) error {
	logger.Debugf("Updating existing account: js_id=%s, js_account=%+v", jsID, jsAccount)

	updated, err := h.jsClient.UpdateAccount(jsID, jsAccount)
	if err != nil {
		logger.Errorf("Failed to update account: js_id=%s, error=%v", jsID, err)
		return fmt.Errorf("failed to update account: %v", err)
	}

	logger.Infof("Account updated successfully: js_id=%s, name=%s, asset=%s",
		updated.ID, updated.Name, updated.Asset)
	return nil
}

// createOrUpdateAccountForAsset 创建或更新账号
func (h *Handler) createOrUpdateAccountForAsset(accountData *events.AccountEventData, assetID string) error {
	// 检查是否已存在 - 使用更可靠的查找策略
	existing, err := h.findExistingAccountForUpdate(accountData, assetID)
	if err != nil {
		logger.Errorf("Failed to check existing account: username=%s, asset_id=%s, error=%v",
			accountData.Username, assetID, err)
		return err
	}

	if len(existing) > 0 {
		logger.Infof("Account exists, updating: current_name=%s, new_name=%s, username=%s, asset_id=%s, js_id=%s",
			existing[0].Name, accountData.Name, accountData.Username, assetID, existing[0].ID)
		return h.updateAccountForAsset(accountData, assetID)
	} else {
		logger.Infof("Account not found, creating: name=%s, username=%s, asset_id=%s",
			accountData.Name, accountData.Username, assetID)
		return h.createAccountForAsset(accountData, assetID)
	}
}

// mapAccountToJS 映射账号数据到JumpServer格式
func (h *Handler) mapAccountToJS(accountData *events.AccountEventData, assetID string) (*jumpserver.Account, error) {
	jsAccount := &jumpserver.Account{
		Name:        accountData.Name,
		Username:    accountData.Username,
		Privileged:  accountData.GetPrivileged(),
		IsActive:    accountData.GetIsActive(),
		SecretReset: accountData.GetSecretReset(),
		PushNow:     accountData.GetPushNow(),
		Comment:     accountData.Comment,
	}

	// 设置资产ID
	jsAccount.SetAssetID(assetID)

	// 设置来源
	jsAccount.SetSource(accountData.Source)

	// 设置密文类型
	jsAccount.SetSecretType(accountData.SecretType)

	// 处理密文
	if accountData.Secret != "" {
		jsAccount.Secret = accountData.Secret
	}
	if accountData.Passphrase != "" {
		jsAccount.Passphrase = accountData.Passphrase
	}

	// 处理模板引用
	if accountData.GetTemplateID() > 0 {
		templateName, err := h.getTemplateNameByID(accountData.GetTemplateID())
		if err != nil {
			logger.Errorf("Failed to resolve template name: template_id=%d, error=%v",
				accountData.GetTemplateID(), err)
		} else {
			// 查找JumpServer中的模板
			templates, err := h.jsClient.GetAccountTemplates(templateName)
			if err == nil && len(templates) > 0 {
				jsAccount.Template = templates[0].ID
				logger.Debugf("Template resolved: rdb_template_id=%d, js_template_id=%s",
					accountData.GetTemplateID(), templates[0].ID)
			} else {
				logger.Errorf("Failed to find template in JumpServer: name=%s, error=%v",
					templateName, err)
			}
		}
	} else {
		logger.Debugf("No template reference for account: rdb_id=%d", accountData.GetID())
	}

	logger.Debugf("Mapped account to JumpServer format: name=%s, username=%s, asset=%s, template=%s",
		jsAccount.Name, jsAccount.Username, jsAccount.Asset, jsAccount.Template)

	return jsAccount, nil
}

// 数据库查询方法

// ResourceBinding 资源绑定结构
type ResourceBinding struct {
	ID         int64 `json:"id"`
	AccountID  int64 `json:"account_id"`
	ResourceID int64 `json:"resource_id"`
	IsActive   bool  `json:"is_active"`
}

// Resource 资源结构
type Resource struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	Ident      string `json:"ident"`
	Labels     string `json:"labels"`
	SourceID   int64  `json:"source_id"`
	SourceType string `json:"source_type"`
}

// getAccountResourceBindings 获取账号的资源绑定关系
func (h *Handler) getAccountResourceBindings(accountID int64) ([]ResourceBinding, error) {
	query := `
		SELECT id, account_id, resource_id, is_active
		FROM account_resource_binding
		WHERE account_id = ? AND is_active = 1
	`

	rows, err := h.db.Query(query, accountID)
	if err != nil {
		return nil, fmt.Errorf("failed to query bindings: %v", err)
	}
	defer rows.Close()

	var bindings []ResourceBinding
	for rows.Next() {
		var binding ResourceBinding
		if err := rows.Scan(&binding.ID, &binding.AccountID, &binding.ResourceID, &binding.IsActive); err != nil {
			logger.Errorf("Failed to scan binding row: %v", err)
			continue
		}
		bindings = append(bindings, binding)
	}

	return bindings, nil
}

// getResourceFromRDB 从RDB获取资源信息
func (h *Handler) getResourceFromRDB(resourceID int64) (*Resource, error) {
	query := `
		SELECT id, name, ident, labels, source_id, source_type
		FROM resource
		WHERE id = ?
	`

	var resource Resource
	err := h.db.QueryRow(query, resourceID).Scan(
		&resource.ID, &resource.Name, &resource.Ident, &resource.Labels,
		&resource.SourceID, &resource.SourceType)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("resource not found: id=%d", resourceID)
		}
		return nil, fmt.Errorf("failed to query resource: %v", err)
	}

	return &resource, nil
}

// getAccountFromRDB 从RDB获取账号信息
func (h *Handler) getAccountFromRDB(accountID int64) (*events.AccountEventData, error) {
	query := `
		SELECT id, name, username, secret_type, secret, passphrase,
		       template_id, source, privileged, is_active, secret_reset,
		       push_now, comment, creator, created_at, updated_at
		FROM account
		WHERE id = ?
	`

	var account events.AccountEventData
	var createdAt, updatedAt time.Time
	var secret, passphrase, comment, creator sql.NullString

	err := h.db.QueryRow(query, accountID).Scan(
		&account.ID, &account.Name, &account.Username, &account.SecretType,
		&secret, &passphrase, &account.TemplateID, &account.Source,
		&account.Privileged, &account.IsActive, &account.SecretReset,
		&account.PushNow, &comment, &creator, &createdAt, &updatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("account not found: id=%d", accountID)
		}
		return nil, fmt.Errorf("failed to query account: %v", err)
	}

	// 处理可能为NULL的字段
	if secret.Valid {
		account.Secret = secret.String
	}
	if passphrase.Valid {
		account.Passphrase = passphrase.String
	}
	if comment.Valid {
		account.Comment = comment.String
	}
	if creator.Valid {
		account.Creator = creator.String
	}

	account.CreatedAt = createdAt.Unix()
	account.UpdatedAt = updatedAt.Unix()

	return &account, nil
}

// findExistingAccountForUpdate 为账号更新查找现有账号
func (h *Handler) findExistingAccountForUpdate(accountData *events.AccountEventData, assetID string) ([]jumpserver.Account, error) {
	// 策略1：如果有原始账号信息，优先根据原始名称查找
	if accountData.OriginalName != "" {
		logger.Debugf("Searching account by original name: original_name=%s, asset_id=%s",
			accountData.OriginalName, assetID)

		existing, err := h.jsClient.GetAccounts(accountData.OriginalName, assetID)
		if err != nil {
			logger.Errorf("Failed to query by original name: %v", err)
		} else if len(existing) > 0 {
			logger.Infof("Found account by original name: js_id=%s, name=%s",
				existing[0].ID, existing[0].Name)
			return existing, nil
		}
	}

	// 策略2：根据当前名称查找
	logger.Debugf("Searching account by current name: name=%s, asset_id=%s",
		accountData.Name, assetID)

	existing, err := h.jsClient.GetAccounts(accountData.Name, assetID)
	if err != nil {
		logger.Errorf("Failed to query by current name: %v", err)
	} else if len(existing) > 0 {
		logger.Infof("Found account by current name: js_id=%s, name=%s",
			existing[0].ID, existing[0].Name)
		return existing, nil
	}

	// 策略3：根据用户名查找（但要小心，可能匹配到多个）
	logger.Debugf("Searching account by username: username=%s, asset_id=%s",
		accountData.Username, assetID)

	allAccounts, err := h.jsClient.GetAccounts("", assetID)
	if err != nil {
		return nil, fmt.Errorf("failed to query all accounts for asset %s: %v", assetID, err)
	}

	// 查找匹配的用户名
	var matchingAccounts []jumpserver.Account
	for _, account := range allAccounts {
		if account.Username == accountData.Username {
			matchingAccounts = append(matchingAccounts, account)
		}
	}

	if len(matchingAccounts) == 1 {
		logger.Infof("Found unique account by username: js_id=%s, name=%s, username=%s",
			matchingAccounts[0].ID, matchingAccounts[0].Name, matchingAccounts[0].Username)
		return matchingAccounts, nil
	} else if len(matchingAccounts) > 1 {
		logger.Errorf("Found multiple accounts with same username, skipping update to avoid conflicts: username=%s, count=%d",
			accountData.Username, len(matchingAccounts))
		return nil, nil
	}

	logger.Infof("No existing account found for update")
	return nil, nil
}

// getTemplateNameByID 根据模板ID获取模板名称
func (h *Handler) getTemplateNameByID(templateID int64) (string, error) {
	query := `SELECT name FROM account_template WHERE id = ?`

	var name string
	err := h.db.QueryRow(query, templateID).Scan(&name)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("template not found: id=%d", templateID)
		}
		return "", fmt.Errorf("failed to query template: %v", err)
	}

	return name, nil
}

// getResourceIP 获取资源的IP地址
func (h *Handler) getResourceIP(resourceID int64) (string, error) {
	resource, err := h.getResourceFromRDB(resourceID)
	if err != nil {
		return "", err
	}

	return h.extractIPFromResource(resource), nil
}

// extractIPFromResource 从资源中提取IP地址
func (h *Handler) extractIPFromResource(resource *Resource) string {
	logger.Debugf("Extracting IP from resource: id=%d, ident=%s, name=%s, labels=%s, source_id=%d, source_type=%s",
		resource.ID, resource.Ident, resource.Name, resource.Labels, resource.SourceID, resource.SourceType)

	// 1. 优先从ident字段获取IP
	if resource.Ident != "" && h.isValidIP(resource.Ident) {
		logger.Debugf("Found IP in ident field: resource_id=%d, ip=%s", resource.ID, resource.Ident)
		return resource.Ident
	}

	// 2. 从name字段获取IP（有些情况下name就是IP）
	if resource.Name != "" && h.isValidIP(resource.Name) {
		logger.Debugf("Found IP in name field: resource_id=%d, ip=%s", resource.ID, resource.Name)
		return resource.Name
	}

	// 3. 如果是主机资源，通过source_id查询AMS主机表获取IP
	if resource.SourceType == "host" && resource.SourceID > 0 {
		logger.Infof("Querying AMS host table for IP: resource_id=%d, source_id=%d",
			resource.ID, resource.SourceID)

		ip, err := h.getHostIPFromAMS(resource.SourceID)
		if err != nil {
			logger.Errorf("Failed to get host IP from AMS: resource_id=%d, source_id=%d, error=%v",
				resource.ID, resource.SourceID, err)
		} else if ip != "" && h.isValidIP(ip) {
			logger.Infof("Found IP from AMS host table: resource_id=%d, source_id=%d, ip=%s",
				resource.ID, resource.SourceID, ip)
			return ip
		}
	}

	// 4. 从labels JSON中提取IP
	if resource.Labels != "" {
		// 尝试解析为JSON
		var labels map[string]interface{}
		if err := json.Unmarshal([]byte(resource.Labels), &labels); err == nil {
			// 检查常见的IP字段
			ipFields := []string{"ip", "address", "host", "hostname", "server"}
			for _, field := range ipFields {
				if ip, ok := labels[field].(string); ok && h.isValidIP(ip) {
					logger.Debugf("Found IP in labels.%s: resource_id=%d, ip=%s", field, resource.ID, ip)
					return ip
				}
			}
		} else {
			// 如果不是JSON，尝试从逗号分隔的标签中提取IP
			// 格式如: "ip=*******,region=bj,os=linux"
			pairs := strings.Split(resource.Labels, ",")
			for _, pair := range pairs {
				kv := strings.Split(strings.TrimSpace(pair), "=")
				if len(kv) == 2 {
					key := strings.TrimSpace(kv[0])
					value := strings.TrimSpace(kv[1])
					if (key == "ip" || key == "address" || key == "host") && h.isValidIP(value) {
						logger.Debugf("Found IP in labels key-value: resource_id=%d, key=%s, ip=%s",
							resource.ID, key, value)
						return value
					}
				}
			}
		}
	}

	logger.Errorf("No valid IP found for resource: id=%d, ident=%s, name=%s, labels=%s",
		resource.ID, resource.Ident, resource.Name, resource.Labels)
	return ""
}

// getHostIPFromAMS 从AMS数据库查询主机IP地址
func (h *Handler) getHostIPFromAMS(hostID int64) (string, error) {
	// 检查AMS数据库是否可用
	if models.DB["ams"] == nil {
		return "", fmt.Errorf("AMS database not connected")
	}

	// 使用XORM查询AMS数据库
	var host models.Host
	has, err := models.DB["ams"].Where("id = ?", hostID).Get(&host)
	if err != nil {
		return "", fmt.Errorf("failed to query host: %v", err)
	}

	if !has {
		return "", fmt.Errorf("host not found: id=%d", hostID)
	}

	logger.Debugf("Host IP found in AMS: host_id=%d, ip=%s", hostID, host.IP)
	return host.IP, nil
}

// isValidIP 简单的IP地址验证
func (h *Handler) isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}

	for _, part := range parts {
		if num, err := strconv.Atoi(part); err != nil || num < 0 || num > 255 {
			return false
		}
	}

	return true
}
