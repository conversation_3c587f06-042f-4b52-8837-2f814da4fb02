// Package main JumpServer同步服务
//
//	@title			JumpServer同步服务API
//	@version		1.0.0
//	@description	JumpServer同步服务，负责将Arboris系统中的资产信息同步到JumpServer堡垒机系统
//	@description	提供死信队列管理、系统监控、健康检查等功能
//
//	@contact.name	Arboris Team
//	@contact.email	<EMAIL>
//
//	@license.name	MIT
//	@license.url	https://opensource.org/licenses/MIT
//
//	@BasePath	/
//
//	@tag.name			Health
//	@tag.description	健康检查和服务信息接口
//
//	@tag.name			Admin
//	@tag.description	管理接口，包括死信队列管理和系统状态监控
//
//	@tag.name			DeadLetter
//	@tag.description	死信队列管理接口，用于处理同步失败的消息
//
//	@tag.name			System
//	@tag.description	系统状态和监控接口
//
//	@securityDefinitions.apikey	ApiKeyAuth
//	@in							header
//	@name						Authorization
//	@description				API密钥认证，格式：Bearer {token}
package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/go-redis/redis/v8"
	_ "github.com/go-sql-driver/mysql"
	"github.com/toolkits/pkg/logger"

	"arboris/src/common/loggeri"
	"arboris/src/models"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/modules/jumpserver-sync/deadletter"
	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/http"
	"arboris/src/modules/jumpserver-sync/sync"
)

var (
	vers *bool
	help *bool
	conf *string

	version = "1.0.0"
)

func init() {
	vers = flag.Bool("v", false, "display the version.")
	help = flag.Bool("h", false, "print this help.")
	conf = flag.String("f", "", "specify configuration file.")
	flag.Parse()

	if *vers {
		fmt.Println("Version:", version)
		os.Exit(0)
	}

	if *help {
		flag.Usage()
		os.Exit(0)
	}
}

func main() {
	parseConf()

	// 初始化logger（与RDB风格保持一致）
	loggerConfig := loggeri.Config{
		Dir:       config.Config.Logger.Dir,
		Level:     config.Config.Logger.Level,
		KeepHours: config.Config.Logger.KeepHours,
	}
	loggeri.Init(loggerConfig)

	logger.Info("Starting JumpServer sync service")

	// 初始化数据库连接（使用RDB风格）
	models.InitMySQL("rdb", "ams")

	// 创建同步处理器
	handler, err := sync.NewHandler(config.Config)
	if err != nil {
		logger.Errorf("Failed to create sync handler: %v", err)
		os.Exit(1)
	}

	// 创建Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr:     config.Config.Redis.Addr,
		Password: config.Config.Redis.Password,
		DB:       config.Config.Redis.DB,
	})

	// 创建死信队列
	dlq := deadletter.NewQueue(redisClient, config.Config.Sync.Consumer.Stream)

	// 创建重试配置
	retryConfig := &events.RetryConfig{
		MaxAttempts:     config.Config.Sync.Retry.MaxAttempts,
		InitialInterval: config.Config.Sync.Retry.InitialInterval,
		MaxInterval:     config.Config.Sync.Retry.MaxInterval,
		Multiplier:      config.Config.Sync.Retry.Multiplier,
		EnableDLQ:       config.Config.Sync.Retry.EnableDLQ,
	}

	// 创建Redis Streams消费者
	consumer, err := events.NewConsumer(
		config.Config.Redis.Addr,
		config.Config.Redis.Password,
		config.Config.Redis.DB,
		config.Config.Sync.Consumer.Stream,
		config.Config.Sync.Consumer.Group,
		config.Config.Sync.Consumer.Consumer,
		int64(config.Config.Sync.Consumer.BatchSize),
		config.Config.Sync.Consumer.PollInterval,
		handler,
	)
	if err != nil {
		logger.Errorf("Failed to create event consumer: %v", err)
		os.Exit(1)
	}

	// 创建错误分类器
	errorClassifier := events.NewErrorClassifier(
		config.Config.Sync.ErrorHandling.RetryPatterns,
		config.Config.Sync.ErrorHandling.DLQPatterns,
		config.Config.Sync.ErrorHandling.DiscardPatterns,
	)

	// 设置重试配置、死信队列和错误分类器
	consumer.SetRetryConfig(retryConfig)
	consumer.SetDeadLetterQueue(dlq)
	consumer.SetErrorClassifier(errorClassifier)

	// 创建管理接口处理器
	adminHandler := http.NewAdminHandler(dlq)
	http.SetAdminHandler(adminHandler)

	// 启动HTTP服务器（用于健康检查和监控）
	http.Start()

	// 启动消费者
	consumer.Start()
	logger.Info("Event consumer started")

	// 等待退出信号
	waitForShutdown(consumer)
}

// parseConf 解析配置文件（与RDB风格保持一致）
func parseConf() {
	if err := config.Parse(); err != nil {
		fmt.Printf("cannot parse configuration file: %v\n", err)
		os.Exit(1)
	}
}

// waitForShutdown 等待关闭信号
func waitForShutdown(consumer *events.Consumer) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)

	sig := <-sigChan
	logger.Infof("Received shutdown signal: %s", sig.String())

	// 关闭消费者
	logger.Info("Stopping event consumer")
	consumer.Stop()

	// 关闭HTTP服务器
	logger.Info("Stopping HTTP server")
	http.Shutdown()

	// 关闭logger
	logger.Close()

	logger.Info("JumpServer sync service stopped")
}
