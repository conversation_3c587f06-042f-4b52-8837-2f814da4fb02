package deadletter

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/toolkits/pkg/logger"

	"arboris/src/modules/jumpserver-sync/events"
)

// Queue 死信队列实现
type Queue struct {
	redis       *redis.Client
	mainStream  string // 主队列名称
	deadStream  string // 死信队列名称
	retryStream string // 重试队列名称
}

// NewQueue 创建死信队列
func NewQueue(redisClient *redis.Client, mainStream string) *Queue {
	return &Queue{
		redis:       redisClient,
		mainStream:  mainStream,
		deadStream:  mainStream + ":dead",
		retryStream: mainStream + ":retry",
	}
}

// SendToDeadLetter 发送消息到死信队列
func (q *Queue) SendToDeadLetter(event *events.Event, originalErr error, retryCount int, streamID string) error {
	deadMessage := events.DeadLetterMessage{
		OriginalEvent: event,
		ErrorMessage:  originalErr.Error(),
		FailedAt:      time.Now().Unix(),
		RetryCount:    retryCount,
		LastError:     originalErr.Error(),
		FailureReason: string(q.classifyError(originalErr)),
		StreamID:      streamID,
	}

	// 序列化为JSON
	data, err := json.Marshal(deadMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal dead letter message: %v", err)
	}

	// 发送到死信队列
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result := q.redis.XAdd(ctx, &redis.XAddArgs{
		Stream: q.deadStream,
		Values: map[string]interface{}{
			"data":        string(data),
			"event_type":  string(event.Type), // 转换为字符串
			"event_id":    event.ID,
			"failed_at":   time.Now().Format("2006-01-02 15:04:05"),
			"retry_count": retryCount,
			"error_type":  string(q.classifyError(originalErr)),
			"stream_id":   streamID,
		},
	})

	if err := result.Err(); err != nil {
		return fmt.Errorf("failed to send message to dead letter queue: %v", err)
	}

	logger.Infof("Message sent to dead letter queue: event_id=%s, stream_id=%s, error=%s",
		event.ID, streamID, originalErr.Error())

	return nil
}

// ListDeadMessages 列出死信队列中的消息
func (q *Queue) ListDeadMessages(limit int, offset int) ([]events.DeadLetterMessage, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 计算范围
	start := fmt.Sprintf("%d", offset)
	if offset == 0 {
		start = "-"
	}

	count := int64(limit)
	if limit <= 0 {
		count = 50 // 默认限制
	}

	// 从死信队列读取消息
	result, err := q.redis.XRevRangeN(ctx, q.deadStream, "+", start, count).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to read from dead letter queue: %v", err)
	}

	var messages []events.DeadLetterMessage
	for _, msg := range result {
		if dataStr, ok := msg.Values["data"].(string); ok {
			var deadMsg events.DeadLetterMessage
			if err := json.Unmarshal([]byte(dataStr), &deadMsg); err == nil {
				messages = append(messages, deadMsg)
			} else {
				logger.Warningf("Failed to parse dead letter message %s: %v", msg.ID, err)
			}
		}
	}

	return messages, nil
}

// ListDeadMessageIDs 列出死信队列中所有消息的ID
func (q *Queue) ListDeadMessageIDs() ([]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取所有消息ID
	result, err := q.redis.XRevRangeN(ctx, q.deadStream, "+", "-", 1000).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to read from dead letter queue: %v", err)
	}

	var ids []string
	for _, msg := range result {
		ids = append(ids, msg.ID)
	}

	return ids, nil
}

// GetDeadMessageByStreamID 根据Redis Stream ID获取死信消息
func (q *Queue) GetDeadMessageByStreamID(streamID string) (*events.DeadLetterMessage, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用XRANGE获取特定ID的消息
	result, err := q.redis.XRange(ctx, q.deadStream, streamID, streamID).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to read from dead letter queue: %v", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("message with stream ID %s not found", streamID)
	}

	msg := result[0]
	if dataStr, ok := msg.Values["data"].(string); ok {
		var deadMsg events.DeadLetterMessage
		if err := json.Unmarshal([]byte(dataStr), &deadMsg); err != nil {
			return nil, fmt.Errorf("failed to parse dead letter message: %v", err)
		}
		return &deadMsg, nil
	}

	return nil, fmt.Errorf("invalid message format for stream ID %s", streamID)
}

// GetDeadMessageByID 根据ID获取死信消息
func (q *Queue) GetDeadMessageByID(messageID string) (*events.DeadLetterMessage, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := q.redis.XRange(ctx, q.deadStream, messageID, messageID).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get message from dead letter queue: %v", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("message not found in dead letter queue: %s", messageID)
	}

	msg := result[0]
	dataStr, ok := msg.Values["data"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message format")
	}

	var deadMsg events.DeadLetterMessage
	if err := json.Unmarshal([]byte(dataStr), &deadMsg); err != nil {
		return nil, fmt.Errorf("failed to parse dead letter message: %v", err)
	}

	return &deadMsg, nil
}

// ReprocessMessage 重新处理死信队列中的消息
func (q *Queue) ReprocessMessage(messageID string) error {
	// 1. 从死信队列读取消息
	deadMsg, err := q.GetDeadMessageByID(messageID)
	if err != nil {
		return err
	}

	// 2. 重新发送到主队列
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构造与原始事件相同的消息格式
	values := map[string]interface{}{
		"id":        deadMsg.OriginalEvent.ID,
		"type":      string(deadMsg.OriginalEvent.Type),
		"source":    deadMsg.OriginalEvent.Source,
		"timestamp": deadMsg.OriginalEvent.Timestamp,

		// 添加重新处理的元数据
		"metadata.reprocessed":          "true",
		"metadata.original_failed_at":   deadMsg.FailedAt,
		"metadata.original_retry_count": deadMsg.RetryCount,
		"metadata.original_stream_id":   deadMsg.StreamID,
	}

	// 添加原始事件的数据字段
	for key, value := range deadMsg.OriginalEvent.Data {
		dataKey := fmt.Sprintf("data.%s", key)
		// 如果值是复杂对象，序列化为JSON
		if jsonData, err := json.Marshal(value); err == nil {
			values[dataKey] = string(jsonData)
		} else {
			values[dataKey] = value
		}
	}

	// 添加原始事件的元数据字段
	for key, value := range deadMsg.OriginalEvent.Metadata {
		metaKey := fmt.Sprintf("metadata.%s", key)
		values[metaKey] = value
	}

	result := q.redis.XAdd(ctx, &redis.XAddArgs{
		Stream: q.mainStream,
		Values: values,
	})

	if err := result.Err(); err != nil {
		return fmt.Errorf("failed to reprocess message: %v", err)
	}

	// 3. 从死信队列删除消息
	delResult := q.redis.XDel(ctx, q.deadStream, messageID)
	if err := delResult.Err(); err != nil {
		logger.Warningf("Failed to delete message from dead letter queue: %v", err)
		// 不返回错误，因为消息已经重新处理了
	}

	logger.Infof("Message reprocessed successfully: message_id=%s, event_id=%s",
		messageID, deadMsg.OriginalEvent.ID)

	return nil
}

// ReprocessMessageByStreamID 根据Stream ID重新处理死信队列中的消息
func (q *Queue) ReprocessMessageByStreamID(streamID string) error {
	// 1. 从死信队列读取消息
	deadMsg, err := q.GetDeadMessageByStreamID(streamID)
	if err != nil {
		return err
	}

	// 2. 重新发送到主队列
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构造与原始事件相同的消息格式
	values := map[string]interface{}{
		"id":        deadMsg.OriginalEvent.ID,
		"type":      string(deadMsg.OriginalEvent.Type),
		"source":    deadMsg.OriginalEvent.Source,
		"timestamp": deadMsg.OriginalEvent.Timestamp,

		// 添加重新处理的元数据
		"metadata.reprocessed":          "true",
		"metadata.original_failed_at":   deadMsg.FailedAt,
		"metadata.original_retry_count": deadMsg.RetryCount,
		"metadata.original_stream_id":   deadMsg.StreamID,
	}

	// 添加原始事件的数据字段
	for key, value := range deadMsg.OriginalEvent.Data {
		dataKey := fmt.Sprintf("data.%s", key)
		// 如果值是复杂对象，序列化为JSON
		if jsonData, err := json.Marshal(value); err == nil {
			values[dataKey] = string(jsonData)
		} else {
			// 如果序列化失败，直接使用字符串表示
			values[dataKey] = fmt.Sprintf("%v", value)
		}
	}

	// 添加原始事件的元数据字段
	for key, value := range deadMsg.OriginalEvent.Metadata {
		metaKey := fmt.Sprintf("metadata.%s", key)
		values[metaKey] = value
	}

	// 发送到主队列
	result := q.redis.XAdd(ctx, &redis.XAddArgs{
		Stream: q.mainStream,
		Values: values,
	})

	if err := result.Err(); err != nil {
		return fmt.Errorf("failed to reprocess message to main queue: %v", err)
	}

	// 3. 从死信队列删除消息
	if err := q.DeleteMessageByStreamID(streamID); err != nil {
		logger.Warningf("Failed to delete reprocessed message from dead letter queue: %v", err)
		// 不返回错误，因为消息已经成功重新处理
	}

	logger.Infof("Message reprocessed successfully: stream_id=%s, event_id=%s",
		streamID, deadMsg.OriginalEvent.ID)

	return nil
}

// BatchReprocessMessages 批量重新处理死信消息
func (q *Queue) BatchReprocessMessages(messageIDs []string) (int, []string, error) {
	var successCount int
	var failedIDs []string

	for _, messageID := range messageIDs {
		if err := q.ReprocessMessage(messageID); err != nil {
			logger.Errorf("Failed to reprocess message %s: %v", messageID, err)
			failedIDs = append(failedIDs, messageID)
		} else {
			successCount++
		}
	}

	return successCount, failedIDs, nil
}

// DeleteMessage 删除死信队列中的消息
func (q *Queue) DeleteMessage(messageID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result := q.redis.XDel(ctx, q.deadStream, messageID)
	if err := result.Err(); err != nil {
		return fmt.Errorf("failed to delete message from dead letter queue: %v", err)
	}

	deletedCount := result.Val()
	if deletedCount == 0 {
		return fmt.Errorf("message not found: %s", messageID)
	}

	logger.Infof("Message deleted from dead letter queue: message_id=%s", messageID)
	return nil
}

// DeleteMessageByStreamID 根据Stream ID删除死信队列中的消息
func (q *Queue) DeleteMessageByStreamID(streamID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result := q.redis.XDel(ctx, q.deadStream, streamID)
	if err := result.Err(); err != nil {
		return fmt.Errorf("failed to delete message from dead letter queue: %v", err)
	}

	deletedCount := result.Val()
	if deletedCount == 0 {
		return fmt.Errorf("message not found: %s", streamID)
	}

	logger.Infof("Message deleted from dead letter queue: stream_id=%s", streamID)
	return nil
}

// GetStats 获取死信队列统计信息
func (q *Queue) GetStats() (map[string]interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 获取死信队列长度
	lenResult := q.redis.XLen(ctx, q.deadStream)
	totalCount := lenResult.Val()

	// 获取最近的消息进行错误类型统计
	recentResult, err := q.redis.XRevRangeN(ctx, q.deadStream, "+", "-", 100).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get recent messages: %v", err)
	}

	// 统计错误类型
	errorTypeStats := make(map[string]int)
	eventTypeStats := make(map[string]int)

	for _, msg := range recentResult {
		if errorType, ok := msg.Values["error_type"].(string); ok {
			errorTypeStats[errorType]++
		}
		if eventType, ok := msg.Values["event_type"].(string); ok {
			eventTypeStats[eventType]++
		}
	}

	stats := map[string]interface{}{
		"total_count":      totalCount,
		"error_type_stats": errorTypeStats,
		"event_type_stats": eventTypeStats,
		"stream_name":      q.deadStream,
		"last_updated":     time.Now().Unix(),
	}

	return stats, nil
}

// classifyError 分类错误（内部使用）
func (q *Queue) classifyError(err error) events.ErrorType {
	classifier := &ErrorClassifier{}
	return classifier.ClassifyError(err)
}

// ErrorClassifier 错误分类器（简化版本）
type ErrorClassifier struct{}

// ClassifyError 分类错误
func (ec *ErrorClassifier) ClassifyError(err error) events.ErrorType {
	if err == nil {
		return events.ErrorTypeUnknown
	}

	errStr := err.Error()

	// 简单的错误分类逻辑
	if contains(errStr, "timeout", "deadline") {
		return events.ErrorTypeTimeout
	}
	if contains(errStr, "connection", "network", "dial") {
		return events.ErrorTypeNetwork
	}
	if contains(errStr, "401", "403", "unauthorized", "forbidden") {
		return events.ErrorTypeAuth
	}
	if contains(errStr, "404", "not found") {
		return events.ErrorTypeNotFound
	}
	if contains(errStr, "400", "validation", "invalid") {
		return events.ErrorTypeValidation
	}
	if contains(errStr, "500", "502", "503", "504") {
		return events.ErrorTypeServer
	}

	return events.ErrorTypeUnknown
}

// contains 检查字符串是否包含任一关键词
func contains(str string, keywords ...string) bool {
	for _, keyword := range keywords {
		if len(str) >= len(keyword) {
			for i := 0; i <= len(str)-len(keyword); i++ {
				if str[i:i+len(keyword)] == keyword {
					return true
				}
			}
		}
	}
	return false
}
