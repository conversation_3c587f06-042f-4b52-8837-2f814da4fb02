package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端 - 请根据实际环境修改URL和认证信息
	client := jumpserver.NewClient("http://localhost:8080", "admin", "admin")

	fmt.Println("=== 测试节点创建修复 ===")

	// 测试1：创建父节点
	fmt.Println("\n1. 创建父节点...")
	parentNode := &jumpserver.Node{
		Key:   "test-fix-parent",
		Value: "修复测试父节点",
	}

	createdParent, err := client.CreateNode(parentNode)
	if err != nil {
		log.Fatalf("创建父节点失败: %v", err)
	}
	fmt.Printf("✓ 父节点创建成功: ID=%s, Value=%s\n", createdParent.ID, createdParent.Value)

	// 测试2：创建第一个子节点
	fmt.Println("\n2. 创建第一个子节点...")
	child1 := &jumpserver.Node{
		Key:    "child1",
		Value:  "子节点1",
		Parent: createdParent.ID,
	}

	createdChild1, err := client.CreateNode(child1)
	if err != nil {
		log.Fatalf("创建第一个子节点失败: %v", err)
	}
	fmt.Printf("✓ 第一个子节点创建成功: ID=%s, Value=%s\n", createdChild1.ID, createdChild1.Value)

	// 测试3：创建第二个子节点
	fmt.Println("\n3. 创建第二个子节点...")
	child2 := &jumpserver.Node{
		Key:    "child2",
		Value:  "子节点2",
		Parent: createdParent.ID,
	}

	createdChild2, err := client.CreateNode(child2)
	if err != nil {
		log.Fatalf("创建第二个子节点失败: %v", err)
	}
	fmt.Printf("✓ 第二个子节点创建成功: ID=%s, Value=%s\n", createdChild2.ID, createdChild2.Value)

	// 测试4：验证子节点列表
	fmt.Println("\n4. 验证子节点列表...")
	children, err := client.GetNodeChildren(createdParent.ID)
	if err != nil {
		log.Fatalf("获取子节点列表失败: %v", err)
	}

	fmt.Printf("✓ 父节点下共有 %d 个子节点:\n", len(children))
	for i, child := range children {
		fmt.Printf("  %d. ID=%s, Key=%s, Value=%s\n", i+1, child.ID, child.Key, child.Value)
	}

	// 测试5：测试多层级节点创建
	fmt.Println("\n5. 测试多层级节点创建...")
	hierarchyPath := "/测试公司/技术部/后端组/核心团队"
	
	finalNode, err := client.CreateNodeHierarchy(hierarchyPath)
	if err != nil {
		log.Fatalf("创建多层级节点失败: %v", err)
	}
	fmt.Printf("✓ 多层级节点创建成功: ID=%s, Value=%s\n", finalNode.ID, finalNode.Value)

	// 测试6：测试同名不同父节点的情况
	fmt.Println("\n6. 测试同名不同父节点...")
	
	// 创建第二个父节点
	parent2 := &jumpserver.Node{
		Key:   "test-fix-parent2",
		Value: "修复测试父节点2",
	}
	createdParent2, err := client.CreateNode(parent2)
	if err != nil {
		log.Fatalf("创建第二个父节点失败: %v", err)
	}

	// 在第二个父节点下创建同名子节点
	sameNameChild := &jumpserver.Node{
		Key:    "same-name",
		Value:  "同名节点",
		Parent: createdParent2.ID,
	}
	createdSameNameChild, err := client.CreateNode(sameNameChild)
	if err != nil {
		log.Fatalf("创建同名子节点失败: %v", err)
	}
	fmt.Printf("✓ 同名子节点创建成功: ID=%s, Value=%s\n", createdSameNameChild.ID, createdSameNameChild.Value)

	// 在第一个父节点下也创建同名子节点
	sameNameChild2 := &jumpserver.Node{
		Key:    "same-name",
		Value:  "同名节点",
		Parent: createdParent.ID,
	}
	createdSameNameChild2, err := client.CreateNode(sameNameChild2)
	if err != nil {
		log.Fatalf("创建第二个同名子节点失败: %v", err)
	}
	fmt.Printf("✓ 第二个同名子节点创建成功: ID=%s, Value=%s\n", createdSameNameChild2.ID, createdSameNameChild2.Value)

	fmt.Println("\n=== 所有测试通过！修复成功！ ===")
	fmt.Println("\n修复内容总结:")
	fmt.Println("1. ✓ 修复了 GetNodeByParentAndValue 方法中的子节点查找逻辑")
	fmt.Println("2. ✓ 现在可以正确创建同一父节点下的多个子节点")
	fmt.Println("3. ✓ 支持创建多层级节点树")
	fmt.Println("4. ✓ 支持在不同父节点下创建同名子节点")
}
