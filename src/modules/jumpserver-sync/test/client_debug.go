package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端 - 请根据实际环境修改URL和认证信息
	client := jumpserver.NewClient("http://10.1.4.213:8090", "admin", "admin")

	fmt.Println("=== JumpServer 客户端调试 ===")

	// 测试1：创建测试节点结构
	fmt.Println("\n1. 创建测试节点结构...")
	
	// 创建根节点
	rootNode := &jumpserver.Node{
		Key:   "debug-root",
		Value: "调试根节点",
	}
	
	createdRoot, err := client.CreateNode(rootNode)
	if err != nil {
		log.Printf("创建根节点失败: %v", err)
		// 尝试查找现有根节点
		existingRoot, findErr := client.GetNodeByParentAndValue("", "调试根节点")
		if findErr != nil {
			log.Fatalf("查找现有根节点也失败: %v", findErr)
		}
		if existingRoot == nil {
			log.Fatalf("根节点不存在且无法创建")
		}
		createdRoot = existingRoot
		fmt.Printf("✓ 使用现有根节点: ID=%s, Value=%s\n", createdRoot.ID, createdRoot.Value)
	} else {
		fmt.Printf("✓ 根节点创建成功: ID=%s, Value=%s\n", createdRoot.ID, createdRoot.Value)
	}

	// 创建子节点
	childNode := &jumpserver.Node{
		Key:    "debug-child",
		Value:  "调试子节点",
		Parent: createdRoot.ID,
	}
	
	createdChild, err := client.CreateNode(childNode)
	if err != nil {
		log.Printf("创建子节点失败: %v", err)
		// 尝试查找现有子节点
		existingChild, findErr := client.GetNodeByParentAndValue(createdRoot.ID, "调试子节点")
		if findErr != nil {
			log.Fatalf("查找现有子节点也失败: %v", findErr)
		}
		if existingChild == nil {
			log.Fatalf("子节点不存在且无法创建")
		}
		createdChild = existingChild
		fmt.Printf("✓ 使用现有子节点: ID=%s, Value=%s\n", createdChild.ID, createdChild.Value)
	} else {
		fmt.Printf("✓ 子节点创建成功: ID=%s, Value=%s\n", createdChild.ID, createdChild.Value)
	}

	// 测试2：查找节点功能
	fmt.Println("\n2. 测试节点查找功能...")
	
	// 查找根节点
	fmt.Println("查找根节点...")
	foundRoot, err := client.GetNodeByParentAndValue("", "调试根节点")
	if err != nil {
		log.Fatalf("查找根节点失败: %v", err)
	}
	if foundRoot == nil {
		log.Fatalf("根节点未找到")
	}
	fmt.Printf("✓ 根节点查找成功: ID=%s, Value=%s\n", foundRoot.ID, foundRoot.Value)

	// 查找子节点
	fmt.Println("查找子节点...")
	foundChild, err := client.GetNodeByParentAndValue(foundRoot.ID, "调试子节点")
	if err != nil {
		log.Fatalf("查找子节点失败: %v", err)
	}
	if foundChild == nil {
		log.Fatalf("子节点未找到")
	}
	fmt.Printf("✓ 子节点查找成功: ID=%s, Value=%s\n", foundChild.ID, foundChild.Value)

	// 测试3：更新节点名称
	fmt.Println("\n3. 测试节点更新功能...")
	
	// 记录原始名称
	originalName := foundChild.Value
	fmt.Printf("原始子节点名称: %s\n", originalName)
	
	// 更新子节点名称
	foundChild.Value = "更新后的调试子节点"
	updatedChild, err := client.UpdateNode(foundChild.ID, foundChild)
	if err != nil {
		log.Fatalf("更新子节点失败: %v", err)
	}
	fmt.Printf("✓ 子节点更新成功: ID=%s, 新名称=%s\n", updatedChild.ID, updatedChild.Value)

	// 测试4：验证更新后的查找
	fmt.Println("\n4. 验证更新后的查找...")
	
	// 用原始名称查找（应该找不到）
	fmt.Printf("用原始名称查找: %s\n", originalName)
	oldNameNode, err := client.GetNodeByParentAndValue(foundRoot.ID, originalName)
	if err != nil {
		log.Printf("用原始名称查找失败: %v", err)
	}
	if oldNameNode != nil {
		log.Printf("❌ 警告：用原始名称仍然能找到节点: ID=%s, Value=%s", oldNameNode.ID, oldNameNode.Value)
	} else {
		fmt.Printf("✓ 用原始名称查找不到节点（正确）\n")
	}

	// 用新名称查找（应该能找到）
	fmt.Printf("用新名称查找: %s\n", updatedChild.Value)
	newNameNode, err := client.GetNodeByParentAndValue(foundRoot.ID, updatedChild.Value)
	if err != nil {
		log.Fatalf("用新名称查找失败: %v", err)
	}
	if newNameNode == nil {
		log.Fatalf("❌ 用新名称找不到节点")
	}
	if newNameNode.ID != updatedChild.ID {
		log.Fatalf("❌ 找到的节点ID不匹配: 期望=%s, 实际=%s", updatedChild.ID, newNameNode.ID)
	}
	fmt.Printf("✓ 用新名称查找成功: ID=%s, Value=%s\n", newNameNode.ID, newNameNode.Value)

	// 测试5：模拟更新事件的查找逻辑
	fmt.Println("\n5. 模拟更新事件的查找逻辑...")
	
	// 模拟：我们有一个节点更新事件，原始名称是 "更新后的调试子节点"，新名称是 "最终调试子节点"
	currentName := "最终调试子节点"
	searchName := updatedChild.Value // 这应该是原始名称
	
	fmt.Printf("模拟更新: 搜索名称=%s, 新名称=%s\n", searchName, currentName)
	
	// 用搜索名称查找现有节点
	existingForUpdate, err := client.GetNodeByParentAndValue(foundRoot.ID, searchName)
	if err != nil {
		log.Fatalf("模拟更新查找失败: %v", err)
	}
	if existingForUpdate == nil {
		log.Fatalf("❌ 模拟更新时找不到现有节点")
	}
	fmt.Printf("✓ 模拟更新查找成功: ID=%s, Value=%s\n", existingForUpdate.ID, existingForUpdate.Value)
	
	// 执行更新
	existingForUpdate.Value = currentName
	finalUpdated, err := client.UpdateNode(existingForUpdate.ID, existingForUpdate)
	if err != nil {
		log.Fatalf("模拟更新执行失败: %v", err)
	}
	fmt.Printf("✓ 模拟更新执行成功: ID=%s, 最终名称=%s\n", finalUpdated.ID, finalUpdated.Value)

	fmt.Println("\n=== 🎉 所有测试通过！客户端功能正常！ ===")
	fmt.Println("\n总结:")
	fmt.Println("✅ 节点创建功能正常")
	fmt.Println("✅ 节点查找功能正常")
	fmt.Println("✅ 节点更新功能正常")
	fmt.Println("✅ 更新后的查找逻辑正常")
	fmt.Println("✅ 模拟更新事件处理正常")
	
	fmt.Println("\n如果 JumpServer 客户端功能正常，问题可能在于:")
	fmt.Println("1. 事件数据解析")
	fmt.Println("2. 父节点查找")
	fmt.Println("3. 节点名称映射")
	fmt.Println("4. OriginalName 字段传递")
}
