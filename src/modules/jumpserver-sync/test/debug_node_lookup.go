package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端
	client := jumpserver.NewClient("http://10.1.4.213:8090", "admin", "admin")

	fmt.Println("=== 调试节点查找问题 ===")

	// 从日志中我们知道父节点ID是某个值，让我们先找到它
	fmt.Println("\n1. 查找所有根节点...")
	rootNodes, err := client.GetNodeChildren("")
	if err != nil {
		log.Fatalf("获取根节点失败: %v", err)
	}

	fmt.Printf("找到 %d 个根节点:\n", len(rootNodes))
	for i, node := range rootNodes {
		fmt.Printf("  %d. ID=%s, Value=%s, Key=%s\n", i+1, node.ID, node.Value, node.Key)
	}

	// 查找可能的父节点（根据日志，应该有一个节点下有 dddddd 和 flsssssst）
	var targetParentID string
	var targetParentName string

	for _, rootNode := range rootNodes {
		children, err := client.GetNodeChildren(rootNode.ID)
		if err != nil {
			continue
		}

		// 检查是否包含 dddddd 和 flsssssst
		hasFirst := false
		hasSecond := false
		for _, child := range children {
			if child.Value == "dddddd" {
				hasFirst = true
			}
			if child.Value == "flsssssst" {
				hasSecond = true
			}
		}

		if hasFirst && hasSecond {
			targetParentID = rootNode.ID
			targetParentName = rootNode.Value
			fmt.Printf("\n✓ 找到目标父节点: ID=%s, Name=%s\n", targetParentID, targetParentName)
			break
		}
	}

	if targetParentID == "" {
		fmt.Println("\n❌ 未找到包含 dddddd 和 flsssssst 的父节点")
		return
	}

	// 获取目标父节点的所有子节点
	fmt.Printf("\n2. 获取父节点 '%s' 的所有子节点...\n", targetParentName)
	children, err := client.GetNodeChildren(targetParentID)
	if err != nil {
		log.Fatalf("获取子节点失败: %v", err)
	}

	fmt.Printf("父节点下有 %d 个子节点:\n", len(children))
	for i, child := range children {
		fmt.Printf("  %d. ID=%s, Value=%s, Key=%s\n", i+1, child.ID, child.Value, child.Key)
		if len(child.Meta) > 0 {
			fmt.Printf("     Meta: %+v\n", child.Meta)
		}
	}

	// 测试查找 flagttttt
	fmt.Printf("\n3. 测试查找节点 'flagttttt'...\n")
	targetNode, err := client.GetNodeByParentAndValue(targetParentID, "flagttttt")
	if err != nil {
		fmt.Printf("❌ 查找失败: %v\n", err)
	} else if targetNode == nil {
		fmt.Printf("❌ 节点不存在\n")
	} else {
		fmt.Printf("✓ 找到节点: ID=%s, Value=%s\n", targetNode.ID, targetNode.Value)
	}

	// 分析可能的匹配
	fmt.Printf("\n4. 分析可能的节点匹配...\n")
	
	// 检查是否有节点可能是我们要找的
	for i, child := range children {
		fmt.Printf("分析子节点 %d: %s\n", i+1, child.Value)
		
		// 检查 Meta 信息
		if rdbIdent, exists := child.Meta["rdb_ident"]; exists {
			fmt.Printf("  - RDB Ident: %s\n", rdbIdent)
			if rdbIdent == "flagopen" {
				fmt.Printf("  ✓ RDB Ident 匹配 'flagopen'!\n")
			}
		}
		
		if rdbPath, exists := child.Meta["rdb_path"]; exists {
			fmt.Printf("  - RDB Path: %s\n", rdbPath)
			if rdbPath == "flagopen.flagopen" {
				fmt.Printf("  ✓ RDB Path 匹配 'flagopen.flagopen'!\n")
			}
		}
		
		if rdbID, exists := child.Meta["rdb_id"]; exists {
			fmt.Printf("  - RDB ID: %s\n", rdbID)
		}
	}

	// 模拟修复后的查找逻辑
	fmt.Printf("\n5. 模拟修复后的查找逻辑...\n")
	
	// 方法1: 通过 rdb_ident 查找
	fmt.Printf("方法1: 通过 rdb_ident='flagopen' 查找...\n")
	for _, child := range children {
		if rdbIdent, exists := child.Meta["rdb_ident"]; exists && rdbIdent == "flagopen" {
			fmt.Printf("✓ 通过 rdb_ident 找到节点: ID=%s, Value=%s\n", child.ID, child.Value)
		}
	}
	
	// 方法2: 通过 rdb_path 查找
	fmt.Printf("方法2: 通过 rdb_path='flagopen.flagopen' 查找...\n")
	for _, child := range children {
		if rdbPath, exists := child.Meta["rdb_path"]; exists && rdbPath == "flagopen.flagopen" {
			fmt.Printf("✓ 通过 rdb_path 找到节点: ID=%s, Value=%s\n", child.ID, child.Value)
		}
	}
	
	// 方法3: 如果只有一个节点
	if len(children) == 1 {
		child := children[0]
		fmt.Printf("方法3: 只有一个子节点，假设就是目标: ID=%s, Value=%s\n", child.ID, child.Value)
	}

	fmt.Println("\n=== 调试完成 ===")
	fmt.Println("\n建议:")
	fmt.Println("1. 检查节点更新事件是否正确处理")
	fmt.Println("2. 确认 RDB 和 JumpServer 中的节点数据是否一致")
	fmt.Println("3. 考虑使用 Meta 信息进行节点匹配")
	fmt.Println("4. 如果节点名称已更新，考虑更新 JumpServer 中的对应节点")
}
