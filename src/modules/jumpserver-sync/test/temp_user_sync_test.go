package test

import (
	"fmt"
	"time"

	"github.com/toolkits/pkg/logger"

	"arboris/src/modules/jumpserver-sync/events"
)

// TestTempUserSync 测试临时用户同步逻辑
func TestTempUserSync() {
	logger.Info("=== 测试临时用户同步逻辑 ===")

	// 测试永久用户
	testPermanentUser()

	// 测试临时用户（未过期）
	testValidTempUser()

	// 测试临时用户（已过期）
	testExpiredTempUser()

	// 测试临时用户转永久用户
	testTempToPermanentUser()

	logger.Info("=== 临时用户同步测试完成 ===")
}

// testPermanentUser 测试永久用户
func testPermanentUser() {
	logger.Info("--- 测试永久用户 ---")

	userData := &events.UserEventData{
		ID:           1001,
		UUID:         "perm-user-uuid-001",
		Username:     "permanent_user",
		Password:     "test123456",
		Dispname:     "永久用户",
		Email:        "<EMAIL>",
		Phone:        "13800138001",
		Organization: "测试组织",
		Status:       0, // 活跃
		IsRoot:       0, // 非管理员
		Type:         0, // 永久用户
		ActiveBegin:  time.Now().Unix(),
		ActiveEnd:    0, // 永不过期
	}

	// 模拟发布用户创建事件
	logger.Infof("创建永久用户: %s", userData.Username)
	events.PublishUserCreateEvent(userData)

	// 模拟用户更新
	userData.Dispname = "永久用户（已更新）"
	userData.Email = "<EMAIL>"
	logger.Infof("更新永久用户: %s", userData.Username)
	events.PublishUserUpdateEvent(userData)
}

// testValidTempUser 测试有效的临时用户
func testValidTempUser() {
	logger.Info("--- 测试有效的临时用户 ---")

	now := time.Now()
	activeBegin := now.Add(-1 * time.Hour).Unix()  // 1小时前开始
	activeEnd := now.Add(24 * time.Hour).Unix()    // 24小时后过期

	userData := &events.UserEventData{
		ID:           1002,
		UUID:         "temp-user-uuid-001",
		Username:     "temp_user_valid",
		Password:     "temp123456",
		Dispname:     "临时用户（有效）",
		Email:        "<EMAIL>",
		Phone:        "13800138002",
		Organization: "测试组织",
		Status:       0, // 活跃
		IsRoot:       0, // 非管理员
		Type:         1, // 临时用户
		ActiveBegin:  activeBegin,
		ActiveEnd:    activeEnd,
	}

	// 模拟发布用户创建事件
	logger.Infof("创建有效临时用户: %s (有效期: %s - %s)", 
		userData.Username,
		time.Unix(activeBegin, 0).Format("2006-01-02 15:04:05"),
		time.Unix(activeEnd, 0).Format("2006-01-02 15:04:05"))
	events.PublishUserCreateEvent(userData)

	// 模拟用户更新
	userData.Dispname = "临时用户（有效，已更新）"
	logger.Infof("更新有效临时用户: %s", userData.Username)
	events.PublishUserUpdateEvent(userData)
}

// testExpiredTempUser 测试已过期的临时用户
func testExpiredTempUser() {
	logger.Info("--- 测试已过期的临时用户 ---")

	now := time.Now()
	activeBegin := now.Add(-48 * time.Hour).Unix() // 48小时前开始
	activeEnd := now.Add(-1 * time.Hour).Unix()    // 1小时前过期

	userData := &events.UserEventData{
		ID:           1003,
		UUID:         "temp-user-uuid-002",
		Username:     "temp_user_expired",
		Password:     "expired123456",
		Dispname:     "临时用户（已过期）",
		Email:        "<EMAIL>",
		Phone:        "13800138003",
		Organization: "测试组织",
		Status:       0, // 活跃（但实际已过期）
		IsRoot:       0, // 非管理员
		Type:         1, // 临时用户
		ActiveBegin:  activeBegin,
		ActiveEnd:    activeEnd,
	}

	// 模拟发布用户创建事件（应该被跳过）
	logger.Infof("尝试创建已过期临时用户: %s (有效期: %s - %s)", 
		userData.Username,
		time.Unix(activeBegin, 0).Format("2006-01-02 15:04:05"),
		time.Unix(activeEnd, 0).Format("2006-01-02 15:04:05"))
	events.PublishUserCreateEvent(userData)

	// 模拟用户更新（应该被禁用）
	userData.Dispname = "临时用户（已过期，尝试更新）"
	logger.Infof("尝试更新已过期临时用户: %s", userData.Username)
	events.PublishUserUpdateEvent(userData)
}

// testTempToPermanentUser 测试临时用户转永久用户
func testTempToPermanentUser() {
	logger.Info("--- 测试临时用户转永久用户 ---")

	now := time.Now()
	activeBegin := now.Add(-1 * time.Hour).Unix()  // 1小时前开始
	activeEnd := now.Add(24 * time.Hour).Unix()    // 24小时后过期

	userData := &events.UserEventData{
		ID:           1004,
		UUID:         "temp-to-perm-uuid-001",
		Username:     "temp_to_permanent",
		Password:     "convert123456",
		Dispname:     "临时转永久用户",
		Email:        "<EMAIL>",
		Phone:        "13800138004",
		Organization: "测试组织",
		Status:       0, // 活跃
		IsRoot:       0, // 非管理员
		Type:         1, // 临时用户
		ActiveBegin:  activeBegin,
		ActiveEnd:    activeEnd,
	}

	// 创建临时用户
	logger.Infof("创建临时用户: %s", userData.Username)
	events.PublishUserCreateEvent(userData)

	// 转换为永久用户
	userData.Type = 0        // 永久用户
	userData.ActiveEnd = 0   // 永不过期
	userData.Dispname = "临时转永久用户（已转换）"
	logger.Infof("将临时用户转换为永久用户: %s", userData.Username)
	events.PublishUserUpdateEvent(userData)
}

// TestExpiredUserChecker 测试过期用户检查器
func TestExpiredUserChecker() {
	logger.Info("=== 测试过期用户检查器 ===")

	// 这个测试需要实际的数据库连接和JumpServer连接
	// 在实际环境中运行时会检查数据库中的过期用户
	logger.Info("过期用户检查器测试需要在实际环境中运行")
	logger.Info("检查器会每小时自动运行一次，查找并禁用过期的临时用户")

	logger.Info("=== 过期用户检查器测试完成 ===")
}

// 运行所有测试
func RunAllTempUserTests() {
	TestTempUserSync()
	TestExpiredUserChecker()
}
