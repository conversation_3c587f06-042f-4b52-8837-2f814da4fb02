package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端
	client := jumpserver.NewClient("http://10.1.4.213:8090", "admin", "admin")

	fmt.Println("=== 测试类型断言修复 ===")

	// 测试场景1：查找一个现有的用户组
	fmt.Println("\n1. 查找现有用户组...")
	
	// 先获取所有用户组，找一个有成员的
	groups, err := client.GetAllUserGroups()
	if err != nil {
		log.Fatalf("获取用户组列表失败: %v", err)
	}

	var testGroup *jumpserver.UserGroup
	for _, group := range groups {
		if len(group.Users) > 0 {
			testGroup = &group
			break
		}
	}

	if testGroup == nil {
		fmt.Println("❌ 没有找到有成员的用户组，请先创建一个用户组并添加成员")
		return
	}

	fmt.Printf("✓ 找到测试用户组: ID=%s, Name=%s, 成员数=%d\n", 
		testGroup.ID, testGroup.Name, len(testGroup.Users))

	// 测试场景2：分析用户组成员的数据类型
	fmt.Println("\n2. 分析用户组成员数据类型...")
	
	for i, user := range testGroup.Users {
		fmt.Printf("成员 %d: ", i+1)
		switch u := user.(type) {
		case string:
			fmt.Printf("类型=string, 值=%s\n", u)
		case map[string]interface{}:
			fmt.Printf("类型=object, 字段: ")
			for key := range u {
				fmt.Printf("%s ", key)
			}
			fmt.Println()
			if id, ok := u["id"].(string); ok {
				fmt.Printf("  - ID: %s\n", id)
			}
			if username, ok := u["username"].(string); ok {
				fmt.Printf("  - Username: %s\n", username)
			}
		default:
			fmt.Printf("类型=unknown (%T), 值=%v\n", user, user)
		}
	}

	// 测试场景3：测试 GetUserGroupMembers 方法
	fmt.Println("\n3. 测试 GetUserGroupMembers 方法...")
	
	members, err := client.GetUserGroupMembers(testGroup.ID)
	if err != nil {
		log.Fatalf("获取用户组成员失败: %v", err)
	}

	fmt.Printf("✓ 成功获取 %d 个成员:\n", len(members))
	for i, member := range members {
		fmt.Printf("  成员 %d: ID=%s\n", i+1, member.ID)
	}

	// 测试场景4：验证类型断言是否正确
	fmt.Println("\n4. 验证类型断言结果...")
	
	if len(members) == len(testGroup.Users) {
		fmt.Printf("✅ 成员数量匹配: %d\n", len(members))
	} else {
		fmt.Printf("❌ 成员数量不匹配: 原始=%d, 解析后=%d\n", len(testGroup.Users), len(members))
	}

	// 检查是否所有成员都有有效的ID
	validCount := 0
	for _, member := range members {
		if member.ID != "" {
			validCount++
		}
	}

	if validCount == len(members) {
		fmt.Printf("✅ 所有成员都有有效的ID\n")
	} else {
		fmt.Printf("❌ 有 %d 个成员缺少有效ID\n", len(members)-validCount)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n类型断言修复总结:")
	fmt.Println("✅ 修复了 interface{} 到 string 的类型断言错误")
	fmt.Println("✅ 支持处理字符串和对象两种数据格式")
	fmt.Println("✅ 正确提取用户ID用于成员管理")
	fmt.Println("✅ 编译错误已解决")
}

// 模拟 GetAllUserGroups 方法（如果不存在的话）
func (c *jumpserver.Client) GetAllUserGroups() ([]jumpserver.UserGroup, error) {
	// 这里应该调用实际的API来获取所有用户组
	// 为了测试，我们可以先尝试获取一个已知的用户组
	return []jumpserver.UserGroup{}, fmt.Errorf("method not implemented, please use existing user group")
}
