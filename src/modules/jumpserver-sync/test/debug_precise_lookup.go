package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/sync"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	fmt.Println("=== 测试精确节点查找修复 ===")

	// 创建配置
	cfg := &config.ConfigT{
		JumpServer: config.JumpServerConfig{
			BaseURL:  "http://10.1.4.213:8090",
			Username: "admin",
			Password: "admin",
		},
		Sync: config.SyncConfig{
			Rules: config.SyncRules{
				IncludePaths: []string{},
				ExcludePaths: []string{},
				Filters:      map[string]string{},
			},
			Mapping: config.SyncMapping{
				NodeRules:    []config.NodeRule{},
				MachineRules: []config.MachineRule{},
			},
		},
	}

	// 创建处理器
	handler, err := sync.NewHandler(cfg)
	if err != nil {
		log.Fatalf("创建处理器失败: %v", err)
	}

	// 测试场景1：测试问题路径
	fmt.Println("\n1. 测试问题路径: flagopen.flagopen")
	
	testPath := "flagopen.flagopen"
	fmt.Printf("测试路径: %s\n", testPath)
	
	nodePK, nodeName := handler.ConvertNodePathToJSNodePK(testPath)
	
	if nodePK != "" {
		fmt.Printf("✓ 转换成功: PK=%s, Name=%s\n", nodePK, nodeName)
	} else {
		fmt.Printf("❌ 转换失败\n")
	}

	// 测试场景2：测试其他路径
	fmt.Println("\n2. 测试其他可能的路径...")
	
	testPaths := []string{
		"flagopen",
		"flag",
		"test",
	}
	
	for _, path := range testPaths {
		fmt.Printf("\n测试路径: %s\n", path)
		pk, name := handler.ConvertNodePathToJSNodePK(path)
		if pk != "" {
			fmt.Printf("  ✓ 成功: PK=%s, Name=%s\n", pk, name)
		} else {
			fmt.Printf("  ❌ 失败\n")
		}
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n修复要点:")
	fmt.Println("1. ✅ 使用 getNodeByPath 替代 getNodeNameByIdent")
	fmt.Println("2. ✅ 通过完整路径精确查询 RDB 节点")
	fmt.Println("3. ✅ 使用 rdb_id 进行精确匹配")
	fmt.Println("4. ✅ 添加多种备用匹配方法")
	fmt.Println("5. ✅ 详细的调试日志输出")
	
	fmt.Println("\n请查看日志输出，特别关注:")
	fmt.Println("- RDB 查询是否找到了正确的节点")
	fmt.Println("- 节点的 id, ident, name, path 信息")
	fmt.Println("- JumpServer 查找过程")
	fmt.Println("- 备用匹配方法的结果")
}
