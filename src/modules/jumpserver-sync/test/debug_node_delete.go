package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端
	client := jumpserver.NewClient("http://10.1.4.213:8090", "admin", "admin")

	fmt.Println("=== 测试节点删除修复 ===")

	// 测试场景1：创建同名多层级节点结构
	fmt.Println("\n1. 创建同名多层级节点结构...")
	
	// 创建层级结构：删除测试组 -> 删除测试组 -> 删除测试组 -> 删除测试组
	hierarchyPath := "/删除测试组/删除测试组/删除测试组/删除测试组"
	
	finalNode, err := client.CreateNodeHierarchy(hierarchyPath)
	if err != nil {
		log.Printf("创建测试节点结构失败: %v", err)
		// 尝试查找现有结构
		rootNode, findErr := client.GetNodeByParentAndValue("", "删除测试组")
		if findErr != nil || rootNode == nil {
			log.Fatalf("无法创建或找到测试节点结构")
		}
		fmt.Printf("✓ 使用现有节点结构\n")
	} else {
		fmt.Printf("✓ 测试节点结构创建成功: ID=%s, Value=%s\n", finalNode.ID, finalNode.Value)
	}

	// 获取各层级节点信息
	fmt.Println("\n2. 获取各层级节点信息...")
	
	level1Node, err := client.GetNodeByParentAndValue("", "删除测试组")
	if err != nil || level1Node == nil {
		log.Fatalf("获取第1层节点失败: %v", err)
	}
	fmt.Printf("✓ 第1层节点: ID=%s, Value=%s\n", level1Node.ID, level1Node.Value)

	level2Node, err := client.GetNodeByParentAndValue(level1Node.ID, "删除测试组")
	if err != nil || level2Node == nil {
		log.Fatalf("获取第2层节点失败: %v", err)
	}
	fmt.Printf("✓ 第2层节点: ID=%s, Value=%s\n", level2Node.ID, level2Node.Value)

	level3Node, err := client.GetNodeByParentAndValue(level2Node.ID, "删除测试组")
	if err != nil || level3Node == nil {
		log.Fatalf("获取第3层节点失败: %v", err)
	}
	fmt.Printf("✓ 第3层节点: ID=%s, Value=%s\n", level3Node.ID, level3Node.Value)

	level4Node, err := client.GetNodeByParentAndValue(level3Node.ID, "删除测试组")
	if err != nil || level4Node == nil {
		log.Fatalf("获取第4层节点失败: %v", err)
	}
	fmt.Printf("✓ 第4层节点: ID=%s, Value=%s\n", level4Node.ID, level4Node.Value)

	// 测试场景2：验证全局搜索的问题
	fmt.Println("\n3. 验证全局搜索的问题...")
	
	// 使用全局搜索（旧方法）
	globalSearchNode, err := client.GetNodeByValue("删除测试组")
	if err != nil {
		log.Printf("全局搜索失败: %v", err)
	} else if globalSearchNode != nil {
		fmt.Printf("❌ 全局搜索返回的节点: ID=%s, Value=%s\n", globalSearchNode.ID, globalSearchNode.Value)
		fmt.Printf("   这很可能是第1层节点，而不是我们想要删除的第4层节点\n")
		
		// 检查这是哪一层的节点
		if globalSearchNode.ID == level1Node.ID {
			fmt.Printf("   ✓ 确认：全局搜索返回的是第1层节点\n")
		} else if globalSearchNode.ID == level2Node.ID {
			fmt.Printf("   ✓ 确认：全局搜索返回的是第2层节点\n")
		} else if globalSearchNode.ID == level3Node.ID {
			fmt.Printf("   ✓ 确认：全局搜索返回的是第3层节点\n")
		} else if globalSearchNode.ID == level4Node.ID {
			fmt.Printf("   ✓ 确认：全局搜索返回的是第4层节点\n")
		} else {
			fmt.Printf("   ❓ 全局搜索返回的是未知节点\n")
		}
	}

	// 测试场景3：精确删除最后一层节点
	fmt.Println("\n4. 测试精确删除最后一层节点...")
	
	fmt.Printf("准备删除第4层节点: ID=%s, Value=%s\n", level4Node.ID, level4Node.Value)
	fmt.Printf("父节点是第3层节点: ID=%s, Value=%s\n", level3Node.ID, level3Node.Value)
	
	// 使用精确查找方法
	nodeToDelete, err := client.GetNodeByParentAndValue(level3Node.ID, "删除测试组")
	if err != nil {
		log.Fatalf("精确查找要删除的节点失败: %v", err)
	}
	if nodeToDelete == nil {
		log.Fatalf("精确查找没有找到要删除的节点")
	}
	
	if nodeToDelete.ID != level4Node.ID {
		log.Fatalf("❌ 精确查找找到了错误的节点: 期望=%s, 实际=%s", level4Node.ID, nodeToDelete.ID)
	}
	
	fmt.Printf("✓ 精确查找成功，找到正确的第4层节点: ID=%s\n", nodeToDelete.ID)
	
	// 执行删除
	fmt.Printf("执行删除操作...\n")
	err = client.DeleteNode(nodeToDelete.ID)
	if err != nil {
		log.Fatalf("删除节点失败: %v", err)
	}
	
	fmt.Printf("✓ 节点删除成功\n")

	// 测试场景4：验证删除结果
	fmt.Println("\n5. 验证删除结果...")
	
	// 检查第4层节点是否已删除
	deletedCheck, err := client.GetNodeByParentAndValue(level3Node.ID, "删除测试组")
	if err != nil {
		fmt.Printf("检查删除结果时出错: %v\n", err)
	} else if deletedCheck == nil {
		fmt.Printf("✓ 第4层节点已成功删除\n")
	} else {
		fmt.Printf("❌ 第4层节点仍然存在: ID=%s\n", deletedCheck.ID)
	}
	
	// 检查第3层节点是否仍然存在
	level3Check, err := client.GetNodeByParentAndValue(level2Node.ID, "删除测试组")
	if err != nil {
		fmt.Printf("检查第3层节点时出错: %v\n", err)
	} else if level3Check == nil {
		fmt.Printf("❌ 第3层节点被错误删除了\n")
	} else {
		fmt.Printf("✓ 第3层节点仍然存在: ID=%s\n", level3Check.ID)
	}
	
	// 检查第2层节点是否仍然存在
	level2Check, err := client.GetNodeByParentAndValue(level1Node.ID, "删除测试组")
	if err != nil {
		fmt.Printf("检查第2层节点时出错: %v\n", err)
	} else if level2Check == nil {
		fmt.Printf("❌ 第2层节点被错误删除了\n")
	} else {
		fmt.Printf("✓ 第2层节点仍然存在: ID=%s\n", level2Check.ID)
	}
	
	// 检查第1层节点是否仍然存在
	level1Check, err := client.GetNodeByParentAndValue("", "删除测试组")
	if err != nil {
		fmt.Printf("检查第1层节点时出错: %v\n", err)
	} else if level1Check == nil {
		fmt.Printf("❌ 第1层节点被错误删除了\n")
	} else {
		fmt.Printf("✓ 第1层节点仍然存在: ID=%s\n", level1Check.ID)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n修复效果总结:")
	fmt.Println("✅ 使用精确查找替代全局搜索")
	fmt.Println("✅ 通过父节点ID + 节点名称精确定位")
	fmt.Println("✅ 避免删除错误的同名节点")
	fmt.Println("✅ 保护父节点不被误删")
	fmt.Println("✅ 添加Meta信息备用匹配")
	
	fmt.Println("\n如果测试通过，说明节点删除修复成功！")
}
