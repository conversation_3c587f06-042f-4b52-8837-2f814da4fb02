package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端 - 请根据实际环境修改URL和认证信息
	client := jumpserver.NewClient("http://localhost:8080", "admin", "admin")

	fmt.Println("=== 测试多层级节点创建修复 ===")

	// 测试场景1：创建深层级节点树
	fmt.Println("\n1. 测试创建5层级节点树...")
	hierarchyPath := "/测试公司/技术部/后端组/核心团队/开发小组"
	
	finalNode, err := client.CreateNodeHierarchy(hierarchyPath)
	if err != nil {
		log.Fatalf("创建5层级节点失败: %v", err)
	}
	fmt.Printf("✓ 5层级节点创建成功: ID=%s, Value=%s\n", finalNode.ID, finalNode.Value)

	// 测试场景2：创建更深的层级
	fmt.Println("\n2. 测试创建7层级节点树...")
	deepHierarchyPath := "/集团/子公司/事业部/技术中心/研发部/后端组/微服务团队"
	
	deepFinalNode, err := client.CreateNodeHierarchy(deepHierarchyPath)
	if err != nil {
		log.Fatalf("创建7层级节点失败: %v", err)
	}
	fmt.Printf("✓ 7层级节点创建成功: ID=%s, Value=%s\n", deepFinalNode.ID, deepFinalNode.Value)

	// 测试场景3：在现有层级中插入新分支
	fmt.Println("\n3. 测试在现有层级中创建新分支...")
	branchPath := "/测试公司/技术部/前端组/UI团队"
	
	branchNode, err := client.CreateNodeHierarchy(branchPath)
	if err != nil {
		log.Fatalf("创建分支节点失败: %v", err)
	}
	fmt.Printf("✓ 分支节点创建成功: ID=%s, Value=%s\n", branchNode.ID, branchNode.Value)

	// 测试场景4：验证同名不同父节点
	fmt.Println("\n4. 测试同名不同父节点...")
	
	// 在不同的父节点下创建同名的"开发组"
	devPath1 := "/测试公司/技术部/后端组/开发组"
	devPath2 := "/测试公司/技术部/前端组/开发组"
	
	devNode1, err := client.CreateNodeHierarchy(devPath1)
	if err != nil {
		log.Fatalf("创建第一个开发组失败: %v", err)
	}
	fmt.Printf("✓ 后端开发组创建成功: ID=%s, Value=%s\n", devNode1.ID, devNode1.Value)
	
	devNode2, err := client.CreateNodeHierarchy(devPath2)
	if err != nil {
		log.Fatalf("创建第二个开发组失败: %v", err)
	}
	fmt.Printf("✓ 前端开发组创建成功: ID=%s, Value=%s\n", devNode2.ID, devNode2.Value)

	// 验证两个开发组的ID不同
	if devNode1.ID == devNode2.ID {
		log.Fatalf("❌ 错误：两个同名节点的ID相同，说明创建失败")
	}
	fmt.Printf("✓ 验证通过：两个同名节点ID不同 (ID1=%s, ID2=%s)\n", devNode1.ID, devNode2.ID)

	// 测试场景5：验证节点查找功能
	fmt.Println("\n5. 测试节点查找功能...")
	
	// 查找"技术部"节点（应该能找到）
	techDept, err := client.GetNodeByParentAndValue("", "技术部")
	if err != nil {
		log.Fatalf("查找技术部失败: %v", err)
	}
	if techDept == nil {
		log.Fatalf("❌ 技术部节点未找到")
	}
	fmt.Printf("✓ 找到技术部节点: ID=%s, Value=%s\n", techDept.ID, techDept.Value)

	// 查找技术部下的子节点
	techChildren, err := client.GetNodeChildren(techDept.ID)
	if err != nil {
		log.Fatalf("获取技术部子节点失败: %v", err)
	}
	fmt.Printf("✓ 技术部下有 %d 个子节点:\n", len(techChildren))
	for i, child := range techChildren {
		fmt.Printf("  %d. %s (ID: %s)\n", i+1, child.Value, child.ID)
	}

	// 测试场景6：验证层级查找
	fmt.Println("\n6. 测试层级查找...")
	
	// 在后端组下查找开发组
	backendGroup, err := client.GetNodeByParentAndValue(techDept.ID, "后端组")
	if err != nil || backendGroup == nil {
		log.Fatalf("查找后端组失败: %v", err)
	}
	
	backendDevGroup, err := client.GetNodeByParentAndValue(backendGroup.ID, "开发组")
	if err != nil {
		log.Fatalf("在后端组下查找开发组失败: %v", err)
	}
	if backendDevGroup == nil {
		log.Fatalf("❌ 后端组下的开发组未找到")
	}
	fmt.Printf("✓ 在后端组下找到开发组: ID=%s, Value=%s\n", backendDevGroup.ID, backendDevGroup.Value)

	// 在前端组下查找开发组
	frontendGroup, err := client.GetNodeByParentAndValue(techDept.ID, "前端组")
	if err != nil || frontendGroup == nil {
		log.Fatalf("查找前端组失败: %v", err)
	}
	
	frontendDevGroup, err := client.GetNodeByParentAndValue(frontendGroup.ID, "开发组")
	if err != nil {
		log.Fatalf("在前端组下查找开发组失败: %v", err)
	}
	if frontendDevGroup == nil {
		log.Fatalf("❌ 前端组下的开发组未找到")
	}
	fmt.Printf("✓ 在前端组下找到开发组: ID=%s, Value=%s\n", frontendDevGroup.ID, frontendDevGroup.Value)

	// 验证两个开发组确实不同
	if backendDevGroup.ID == frontendDevGroup.ID {
		log.Fatalf("❌ 错误：两个不同父节点下的同名节点ID相同")
	}
	fmt.Printf("✓ 验证通过：不同父节点下的同名节点ID不同\n")

	fmt.Println("\n=== 🎉 所有测试通过！多层级节点创建修复成功！ ===")
	fmt.Println("\n修复效果总结:")
	fmt.Println("✅ 支持创建任意深度的层级节点树")
	fmt.Println("✅ 支持在现有层级中插入新分支")
	fmt.Println("✅ 支持在不同父节点下创建同名子节点")
	fmt.Println("✅ 节点查找功能正确，支持层级查找")
	fmt.Println("✅ 递归父节点创建逻辑工作正常")
}
