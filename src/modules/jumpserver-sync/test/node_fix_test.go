package test

import (
	"fmt"
	"testing"
	"time"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

// TestNodeCreationFix 测试节点创建修复
func TestNodeCreationFix(t *testing.T) {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端
	client := jumpserver.NewClient("http://localhost:8080", "admin", "admin")

	// 测试场景1：创建同一父节点下的多个子节点
	t.Run("CreateMultipleChildrenUnderSameParent", func(t *testing.T) {
		// 创建父节点
		parentNode := &jumpserver.Node{
			Key:   "test-parent",
			Value: "测试父节点",
		}

		createdParent, err := client.CreateNode(parentNode)
		if err != nil {
			t.Fatalf("Failed to create parent node: %v", err)
		}
		logger.Infof("Created parent node: %+v", createdParent)

		// 创建第一个子节点
		child1 := &jumpserver.Node{
			Key:    "child1",
			Value:  "子节点1",
			Parent: createdParent.ID,
		}

		createdChild1, err := client.CreateNode(child1)
		if err != nil {
			t.Fatalf("Failed to create first child node: %v", err)
		}
		logger.Infof("Created first child node: %+v", createdChild1)

		// 创建第二个子节点
		child2 := &jumpserver.Node{
			Key:    "child2",
			Value:  "子节点2",
			Parent: createdParent.ID,
		}

		createdChild2, err := client.CreateNode(child2)
		if err != nil {
			t.Fatalf("Failed to create second child node: %v", err)
		}
		logger.Infof("Created second child node: %+v", createdChild2)

		// 验证两个子节点都创建成功
		children, err := client.GetNodeChildren(createdParent.ID)
		if err != nil {
			t.Fatalf("Failed to get children: %v", err)
		}

		if len(children) != 2 {
			t.Fatalf("Expected 2 children, got %d", len(children))
		}

		logger.Infof("Successfully created 2 children under parent node")
	})

	// 测试场景2：创建多层级节点树
	t.Run("CreateMultiLevelHierarchy", func(t *testing.T) {
		// 创建多层级路径
		hierarchyPath := "/公司/技术部/后端组/核心团队/开发小组"
		
		createdNode, err := client.CreateNodeHierarchy(hierarchyPath)
		if err != nil {
			t.Fatalf("Failed to create hierarchy: %v", err)
		}
		logger.Infof("Created hierarchy node: %+v", createdNode)

		// 验证层级结构
		if createdNode.Value != "开发小组" {
			t.Fatalf("Expected final node value '开发小组', got '%s'", createdNode.Value)
		}

		logger.Infof("Successfully created multi-level hierarchy")
	})

	// 测试场景3：创建同名但不同父节点的节点
	t.Run("CreateSameNameDifferentParents", func(t *testing.T) {
		// 创建两个不同的父节点
		parent1 := &jumpserver.Node{
			Key:   "dept-a",
			Value: "部门A",
		}
		createdParent1, err := client.CreateNode(parent1)
		if err != nil {
			t.Fatalf("Failed to create parent1: %v", err)
		}

		parent2 := &jumpserver.Node{
			Key:   "dept-b", 
			Value: "部门B",
		}
		createdParent2, err := client.CreateNode(parent2)
		if err != nil {
			t.Fatalf("Failed to create parent2: %v", err)
		}

		// 在两个父节点下创建同名子节点
		child1 := &jumpserver.Node{
			Key:    "dev-team",
			Value:  "开发组",
			Parent: createdParent1.ID,
		}
		createdChild1, err := client.CreateNode(child1)
		if err != nil {
			t.Fatalf("Failed to create child under parent1: %v", err)
		}

		child2 := &jumpserver.Node{
			Key:    "dev-team",
			Value:  "开发组",
			Parent: createdParent2.ID,
		}
		createdChild2, err := client.CreateNode(child2)
		if err != nil {
			t.Fatalf("Failed to create child under parent2: %v", err)
		}

		// 验证两个同名节点都创建成功且ID不同
		if createdChild1.ID == createdChild2.ID {
			t.Fatalf("Same name nodes should have different IDs")
		}

		logger.Infof("Successfully created same-name nodes under different parents")
	})
}

// TestGetNodeByParentAndValue 测试节点查找功能
func TestGetNodeByParentAndValue(t *testing.T) {
	logger.Init("debug", "console", "")
	client := jumpserver.NewClient("http://localhost:8080", "admin", "admin")

	// 创建测试节点结构
	parent := &jumpserver.Node{
		Key:   "test-search-parent",
		Value: "搜索测试父节点",
	}
	createdParent, err := client.CreateNode(parent)
	if err != nil {
		t.Fatalf("Failed to create parent: %v", err)
	}

	// 创建子节点
	child := &jumpserver.Node{
		Key:    "test-search-child",
		Value:  "搜索测试子节点",
		Parent: createdParent.ID,
	}
	createdChild, err := client.CreateNode(child)
	if err != nil {
		t.Fatalf("Failed to create child: %v", err)
	}

	// 测试查找功能
	foundChild, err := client.GetNodeByParentAndValue(createdParent.ID, "搜索测试子节点")
	if err != nil {
		t.Fatalf("Failed to find child: %v", err)
	}

	if foundChild == nil {
		t.Fatalf("Child node not found")
	}

	if foundChild.ID != createdChild.ID {
		t.Fatalf("Found wrong node: expected %s, got %s", createdChild.ID, foundChild.ID)
	}

	logger.Infof("Successfully found child node by parent and value")
}
