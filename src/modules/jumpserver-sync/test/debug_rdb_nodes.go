package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/sync"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	fmt.Println("=== 调试 RDB 节点数据 ===")

	// 创建配置
	cfg := &config.ConfigT{
		JumpServer: config.JumpServerConfig{
			BaseURL:  "http://10.1.4.213:8090",
			Username: "admin",
			Password: "admin",
		},
		Sync: config.SyncConfig{
			Rules: config.SyncRules{
				IncludePaths: []string{},
				ExcludePaths: []string{},
				Filters:      map[string]string{},
			},
			Mapping: config.SyncMapping{
				NodeRules:    []config.NodeRule{},
				MachineRules: []config.MachineRule{},
			},
		},
	}

	// 创建处理器
	handler, err := sync.NewHandler(cfg)
	if err != nil {
		log.Fatalf("创建处理器失败: %v", err)
	}

	// 测试场景1：查询 ident 为 "flagopen" 的所有节点
	fmt.Println("\n1. 查询 ident='flagopen' 的所有节点...")
	
	// 这里我们需要直接调用 getNodeNameByIdent 方法
	// 但由于它是私有方法，我们需要通过其他方式
	
	// 让我们直接测试路径转换
	testPath := "flagopen.flagopen"
	fmt.Printf("测试路径转换: %s\n", testPath)
	
	nodePK, nodeName := handler.ConvertNodePathToJSNodePK(testPath)
	
	if nodePK != "" {
		fmt.Printf("✓ 转换成功: PK=%s, Name=%s\n", nodePK, nodeName)
	} else {
		fmt.Printf("❌ 转换失败\n")
	}

	// 测试场景2：查询特定ID的节点
	fmt.Println("\n2. 测试其他可能的路径...")
	
	testPaths := []string{
		"flagopen",
		"flagopen.flagopen",
		"flsssssst",
	}
	
	for _, path := range testPaths {
		fmt.Printf("\n测试路径: %s\n", path)
		pk, name := handler.ConvertNodePathToJSNodePK(path)
		if pk != "" {
			fmt.Printf("  ✓ 成功: PK=%s, Name=%s\n", pk, name)
		} else {
			fmt.Printf("  ❌ 失败\n")
		}
	}

	fmt.Println("\n=== 调试完成 ===")
	fmt.Println("\n请查看上面的日志输出，特别关注:")
	fmt.Println("1. RDB 查询返回了多少个节点")
	fmt.Println("2. 每个节点的 id, name, ident, path")
	fmt.Println("3. 选择了哪个节点")
	fmt.Println("4. JumpServer 查找是否成功")
}
