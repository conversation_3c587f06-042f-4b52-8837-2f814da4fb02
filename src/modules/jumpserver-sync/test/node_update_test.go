package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端 - 请根据实际环境修改URL和认证信息
	client := jumpserver.NewClient("http://localhost:8080", "admin", "admin")

	fmt.Println("=== 测试节点更新修复 ===")

	// 测试场景1：创建多层级节点结构
	fmt.Println("\n1. 创建测试节点结构...")
	hierarchyPath := "/更新测试公司/技术部/后端组/开发小组"
	
	finalNode, err := client.CreateNodeHierarchy(hierarchyPath)
	if err != nil {
		log.Fatalf("创建测试节点结构失败: %v", err)
	}
	fmt.Printf("✓ 测试节点结构创建成功: ID=%s, Value=%s\n", finalNode.ID, finalNode.Value)

	// 获取所有层级的节点信息
	fmt.Println("\n2. 获取各层级节点信息...")
	
	// 获取根节点
	companyNode, err := client.GetNodeByParentAndValue("", "更新测试公司")
	if err != nil || companyNode == nil {
		log.Fatalf("获取公司节点失败: %v", err)
	}
	fmt.Printf("✓ 公司节点: ID=%s, Value=%s\n", companyNode.ID, companyNode.Value)

	// 获取技术部节点
	techNode, err := client.GetNodeByParentAndValue(companyNode.ID, "技术部")
	if err != nil || techNode == nil {
		log.Fatalf("获取技术部节点失败: %v", err)
	}
	fmt.Printf("✓ 技术部节点: ID=%s, Value=%s\n", techNode.ID, techNode.Value)

	// 获取后端组节点
	backendNode, err := client.GetNodeByParentAndValue(techNode.ID, "后端组")
	if err != nil || backendNode == nil {
		log.Fatalf("获取后端组节点失败: %v", err)
	}
	fmt.Printf("✓ 后端组节点: ID=%s, Value=%s\n", backendNode.ID, backendNode.Value)

	// 获取开发小组节点
	devNode, err := client.GetNodeByParentAndValue(backendNode.ID, "开发小组")
	if err != nil || devNode == nil {
		log.Fatalf("获取开发小组节点失败: %v", err)
	}
	fmt.Printf("✓ 开发小组节点: ID=%s, Value=%s\n", devNode.ID, devNode.Value)

	// 测试场景2：更新最底层节点名称
	fmt.Println("\n3. 测试更新最底层节点名称...")
	
	// 更新开发小组的名称
	devNode.Value = "核心开发小组"
	updatedDevNode, err := client.UpdateNode(devNode.ID, devNode)
	if err != nil {
		log.Fatalf("更新开发小组节点失败: %v", err)
	}
	fmt.Printf("✓ 开发小组节点更新成功: ID=%s, 新名称=%s\n", updatedDevNode.ID, updatedDevNode.Value)

	// 验证其他层级节点没有被错误更新
	fmt.Println("\n4. 验证其他层级节点没有被错误更新...")
	
	// 重新获取各层级节点，确认名称没有被错误修改
	companyNodeAfter, err := client.GetNodeByParentAndValue("", "更新测试公司")
	if err != nil || companyNodeAfter == nil {
		log.Fatalf("重新获取公司节点失败: %v", err)
	}
	if companyNodeAfter.Value != "更新测试公司" {
		log.Fatalf("❌ 公司节点名称被错误修改: 期望=%s, 实际=%s", "更新测试公司", companyNodeAfter.Value)
	}
	fmt.Printf("✓ 公司节点名称正确: %s\n", companyNodeAfter.Value)

	techNodeAfter, err := client.GetNodeByParentAndValue(companyNode.ID, "技术部")
	if err != nil || techNodeAfter == nil {
		log.Fatalf("重新获取技术部节点失败: %v", err)
	}
	if techNodeAfter.Value != "技术部" {
		log.Fatalf("❌ 技术部节点名称被错误修改: 期望=%s, 实际=%s", "技术部", techNodeAfter.Value)
	}
	fmt.Printf("✓ 技术部节点名称正确: %s\n", techNodeAfter.Value)

	backendNodeAfter, err := client.GetNodeByParentAndValue(techNode.ID, "后端组")
	if err != nil || backendNodeAfter == nil {
		log.Fatalf("重新获取后端组节点失败: %v", err)
	}
	if backendNodeAfter.Value != "后端组" {
		log.Fatalf("❌ 后端组节点名称被错误修改: 期望=%s, 实际=%s", "后端组", backendNodeAfter.Value)
	}
	fmt.Printf("✓ 后端组节点名称正确: %s\n", backendNodeAfter.Value)

	// 验证更新的节点名称正确
	devNodeAfter, err := client.GetNodeByParentAndValue(backendNode.ID, "核心开发小组")
	if err != nil || devNodeAfter == nil {
		log.Fatalf("重新获取更新后的开发小组节点失败: %v", err)
	}
	if devNodeAfter.Value != "核心开发小组" {
		log.Fatalf("❌ 开发小组节点更新失败: 期望=%s, 实际=%s", "核心开发小组", devNodeAfter.Value)
	}
	fmt.Printf("✓ 开发小组节点更新正确: %s\n", devNodeAfter.Value)

	// 测试场景3：更新中间层级节点名称
	fmt.Println("\n5. 测试更新中间层级节点名称...")
	
	// 更新技术部的名称
	techNode.Value = "研发部"
	updatedTechNode, err := client.UpdateNode(techNode.ID, techNode)
	if err != nil {
		log.Fatalf("更新技术部节点失败: %v", err)
	}
	fmt.Printf("✓ 技术部节点更新成功: ID=%s, 新名称=%s\n", updatedTechNode.ID, updatedTechNode.Value)

	// 验证其他节点没有被错误更新
	fmt.Println("\n6. 验证其他节点没有被错误更新...")
	
	companyNodeAfter2, err := client.GetNodeByParentAndValue("", "更新测试公司")
	if err != nil || companyNodeAfter2 == nil {
		log.Fatalf("重新获取公司节点失败: %v", err)
	}
	if companyNodeAfter2.Value != "更新测试公司" {
		log.Fatalf("❌ 公司节点名称被错误修改: 期望=%s, 实际=%s", "更新测试公司", companyNodeAfter2.Value)
	}
	fmt.Printf("✓ 公司节点名称仍然正确: %s\n", companyNodeAfter2.Value)

	// 验证技术部更新正确
	techNodeAfter2, err := client.GetNodeByParentAndValue(companyNode.ID, "研发部")
	if err != nil || techNodeAfter2 == nil {
		log.Fatalf("重新获取更新后的研发部节点失败: %v", err)
	}
	if techNodeAfter2.Value != "研发部" {
		log.Fatalf("❌ 研发部节点更新失败: 期望=%s, 实际=%s", "研发部", techNodeAfter2.Value)
	}
	fmt.Printf("✓ 研发部节点更新正确: %s\n", techNodeAfter2.Value)

	// 验证子节点仍然存在且正确
	backendNodeAfter2, err := client.GetNodeByParentAndValue(techNodeAfter2.ID, "后端组")
	if err != nil || backendNodeAfter2 == nil {
		log.Fatalf("重新获取后端组节点失败: %v", err)
	}
	fmt.Printf("✓ 后端组节点仍然存在: %s\n", backendNodeAfter2.Value)

	devNodeAfter2, err := client.GetNodeByParentAndValue(backendNodeAfter2.ID, "核心开发小组")
	if err != nil || devNodeAfter2 == nil {
		log.Fatalf("重新获取核心开发小组节点失败: %v", err)
	}
	fmt.Printf("✓ 核心开发小组节点仍然存在: %s\n", devNodeAfter2.Value)

	// 测试场景4：创建同名节点测试更新精确性
	fmt.Println("\n7. 测试同名节点更新精确性...")
	
	// 在不同位置创建同名节点
	otherHierarchyPath := "/其他公司/技术部/后端组"
	otherFinalNode, err := client.CreateNodeHierarchy(otherHierarchyPath)
	if err != nil {
		log.Fatalf("创建其他同名节点失败: %v", err)
	}
	fmt.Printf("✓ 其他同名节点创建成功: ID=%s, Value=%s\n", otherFinalNode.ID, otherFinalNode.Value)

	// 更新第一个"后端组"的名称
	backendNodeAfter2.Value = "服务端组"
	updatedBackendNode, err := client.UpdateNode(backendNodeAfter2.ID, backendNodeAfter2)
	if err != nil {
		log.Fatalf("更新第一个后端组节点失败: %v", err)
	}
	fmt.Printf("✓ 第一个后端组更新成功: ID=%s, 新名称=%s\n", updatedBackendNode.ID, updatedBackendNode.Value)

	// 验证第二个"后端组"没有被错误更新
	otherCompanyNode, err := client.GetNodeByParentAndValue("", "其他公司")
	if err != nil || otherCompanyNode == nil {
		log.Fatalf("获取其他公司节点失败: %v", err)
	}

	otherTechNode, err := client.GetNodeByParentAndValue(otherCompanyNode.ID, "技术部")
	if err != nil || otherTechNode == nil {
		log.Fatalf("获取其他公司技术部节点失败: %v", err)
	}

	otherBackendNode, err := client.GetNodeByParentAndValue(otherTechNode.ID, "后端组")
	if err != nil || otherBackendNode == nil {
		log.Fatalf("重新获取其他公司后端组节点失败: %v", err)
	}
	if otherBackendNode.Value != "后端组" {
		log.Fatalf("❌ 其他公司的后端组被错误修改: 期望=%s, 实际=%s", "后端组", otherBackendNode.Value)
	}
	fmt.Printf("✓ 其他公司的后端组名称正确，没有被错误修改: %s\n", otherBackendNode.Value)

	fmt.Println("\n=== 🎉 所有测试通过！节点更新修复成功！ ===")
	fmt.Println("\n修复效果总结:")
	fmt.Println("✅ 节点更新时能正确定位到目标节点")
	fmt.Println("✅ 不会错误更新同名但不同层级的节点")
	fmt.Println("✅ 支持更新任意层级的节点名称")
	fmt.Println("✅ 更新后的节点层级关系保持正确")
	fmt.Println("✅ 同名节点的更新精确性得到保证")
}
