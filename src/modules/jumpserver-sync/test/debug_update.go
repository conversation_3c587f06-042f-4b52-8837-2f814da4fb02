package main

import (
	"encoding/json"
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/sync"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	fmt.Println("=== 调试节点更新问题 ===")

	// 创建配置
	cfg := &config.ConfigT{
		JumpServer: config.JumpServerConfig{
			BaseURL:  "http://localhost:8080",
			Username: "admin",
			Password: "admin",
		},
		Sync: config.SyncConfig{
			Rules: config.SyncRules{
				IncludePaths: []string{},
				ExcludePaths: []string{},
				Filters:      map[string]string{},
			},
			Mapping: config.SyncMapping{
				NodeRules:    []config.NodeRule{},
				MachineRules: []config.MachineRule{},
			},
		},
	}

	// 创建处理器
	handler, err := sync.NewHandler(cfg)
	if err != nil {
		log.Fatalf("创建处理器失败: %v", err)
	}

	// 模拟节点更新事件
	fmt.Println("\n1. 模拟节点更新事件...")
	
	// 创建一个模拟的节点更新事件
	updateEvent := &events.Event{
		ID:        "test-update-001",
		Type:      events.EventNodeUpdate,
		Source:    "rdb",
		Timestamp: 1234567890,
		Data: map[string]interface{}{
			"id":            123,
			"pid":           456,
			"ident":         "test-node",
			"name":          "新节点名称",
			"original_name": "原始节点名称",
			"note":          "测试节点",
			"path":          "flag.flag-2.flag-2.flag-2",
			"leaf":          0,
			"cate":          "project",
			"icon_color":    "",
			"icon_char":     "",
			"proxy":         0,
			"creator":       "admin",
			"last_updated":  1234567890,
		},
		Metadata: map[string]string{
			"entity_type": "node",
			"entity_id":   "123",
			"node_path":   "flag.flag-2.flag-2.flag-2",
			"node_ident":  "test-node",
		},
	}

	// 打印事件详情
	eventJSON, _ := json.MarshalIndent(updateEvent, "", "  ")
	fmt.Printf("更新事件详情:\n%s\n", eventJSON)

	// 处理事件
	fmt.Println("\n2. 处理节点更新事件...")
	err = handler.HandleEvent(updateEvent)
	if err != nil {
		log.Printf("处理事件失败: %v", err)
	} else {
		fmt.Println("✓ 事件处理完成")
	}

	fmt.Println("\n=== 调试完成 ===")
	fmt.Println("\n请查看上面的日志输出，特别关注:")
	fmt.Println("1. OriginalName 是否正确传递")
	fmt.Println("2. 父节点查找是否成功")
	fmt.Println("3. 现有节点查找是否成功")
	fmt.Println("4. 是否走了创建新节点的逻辑")
}
