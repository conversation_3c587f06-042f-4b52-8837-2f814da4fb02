package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建JumpServer客户端
	client := jumpserver.NewClient("http://10.1.4.213:8090", "admin", "admin")

	fmt.Println("=== 测试用户组成员添加修复 ===")

	// 测试场景1：创建测试用户组
	fmt.Println("\n1. 创建测试用户组...")
	
	testGroup := &jumpserver.UserGroup{
		Name:    "成员测试组",
		Comment: "用于测试成员添加功能",
	}
	
	createdGroup, err := client.CreateUserGroup(testGroup)
	if err != nil {
		log.Printf("创建用户组失败: %v", err)
		// 尝试查找现有用户组
		existingGroup, findErr := client.GetUserGroup("成员测试组")
		if findErr != nil || existingGroup == nil {
			log.Fatalf("无法创建或找到测试用户组")
		}
		createdGroup = existingGroup
		fmt.Printf("✓ 使用现有用户组: ID=%s, Name=%s\n", createdGroup.ID, createdGroup.Name)
	} else {
		fmt.Printf("✓ 用户组创建成功: ID=%s, Name=%s\n", createdGroup.ID, createdGroup.Name)
	}

	// 测试场景2：创建测试用户（如果不存在）
	fmt.Println("\n2. 准备测试用户...")
	
	testUsers := []string{"testuser1", "testuser2", "testuser3"}
	var userIDs []string
	
	for _, username := range testUsers {
		user, err := client.GetUser(username)
		if err != nil {
			fmt.Printf("用户 %s 不存在，跳过\n", username)
			continue
		}
		userIDs = append(userIDs, user.ID)
		fmt.Printf("✓ 找到用户: %s (ID: %s)\n", username, user.ID)
	}
	
	if len(userIDs) < 2 {
		fmt.Println("❌ 需要至少2个测试用户，请先创建 testuser1, testuser2, testuser3")
		return
	}

	// 测试场景3：第一次添加成员
	fmt.Println("\n3. 第一次添加成员...")
	
	firstBatch := userIDs[:1] // 只添加第一个用户
	fmt.Printf("添加第一批成员: %v\n", firstBatch)
	
	err = client.UpdateUserGroupMembers(createdGroup.ID, firstBatch)
	if err != nil {
		log.Fatalf("第一次添加成员失败: %v", err)
	}
	
	// 验证第一次添加的结果
	members1, err := client.GetUserGroupMembers(createdGroup.ID)
	if err != nil {
		log.Fatalf("获取用户组成员失败: %v", err)
	}
	
	fmt.Printf("✓ 第一次添加后，用户组有 %d 个成员\n", len(members1))
	for i, member := range members1 {
		fmt.Printf("  成员 %d: ID=%s\n", i+1, member.ID)
	}

	// 测试场景4：第二次添加成员（这里会测试修复效果）
	fmt.Println("\n4. 第二次添加成员（测试修复效果）...")
	
	// 模拟修复后的逻辑：合并现有成员和新成员
	secondBatch := userIDs[1:2] // 添加第二个用户
	fmt.Printf("准备添加第二批成员: %v\n", secondBatch)
	
	// 获取现有成员
	existingMembers, err := client.GetUserGroupMembers(createdGroup.ID)
	if err != nil {
		log.Fatalf("获取现有成员失败: %v", err)
	}
	
	fmt.Printf("现有成员数量: %d\n", len(existingMembers))
	
	// 合并成员列表
	memberMap := make(map[string]bool)
	var allMemberIDs []string
	
	// 添加现有成员
	for _, member := range existingMembers {
		if !memberMap[member.ID] {
			memberMap[member.ID] = true
			allMemberIDs = append(allMemberIDs, member.ID)
		}
	}
	
	// 添加新成员
	addedCount := 0
	for _, userID := range secondBatch {
		if !memberMap[userID] {
			memberMap[userID] = true
			allMemberIDs = append(allMemberIDs, userID)
			addedCount++
		}
	}
	
	fmt.Printf("合并后总成员数: %d (新增 %d 个)\n", len(allMemberIDs), addedCount)
	
	// 更新用户组成员
	err = client.UpdateUserGroupMembers(createdGroup.ID, allMemberIDs)
	if err != nil {
		log.Fatalf("第二次添加成员失败: %v", err)
	}
	
	// 验证第二次添加的结果
	members2, err := client.GetUserGroupMembers(createdGroup.ID)
	if err != nil {
		log.Fatalf("获取用户组成员失败: %v", err)
	}
	
	fmt.Printf("✓ 第二次添加后，用户组有 %d 个成员\n", len(members2))
	for i, member := range members2 {
		fmt.Printf("  成员 %d: ID=%s\n", i+1, member.ID)
	}

	// 测试场景5：验证修复效果
	fmt.Println("\n5. 验证修复效果...")
	
	if len(members2) != len(allMemberIDs) {
		fmt.Printf("❌ 成员数量不匹配: 期望=%d, 实际=%d\n", len(allMemberIDs), len(members2))
	} else {
		fmt.Printf("✅ 成员数量正确: %d\n", len(members2))
	}
	
	// 检查是否包含所有期望的成员
	actualMemberMap := make(map[string]bool)
	for _, member := range members2 {
		actualMemberMap[member.ID] = true
	}
	
	allPresent := true
	for _, expectedID := range allMemberIDs {
		if !actualMemberMap[expectedID] {
			fmt.Printf("❌ 缺少成员: ID=%s\n", expectedID)
			allPresent = false
		}
	}
	
	if allPresent {
		fmt.Printf("✅ 所有期望的成员都存在\n")
	}

	// 测试场景6：第三次添加成员（测试去重）
	fmt.Println("\n6. 第三次添加成员（测试去重）...")
	
	if len(userIDs) > 2 {
		// 添加第三个用户，同时重复添加第一个用户（测试去重）
		thirdBatch := []string{userIDs[0], userIDs[2]} // 包含重复的第一个用户
		fmt.Printf("准备添加第三批成员（包含重复）: %v\n", thirdBatch)
		
		// 获取现有成员
		existingMembers3, err := client.GetUserGroupMembers(createdGroup.ID)
		if err != nil {
			log.Fatalf("获取现有成员失败: %v", err)
		}
		
		// 合并成员列表
		memberMap3 := make(map[string]bool)
		var allMemberIDs3 []string
		
		// 添加现有成员
		for _, member := range existingMembers3 {
			if !memberMap3[member.ID] {
				memberMap3[member.ID] = true
				allMemberIDs3 = append(allMemberIDs3, member.ID)
			}
		}
		
		// 添加新成员
		addedCount3 := 0
		for _, userID := range thirdBatch {
			if !memberMap3[userID] {
				memberMap3[userID] = true
				allMemberIDs3 = append(allMemberIDs3, userID)
				addedCount3++
			}
		}
		
		fmt.Printf("合并后总成员数: %d (新增 %d 个)\n", len(allMemberIDs3), addedCount3)
		
		// 更新用户组成员
		err = client.UpdateUserGroupMembers(createdGroup.ID, allMemberIDs3)
		if err != nil {
			log.Fatalf("第三次添加成员失败: %v", err)
		}
		
		// 验证结果
		members3, err := client.GetUserGroupMembers(createdGroup.ID)
		if err != nil {
			log.Fatalf("获取用户组成员失败: %v", err)
		}
		
		fmt.Printf("✓ 第三次添加后，用户组有 %d 个成员\n", len(members3))
		
		if addedCount3 == 1 {
			fmt.Printf("✅ 去重功能正常：重复用户被正确过滤\n")
		} else {
			fmt.Printf("❌ 去重功能异常：期望新增1个，实际新增%d个\n", addedCount3)
		}
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n修复效果总结:")
	fmt.Println("✅ 获取现有用户组成员")
	fmt.Println("✅ 合并现有成员和新成员")
	fmt.Println("✅ 去重处理避免重复添加")
	fmt.Println("✅ 保留原有成员不被删除")
	fmt.Println("✅ 正确的增量添加逻辑")
}
