package events

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/toolkits/pkg/logger"
)

// EventHandler 事件处理器接口
type EventHandler interface {
	HandleEvent(event *Event) error
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts     int
	InitialInterval time.Duration
	MaxInterval     time.Duration
	Multiplier      float64
	EnableDLQ       bool
}

// DeadLetterQueue 死信队列接口
type DeadLetterQueue interface {
	SendToDeadLetter(event *Event, err error, retryCount int, streamID string) error
}

// ErrorClassifier 错误分类器
type ErrorClassifier struct {
	retryPatterns   []string
	dlqPatterns     []string
	discardPatterns []string
}

// NewErrorClassifier 创建错误分类器
func NewErrorClassifier(retryPatterns, dlqPatterns, discardPatterns []string) *ErrorClassifier {
	return &ErrorClassifier{
		retryPatterns:   retryPatterns,
		dlqPatterns:     dlqPatterns,
		discardPatterns: discardPatterns,
	}
}

// ClassifyError 分类错误并返回处理策略
func (ec *ErrorClassifier) ClassifyError(err error) ErrorHandlingStrategy {
	if err == nil {
		return StrategyDLQ
	}

	errStr := strings.ToLower(err.Error())

	// 检查是否匹配重试模式
	for _, pattern := range ec.retryPatterns {
		if strings.Contains(errStr, strings.ToLower(pattern)) {
			return StrategyRetry
		}
	}

	// 检查是否匹配丢弃模式
	for _, pattern := range ec.discardPatterns {
		if strings.Contains(errStr, strings.ToLower(pattern)) {
			return StrategyDiscard
		}
	}

	// 检查是否匹配DLQ模式
	for _, pattern := range ec.dlqPatterns {
		if strings.Contains(errStr, strings.ToLower(pattern)) {
			return StrategyDLQ
		}
	}

	// 默认进入DLQ（保险起见）
	return StrategyDLQ
}

// Consumer Redis Streams事件消费者
type Consumer struct {
	client          *redis.Client
	streamName      string
	groupName       string
	consumerName    string
	batchSize       int64
	pollInterval    time.Duration
	handler         EventHandler
	retryConfig     *RetryConfig
	deadLetterQueue DeadLetterQueue
	errorClassifier *ErrorClassifier
	stopChan        chan struct{}
}

// NewConsumer 创建新的事件消费者
func NewConsumer(redisAddr, password string, db int, streamName, groupName, consumerName string, batchSize int64, pollInterval time.Duration, handler EventHandler) (*Consumer, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %v", err)
	}

	consumer := &Consumer{
		client:       client,
		streamName:   streamName,
		groupName:    groupName,
		consumerName: consumerName,
		batchSize:    batchSize,
		pollInterval: pollInterval,
		handler:      handler,
		stopChan:     make(chan struct{}),
	}

	// 创建消费者组
	if err := consumer.createConsumerGroup(); err != nil {
		return nil, err
	}

	return consumer, nil
}

// SetErrorClassifier 设置错误分类器
func (c *Consumer) SetErrorClassifier(classifier *ErrorClassifier) {
	c.errorClassifier = classifier
}

// SetRetryConfig 设置重试配置
func (c *Consumer) SetRetryConfig(config *RetryConfig) {
	c.retryConfig = config
}

// SetDeadLetterQueue 设置死信队列
func (c *Consumer) SetDeadLetterQueue(dlq DeadLetterQueue) {
	c.deadLetterQueue = dlq
}

// createConsumerGroup 创建消费者组
func (c *Consumer) createConsumerGroup() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 尝试创建消费者组，使用MKSTREAM选项自动创建流
	result := c.client.XGroupCreateMkStream(ctx, c.streamName, c.groupName, "0")
	if err := result.Err(); err != nil {
		// 如果组已存在，忽略错误
		if !strings.Contains(err.Error(), "BUSYGROUP") {
			return fmt.Errorf("failed to create consumer group: %v", err)
		}
	}

	logger.Infof("Consumer group created or already exists: stream=%s, group=%s, consumer=%s",
		c.streamName, c.groupName, c.consumerName)

	return nil
}

// Start 启动消费者
func (c *Consumer) Start() {
	logger.Infof("Starting event consumer: stream=%s, group=%s, consumer=%s, batch_size=%d, poll_interval=%v",
		c.streamName, c.groupName, c.consumerName, c.batchSize, c.pollInterval)

	go c.consumeLoop()

	// 启动过期用户检查器
	if handler, ok := c.handler.(interface{ StartExpiredUserChecker() }); ok {
		handler.StartExpiredUserChecker()
	}
}

// Stop 停止消费者
func (c *Consumer) Stop() {
	logger.Info("Stopping event consumer")
	close(c.stopChan)
}

// consumeLoop 消费循环
func (c *Consumer) consumeLoop() {
	ticker := time.NewTicker(c.pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-c.stopChan:
			logger.Info("Consumer stopped")
			return
		case <-ticker.C:
			c.consumeBatch()
		}
	}
}

// consumeBatch 批量消费消息
func (c *Consumer) consumeBatch() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 从消费者组读取消息
	result := c.client.XReadGroup(ctx, &redis.XReadGroupArgs{
		Group:    c.groupName,
		Consumer: c.consumerName,
		Streams:  []string{c.streamName, ">"},
		Count:    c.batchSize,
		Block:    time.Second,
	})

	if err := result.Err(); err != nil {
		if err != redis.Nil {
			logger.Infof("Failed to read from stream: %v", err)
		}
		return
	}

	streams := result.Val()
	if len(streams) == 0 {
		return
	}

	// 处理消息
	for _, stream := range streams {
		for _, message := range stream.Messages {
			processErr := c.processMessage(message)

			// 无论处理成功还是失败，都要确认消息
			// 因为失败的消息已经进入死信队列，不应该重复处理
			if ackErr := c.ackMessage(message.ID); ackErr != nil {
				logger.Errorf("Failed to acknowledge message %s: %v", message.ID, ackErr)
			}

			// 记录处理结果
			if processErr != nil {
				logger.Infof("Message %s processed with error (but acknowledged): %v", message.ID, processErr)
			} else {
				logger.Debugf("Message %s processed and acknowledged successfully", message.ID)
			}
		}
	}
}

// processMessage 处理单个消息
func (c *Consumer) processMessage(message redis.XMessage) error {
	// 解析消息为事件
	event, err := c.parseMessage(message)
	if err != nil {
		return fmt.Errorf("failed to parse message: %v", err)
	}

	logger.Infof("Processing event: stream_id=%s, event_id=%s, event_type=%s",
		message.ID, event.ID, event.Type)

	// 使用重试机制处理事件
	if c.retryConfig != nil && c.retryConfig.EnableDLQ {
		if err := c.handleEventWithRetry(event, message.ID); err != nil {
			return fmt.Errorf("retry handler failed to process event: %v", err)
		}
	} else {
		// 回退到普通处理器
		if err := c.handler.HandleEvent(event); err != nil {
			return fmt.Errorf("handler failed to process event: %v", err)
		}
	}

	logger.Infof("Event processed successfully: stream_id=%s, event_id=%s, event_type=%s",
		message.ID, event.ID, event.Type)

	return nil
}

// parseMessage 解析Redis消息为事件
func (c *Consumer) parseMessage(message redis.XMessage) (*Event, error) {
	event := &Event{
		Data:     make(map[string]interface{}),
		Metadata: make(map[string]string),
	}

	for key, value := range message.Values {
		strValue := fmt.Sprintf("%v", value)

		switch key {
		case "id":
			event.ID = strValue
		case "type":
			event.Type = EventType(strValue)
		case "source":
			event.Source = strValue
		case "timestamp":
			if ts, err := strconv.ParseInt(strValue, 10, 64); err == nil {
				event.Timestamp = ts
			}
		default:
			if strings.HasPrefix(key, "data.") {
				dataKey := strings.TrimPrefix(key, "data.")
				// 尝试反序列化JSON字符串
				if jsonValue := tryParseJSON(strValue); jsonValue != nil {
					event.Data[dataKey] = jsonValue
				} else {
					event.Data[dataKey] = value
				}
			} else if strings.HasPrefix(key, "metadata.") {
				metaKey := strings.TrimPrefix(key, "metadata.")
				event.Metadata[metaKey] = strValue
			}
		}
	}

	return event, nil
}

// ackMessage 确认消息
func (c *Consumer) ackMessage(messageID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := c.client.XAck(ctx, c.streamName, c.groupName, messageID)
	return result.Err()
}

// tryParseJSON 尝试解析JSON字符串
func tryParseJSON(str string) interface{} {
	// 只有当字符串看起来像JSON时才尝试解析
	if len(str) == 0 {
		return nil
	}

	// 简单检查是否可能是JSON
	firstChar := str[0]
	if firstChar != '[' && firstChar != '{' && firstChar != '"' {
		return nil
	}

	var result interface{}
	if err := json.Unmarshal([]byte(str), &result); err == nil {
		return result
	}

	return nil
}

// handleEventWithRetry 处理事件（带重试机制和智能错误分类）
func (c *Consumer) handleEventWithRetry(event *Event, streamID string) error {
	if c.retryConfig == nil {
		// 没有重试配置，直接处理
		return c.handler.HandleEvent(event)
	}

	var lastErr error
	var strategy ErrorHandlingStrategy

	// 重试循环
	for attempt := 0; attempt <= c.retryConfig.MaxAttempts; attempt++ {
		if attempt > 0 {
			// 计算延迟时间（指数退避）
			delay := c.calculateBackoffDelay(attempt)
			logger.Infof("Retrying event %s, attempt %d/%d, delay=%v",
				event.ID, attempt, c.retryConfig.MaxAttempts, delay)
			time.Sleep(delay)
		}

		// 尝试处理事件
		if err := c.handler.HandleEvent(event); err == nil {
			// 成功处理
			if attempt > 0 {
				logger.Infof("Event %s processed successfully after %d retries", event.ID, attempt)
			}
			return nil
		} else {
			lastErr = err
			logger.Warningf("Event %s failed on attempt %d: %v", event.ID, attempt, err)

			// 使用错误分类器确定处理策略
			strategy = c.getErrorHandlingStrategy(err)

			// 如果不是重试策略，跳出重试循环
			if strategy != StrategyRetry {
				logger.Infof("Error classified as non-retryable for event %s, strategy=%d: %v",
					event.ID, strategy, err)
				break
			}
		}
	}

	// 根据策略处理失败的事件
	return c.handleFailedEvent(event, lastErr, strategy, streamID)
}

// getErrorHandlingStrategy 获取错误处理策略
func (c *Consumer) getErrorHandlingStrategy(err error) ErrorHandlingStrategy {
	// 如果有自定义错误分类器，使用它
	if c.errorClassifier != nil {
		return c.errorClassifier.ClassifyError(err)
	}

	// 回退到默认的错误分类逻辑
	return c.getDefaultErrorStrategy(err)
}

// getDefaultErrorStrategy 默认错误分类策略
func (c *Consumer) getDefaultErrorStrategy(err error) ErrorHandlingStrategy {
	if err == nil {
		return StrategyDLQ
	}

	errStr := strings.ToLower(err.Error())

	// 网络和服务器错误 -> 重试
	if strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "deadline") ||
		strings.Contains(errStr, "500") ||
		strings.Contains(errStr, "502") ||
		strings.Contains(errStr, "503") ||
		strings.Contains(errStr, "504") {
		return StrategyRetry
	}

	// 认证和权限错误 -> 丢弃
	if strings.Contains(errStr, "401") ||
		strings.Contains(errStr, "403") ||
		strings.Contains(errStr, "unauthorized") ||
		strings.Contains(errStr, "forbidden") ||
		strings.Contains(errStr, "authentication") ||
		strings.Contains(errStr, "invalid token") {
		return StrategyDiscard
	}

	// 其他错误（包括4xx业务错误）-> DLQ
	return StrategyDLQ
}

// handleFailedEvent 处理失败的事件
func (c *Consumer) handleFailedEvent(event *Event, err error, strategy ErrorHandlingStrategy, streamID string) error {
	switch strategy {
	case StrategyDiscard:
		// 直接丢弃，记录日志
		logger.Errorf("Event %s discarded due to non-recoverable error: %v", event.ID, err)
		return nil // 返回nil表示消息已处理（丢弃）

	case StrategyDLQ:
		// 发送到死信队列
		if c.deadLetterQueue != nil {
			logger.Errorf("Event %s failed, sending to dead letter queue: %v", event.ID, err)

			if dlqErr := c.deadLetterQueue.SendToDeadLetter(event, err, c.retryConfig.MaxAttempts, streamID); dlqErr != nil {
				logger.Errorf("Failed to send event %s to dead letter queue: %v", event.ID, dlqErr)
				return fmt.Errorf("event processing failed and DLQ send failed: %v", dlqErr)
			}

			return nil // 返回nil表示消息已处理（放入死信队列）
		}

		// 如果没有死信队列，返回错误
		return fmt.Errorf("event %s failed and no DLQ configured: %v", event.ID, err)

	default:
		// 不应该到达这里，但为了安全起见
		logger.Errorf("Unknown error handling strategy %d for event %s: %v", strategy, event.ID, err)
		return fmt.Errorf("event %s failed with unknown strategy: %v", event.ID, err)
	}
}

// calculateBackoffDelay 计算退避延迟时间
func (c *Consumer) calculateBackoffDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return 0
	}

	delay := c.retryConfig.InitialInterval
	for i := 1; i < attempt; i++ {
		delay = time.Duration(float64(delay) * c.retryConfig.Multiplier)
		if delay > c.retryConfig.MaxInterval {
			delay = c.retryConfig.MaxInterval
			break
		}
	}

	return delay
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	c.Stop()
	return c.client.Close()
}
