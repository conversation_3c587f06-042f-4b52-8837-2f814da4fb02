package events

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

// EventType 事件类型
type EventType string

// MarshalBinary 实现 encoding.BinaryMarshaler 接口
func (et EventType) MarshalBinary() ([]byte, error) {
	return []byte(string(et)), nil
}

// UnmarshalBinary 实现 encoding.BinaryUnmarshaler 接口
func (et *EventType) UnmarshalBinary(data []byte) error {
	*et = EventType(string(data))
	return nil
}

const (
	// 节点事件
	EventNodeCreate EventType = "node.create"
	EventNodeUpdate EventType = "node.update"
	EventNodeDelete EventType = "node.delete"

	// 主机事件
	EventHostCreate EventType = "host.create"
	EventHostUpdate EventType = "host.update"
	EventHostDelete EventType = "host.delete"

	// 资源事件
	EventResourceCreate EventType = "resource.create"
	EventResourceUpdate EventType = "resource.update"
	EventResourceDelete EventType = "resource.delete"

	// 资源绑定事件
	EventResourceBind   EventType = "resource.bind"
	EventResourceUnbind EventType = "resource.unbind"

	// 用户管理事件
	EventUserCreate         EventType = "user.create"
	EventUserUpdate         EventType = "user.update"
	EventUserDelete         EventType = "user.delete"
	EventUserChangePassword EventType = "user.change_password"
	EventUserEnable         EventType = "user.enable"
	EventUserDisable        EventType = "user.disable"

	// 用户组事件
	EventTeamCreate       EventType = "team.create"
	EventTeamUpdate       EventType = "team.update"
	EventTeamDelete       EventType = "team.delete"
	EventTeamAddMember    EventType = "team.add_member"
	EventTeamRemoveMember EventType = "team.remove_member"
	EventTeamAddUser      EventType = "team.add_user"
	EventTeamRemoveUser   EventType = "team.remove_user"

	// 权限事件
	EventPermissionGrant  EventType = "permission.grant"
	EventPermissionRevoke EventType = "permission.revoke"

	// 账号模板事件
	EventAccountTemplateCreate EventType = "account_template.create"
	EventAccountTemplateUpdate EventType = "account_template.update"
	EventAccountTemplateDelete EventType = "account_template.delete"

	// 账号事件
	EventAccountCreate EventType = "account.create"
	EventAccountUpdate EventType = "account.update"
	EventAccountDelete EventType = "account.delete"

	// 账号绑定事件
	EventAccountBind   EventType = "account.bind"
	EventAccountUnbind EventType = "account.unbind"
)

// Event RDB事件结构
type Event struct {
	ID        string                 `json:"id"`        // 事件ID
	Type      EventType              `json:"type"`      // 事件类型
	Source    string                 `json:"source"`    // 事件源，rdb或ams
	Timestamp int64                  `json:"timestamp"` // 事件时间戳
	Data      map[string]interface{} `json:"data"`      // 事件数据
	Metadata  map[string]string      `json:"metadata"`  // 元数据
}

// NodeEventData 节点事件数据
type NodeEventData struct {
	ID           interface{} `json:"id"`  // 支持字符串或数字格式
	PID          interface{} `json:"pid"` // 支持字符串或数字格式
	Ident        string      `json:"ident"`
	Name         string      `json:"name"`
	OriginalName string      `json:"original_name"` // 原始名称（更新前的名称）
	Note         string      `json:"note"`
	Path         string      `json:"path"`
	Leaf         interface{} `json:"leaf"` // 支持字符串或数字格式
	Cate         string      `json:"cate"`
	IconColor    string      `json:"icon_color"`
	IconChar     string      `json:"icon_char"`
	Proxy        interface{} `json:"proxy"` // 支持字符串或数字格式
	Creator      string      `json:"creator"`
	LastUpdated  interface{} `json:"last_updated"` // 支持字符串或数字格式
}

// HostEventData 主机事件数据
type HostEventData struct {
	ID            interface{} `json:"id"`    // 支持字符串或数字格式
	SN            string      `json:"sn"`
	IP            string      `json:"ip"`
	Ident         string      `json:"ident"`
	Name          string      `json:"name"`
	OSVersion     string      `json:"os_version"`
	KernelVersion string      `json:"kernel_version"`
	CPUModel      string      `json:"cpu_model"`
	CPU           string      `json:"cpu"`
	Mem           string      `json:"mem"`
	Disk          string      `json:"disk"`
	Note          string      `json:"note"`
	Cate          string      `json:"cate"`
	Tenant        string      `json:"tenant"`
	Clock         interface{} `json:"clock"` // 支持字符串或数字格式
	GPU           string      `json:"gpu"`
	GPUModel      string      `json:"gpu_model"`
	Model         string      `json:"model"`
	IDC           string      `json:"idc"`
	Zone          string      `json:"zone"`
	Rack          string      `json:"rack"`
	Manufacturer  string      `json:"manufacturer"`
}

// ResourceEventData 资源事件数据
type ResourceEventData struct {
	ID     int64             `json:"id"`
	UUID   string            `json:"uuid"`
	Ident  string            `json:"ident"`
	Name   string            `json:"name"`
	Labels map[string]string `json:"labels"`
	Extend map[string]string `json:"extend"`
	Cate   string            `json:"cate"`
	Tenant string            `json:"tenant"`
}

// ResourceBindEventData 资源绑定事件数据
type ResourceBindEventData struct {
	NodeID       interface{} `json:"node_id"`     // 支持字符串或数字格式
	ResourceID   interface{} `json:"resource_id"` // 支持字符串或数字格式
	NodePath     string      `json:"node_path"`
	ResourceUUID string      `json:"resource_uuid"`
}

// UserEventData 用户事件数据
type UserEventData struct {
	ID           interface{} `json:"id"` // 支持字符串或数字格式
	UUID         string      `json:"uuid"`
	Username     string      `json:"username"`
	Password     string      `json:"password"` // 明文密码
	Dispname     string      `json:"dispname"`
	Email        string      `json:"email"`
	Phone        string      `json:"phone"`
	Organization string      `json:"organization"`
	Status       interface{} `json:"status"`       // 支持字符串或数字格式
	IsRoot       interface{} `json:"is_root"`      // 支持字符串或数字格式
	Type         interface{} `json:"type"`         // 支持字符串或数字格式
	ActiveBegin  interface{} `json:"active_begin"` // 支持字符串或数字格式
	ActiveEnd    interface{} `json:"active_end"`   // 支持字符串或数字格式
}

// TeamEventData 用户组事件数据
type TeamEventData struct {
	ID               interface{} `json:"id"` // 支持字符串或数字格式
	Ident            string      `json:"ident"`
	Name             string      `json:"name"`
	Note             string      `json:"note"`
	Mgmt             interface{} `json:"mgmt"`               // 支持字符串或数字格式，0: 普通组, 1: 管理组
	Members          interface{} `json:"members"`            // 支持数组格式，普通成员ID列表
	Admins           interface{} `json:"admins"`             // 支持数组格式，管理员ID列表
	Creator          interface{} `json:"creator"`            // 支持字符串或数字格式
	OperationUserIDs interface{} `json:"operation_user_ids"` // 操作相关的用户ID列表（添加/移除的用户）
}

// PasswordChangeEventData 密码变更事件数据
type PasswordChangeEventData struct {
	UserID      interface{} `json:"user_id"` // 支持字符串或数字格式
	Username    string      `json:"username"`
	NewPassword string      `json:"new_password"`
}

// PermissionEventData 权限事件数据
type PermissionEventData struct {
	NodeID     interface{} `json:"node_id"` // 支持字符串或数字格式
	NodePath   string      `json:"node_path"`
	UserIDs    interface{} `json:"user_ids"`   // 支持数组格式
	TeamIDs    interface{} `json:"team_ids"`   // 支持数组格式
	Operation  string      `json:"operation"`  // grant/revoke
	Permission string      `json:"permission"` // 权限类型
	RoleID     interface{} `json:"role_id"`    // 支持字符串或数字格式
	RoleName   string      `json:"role_name"`  // 角色名称
}

// AccountTemplateEventData 账号模板事件数据
type AccountTemplateEventData struct {
	ID             interface{} `json:"id"` // 支持字符串或数字格式
	UUID           string      `json:"uuid"`
	Name           string      `json:"name"`
	Username       string      `json:"username"`
	SecretType     string      `json:"secret_type"`
	Secret         string      `json:"secret,omitempty"`
	Passphrase     string      `json:"passphrase,omitempty"`
	SecretStrategy string      `json:"secret_strategy"`
	SuFrom         interface{} `json:"su_from"`    // 支持字符串或数字格式
	Privileged     interface{} `json:"privileged"` // 支持布尔或数字格式
	IsActive       interface{} `json:"is_active"`  // 支持布尔或数字格式
	AutoPush       interface{} `json:"auto_push"`  // 支持布尔或数字格式
	Comment        string      `json:"comment"`
	Creator        string      `json:"creator"`
	CreatedAt      interface{} `json:"created_at"` // 支持字符串或数字格式
	UpdatedAt      interface{} `json:"updated_at"` // 支持字符串或数字格式
	// 用于模板更新时查找原始模板的字段
	OriginalName string `json:"original_name,omitempty"`
}

// AccountEventData 账号事件数据
type AccountEventData struct {
	ID           interface{} `json:"id"` // 支持字符串或数字格式
	UUID         string      `json:"uuid"`
	Name         string      `json:"name"`
	Username     string      `json:"username"`
	SecretType   string      `json:"secret_type"`
	Secret       string      `json:"secret,omitempty"`
	Passphrase   string      `json:"passphrase,omitempty"`
	AssetUUID    string      `json:"asset_uuid"`
	AssetName    string      `json:"asset_name"`
	AssetAddress string      `json:"asset_address"`
	TemplateID   interface{} `json:"template_id"` // 支持字符串或数字格式
	TemplateUUID string      `json:"template_uuid"`
	Source       string      `json:"source"`
	Privileged   interface{} `json:"privileged"`   // 支持布尔或数字格式
	IsActive     interface{} `json:"is_active"`    // 支持布尔或数字格式
	SecretReset  interface{} `json:"secret_reset"` // 支持布尔或数字格式
	PushNow      interface{} `json:"push_now"`     // 支持布尔或数字格式
	Comment      string      `json:"comment"`
	Creator      string      `json:"creator"`
	CreatedAt    interface{} `json:"created_at"` // 支持字符串或数字格式
	UpdatedAt    interface{} `json:"updated_at"` // 支持字符串或数字格式
	// 用于账号更新时查找原始账号的字段
	OriginalName     string `json:"original_name,omitempty"`
	OriginalUsername string `json:"original_username,omitempty"`
}

// AccountBindEventData 账号绑定事件数据
type AccountBindEventData struct {
	AccountID   interface{} `json:"account_id"` // 支持字符串或数字格式
	AccountUUID string      `json:"account_uuid"`
	AssetUUID   string      `json:"asset_uuid"`
	ResourceID  interface{} `json:"resource_id"`  // 支持字符串或数字格式
	NodeID      interface{} `json:"node_id"`      // 支持字符串或数字格式
	BindingType string      `json:"binding_type"` // manual, auto, template
	IsActive    interface{} `json:"is_active"`    // 支持布尔或数字格式
	Creator     string      `json:"creator"`
	CreatedAt   interface{} `json:"created_at"` // 支持字符串或数字格式
}

// NewEvent 创建新事件
func NewEvent(eventType EventType, source string, data interface{}) *Event {
	eventData := make(map[string]interface{})

	// 将数据转换为map
	if data != nil {
		dataBytes, _ := json.Marshal(data)
		json.Unmarshal(dataBytes, &eventData)
	}

	return &Event{
		ID:        generateEventID(),
		Type:      eventType,
		Source:    source,
		Timestamp: time.Now().Unix(),
		Data:      eventData,
		Metadata:  make(map[string]string),
	}
}

// ToJSON 转换为JSON字符串
func (e *Event) ToJSON() (string, error) {
	data, err := json.Marshal(e)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析事件
func FromJSON(jsonStr string) (*Event, error) {
	var event Event
	err := json.Unmarshal([]byte(jsonStr), &event)
	if err != nil {
		return nil, err
	}
	return &event, nil
}

// generateEventID 生成事件ID
func generateEventID() string {
	// 使用更规范的格式：时间戳(毫秒)-随机字符串
	timestamp := time.Now().UnixMilli()
	randomPart := randomString(8)
	return fmt.Sprintf("%d-%s", timestamp, randomPart)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)

	// 使用加密安全的随机数生成器
	randBytes := make([]byte, length)
	if _, err := rand.Read(randBytes); err != nil {
		// 如果加密随机数失败，回退到时间戳方式（但改进算法）
		seed := time.Now().UnixNano()
		for i := range b {
			seed = seed*1103515245 + 12345 // 线性同余生成器
			b[i] = charset[seed%int64(len(charset))]
		}
		return string(b)
	}

	// 使用加密随机数
	for i := range b {
		b[i] = charset[randBytes[i]%byte(len(charset))]
	}
	return string(b)
}

// GetID 获取用户ID
func (u *UserEventData) GetID() int64 {
	return parseInt64(u.ID)
}

// GetStatus 获取用户状态
func (u *UserEventData) GetStatus() int {
	return parseInt(u.Status)
}

// GetIsRoot 获取是否为root用户
func (u *UserEventData) GetIsRoot() int {
	return parseInt(u.IsRoot)
}

// GetType 获取用户类型
func (u *UserEventData) GetType() int {
	return parseInt(u.Type)
}

// GetActiveBeginTimestamp 获取ActiveBegin的时间戳
func (u *UserEventData) GetActiveBeginTimestamp() int64 {
	return parseTimestamp(u.ActiveBegin)
}

// GetActiveEndTimestamp 获取ActiveEnd的时间戳
func (u *UserEventData) GetActiveEndTimestamp() int64 {
	return parseTimestamp(u.ActiveEnd)
}

// GetUserID 获取密码变更事件的用户ID
func (p *PasswordChangeEventData) GetUserID() int64 {
	return parseInt64(p.UserID)
}

// GetID 获取团队ID
func (t *TeamEventData) GetID() int64 {
	return parseInt64(t.ID)
}

// GetMgmt 获取团队管理类型
func (t *TeamEventData) GetMgmt() int {
	return parseInt(t.Mgmt)
}

// GetCreator 获取团队创建者ID
func (t *TeamEventData) GetCreator() int64 {
	return parseInt64(t.Creator)
}

// GetMembers 获取团队成员ID列表
func (t *TeamEventData) GetMembers() []int64 {
	return parseInt64Array(t.Members)
}

// GetAdmins 获取团队管理员ID列表
func (t *TeamEventData) GetAdmins() []int64 {
	return parseInt64Array(t.Admins)
}

// GetOperationUserIDs 获取操作相关的用户ID列表
func (t *TeamEventData) GetOperationUserIDs() []int64 {
	return parseInt64Array(t.OperationUserIDs)
}

// GetNodeID 获取节点ID
func (n *NodeEventData) GetNodeID() int64 {
	return parseInt64(n.ID)
}

// GetNodePID 获取节点父ID
func (n *NodeEventData) GetNodePID() int64 {
	return parseInt64(n.PID)
}

// GetNodeLeaf 获取节点叶子标识
func (n *NodeEventData) GetNodeLeaf() int {
	return parseInt(n.Leaf)
}

// GetNodeProxy 获取节点代理标识
func (n *NodeEventData) GetNodeProxy() int {
	return parseInt(n.Proxy)
}

// GetNodeLastUpdated 获取节点最后更新时间
func (n *NodeEventData) GetNodeLastUpdated() int64 {
	return parseInt64(n.LastUpdated)
}

// GetClock 获取主机时钟时间戳
func (h *HostEventData) GetClock() int64 {
	return parseInt64(h.Clock)
}

// GetID 获取主机ID
func (h *HostEventData) GetID() int64 {
	return parseInt64(h.ID)
}

// GetResourceNodeID 获取资源绑定的节点ID
func (r *ResourceBindEventData) GetResourceNodeID() int64 {
	return parseInt64(r.NodeID)
}

// GetResourceID 获取资源ID
func (r *ResourceBindEventData) GetResourceID() int64 {
	return parseInt64(r.ResourceID)
}

// GetPermissionNodeID 获取权限事件的节点ID
func (p *PermissionEventData) GetPermissionNodeID() int64 {
	return parseInt64(p.NodeID)
}

// GetPermissionRoleID 获取权限事件的角色ID
func (p *PermissionEventData) GetPermissionRoleID() int64 {
	return parseInt64(p.RoleID)
}

// GetPermissionUserIDs 获取权限事件的用户ID列表
func (p *PermissionEventData) GetPermissionUserIDs() []int64 {
	return parseInt64Array(p.UserIDs)
}

// GetPermissionTeamIDs 获取权限事件的团队ID列表
func (p *PermissionEventData) GetPermissionTeamIDs() []int64 {
	return parseInt64Array(p.TeamIDs)
}

// GetID 获取账号模板ID
func (a *AccountTemplateEventData) GetID() int64 {
	return parseInt64(a.ID)
}

// GetSuFrom 获取账号模板的SuFrom
func (a *AccountTemplateEventData) GetSuFrom() int64 {
	return parseInt64(a.SuFrom)
}

// GetPrivileged 获取账号模板是否为特权账号
func (a *AccountTemplateEventData) GetPrivileged() bool {
	return parseBool(a.Privileged)
}

// GetIsActive 获取账号模板是否激活
func (a *AccountTemplateEventData) GetIsActive() bool {
	return parseBool(a.IsActive)
}

// GetAutoPush 获取账号模板是否自动推送
func (a *AccountTemplateEventData) GetAutoPush() bool {
	return parseBool(a.AutoPush)
}

// GetID 获取账号ID
func (a *AccountEventData) GetID() int64 {
	return parseInt64(a.ID)
}

// GetTemplateID 获取账号的模板ID
func (a *AccountEventData) GetTemplateID() int64 {
	return parseInt64(a.TemplateID)
}

// GetPrivileged 获取账号是否为特权账号
func (a *AccountEventData) GetPrivileged() bool {
	return parseBool(a.Privileged)
}

// GetIsActive 获取账号是否激活
func (a *AccountEventData) GetIsActive() bool {
	return parseBool(a.IsActive)
}

// GetSecretReset 获取账号是否可改密
func (a *AccountEventData) GetSecretReset() bool {
	return parseBool(a.SecretReset)
}

// GetPushNow 获取账号是否立即推送
func (a *AccountEventData) GetPushNow() bool {
	return parseBool(a.PushNow)
}

// GetAccountID 获取账号绑定的账号ID
func (a *AccountBindEventData) GetAccountID() int64 {
	return parseInt64(a.AccountID)
}

// GetResourceID 获取账号绑定的资源ID
func (a *AccountBindEventData) GetResourceID() int64 {
	return parseInt64(a.ResourceID)
}

// GetNodeID 获取账号绑定的节点ID
func (a *AccountBindEventData) GetNodeID() int64 {
	return parseInt64(a.NodeID)
}

// parseInt 解析整数，支持字符串和数字格式
func parseInt(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if v == "" {
			return 0
		}
		if num, err := strconv.Atoi(v); err == nil {
			return num
		}
		return 0
	default:
		return 0
	}
}

// parseInt64 解析int64，支持字符串和数字格式
func parseInt64(value interface{}) int64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		if v == "" {
			return 0
		}
		if num, err := strconv.ParseInt(v, 10, 64); err == nil {
			return num
		}
		return 0
	default:
		return 0
	}
}

// parseTimestamp 解析时间戳，支持字符串和数字格式
func parseTimestamp(value interface{}) int64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		if v == "" {
			return 0
		}
		// 尝试解析为数字
		if timestamp, err := strconv.ParseInt(v, 10, 64); err == nil {
			return timestamp
		}
		// 尝试解析为时间格式
		if t, err := time.Parse("2006-01-02 15:04:05", v); err == nil {
			return t.Unix()
		}
		if t, err := time.Parse(time.RFC3339, v); err == nil {
			return t.Unix()
		}
		return 0
	default:
		return 0
	}
}

// parseInt64Array 解析int64数组，支持多种格式
func parseInt64Array(value interface{}) []int64 {
	if value == nil {
		return []int64{}
	}

	switch v := value.(type) {
	case []int64:
		return v
	case []interface{}:
		result := make([]int64, 0, len(v))
		for _, item := range v {
			if id := parseInt64(item); id != 0 {
				result = append(result, id)
			}
		}
		return result
	case []int:
		result := make([]int64, len(v))
		for i, item := range v {
			result[i] = int64(item)
		}
		return result
	case []float64:
		result := make([]int64, len(v))
		for i, item := range v {
			result[i] = int64(item)
		}
		return result
	case string:
		if v == "" || v == "[]" {
			return []int64{}
		}
		// 尝试解析JSON数组
		var arr []interface{}
		if err := json.Unmarshal([]byte(v), &arr); err == nil {
			return parseInt64Array(arr)
		}
		return []int64{}
	default:
		return []int64{}
	}
}

// parseBool 解析布尔值，支持多种格式
func parseBool(value interface{}) bool {
	switch v := value.(type) {
	case bool:
		return v
	case int:
		return v != 0
	case int64:
		return v != 0
	case float64:
		return v != 0
	case string:
		return v == "true" || v == "1" || v == "True" || v == "TRUE"
	default:
		return false
	}
}

// DeadLetterMessage 死信队列消息结构
type DeadLetterMessage struct {
	OriginalEvent *Event `json:"original_event"` // 原始事件
	ErrorMessage  string `json:"error_message"`  // 错误信息
	FailedAt      int64  `json:"failed_at"`      // 失败时间
	RetryCount    int    `json:"retry_count"`    // 重试次数
	LastError     string `json:"last_error"`     // 最后一次错误
	FailureReason string `json:"failure_reason"` // 失败原因分类
	StreamID      string `json:"stream_id"`      // 原始消息ID
}

// ErrorType 错误类型枚举
type ErrorType string

const (
	ErrorTypeNetwork    ErrorType = "NETWORK_ERROR"     // 网络错误
	ErrorTypeAuth       ErrorType = "AUTH_ERROR"        // 认证错误
	ErrorTypeValidation ErrorType = "VALIDATION_ERROR"  // 验证错误
	ErrorTypeNotFound   ErrorType = "NOT_FOUND_ERROR"   // 资源不存在
	ErrorTypeServer     ErrorType = "SERVER_ERROR"      // 服务器错误
	ErrorTypeTimeout    ErrorType = "TIMEOUT_ERROR"     // 超时错误
	ErrorTypeConflict   ErrorType = "CONFLICT_ERROR"    // 冲突错误(409)
	ErrorTypeBadRequest ErrorType = "BAD_REQUEST_ERROR" // 请求错误(400)
	ErrorTypeUnknown    ErrorType = "UNKNOWN_ERROR"     // 未知错误
)

// ErrorHandlingStrategy 错误处理策略
type ErrorHandlingStrategy int

const (
	StrategyRetry   ErrorHandlingStrategy = iota // 重试
	StrategyDLQ                                  // 进入死信队列
	StrategyDiscard                              // 直接丢弃
)

// IsRetryable 判断错误类型是否可重试
func (et ErrorType) IsRetryable() bool {
	switch et {
	case ErrorTypeNetwork, ErrorTypeServer, ErrorTypeTimeout:
		return true
	case ErrorTypeAuth, ErrorTypeValidation, ErrorTypeNotFound:
		return false
	case ErrorTypeConflict, ErrorTypeBadRequest:
		return false // 这些错误不重试，但应该进入DLQ
	default:
		return true // 未知错误默认可重试
	}
}

// GetHandlingStrategy 获取错误处理策略
func (et ErrorType) GetHandlingStrategy() ErrorHandlingStrategy {
	switch et {
	case ErrorTypeNetwork, ErrorTypeServer, ErrorTypeTimeout:
		return StrategyRetry
	case ErrorTypeAuth:
		return StrategyDiscard // 认证错误直接丢弃
	case ErrorTypeValidation, ErrorTypeNotFound, ErrorTypeConflict, ErrorTypeBadRequest:
		return StrategyDLQ // 这些错误进入DLQ等待人工处理
	default:
		return StrategyDLQ // 未知错误进入DLQ保险起见
	}
}
