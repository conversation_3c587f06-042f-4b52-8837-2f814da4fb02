package events

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/toolkits/pkg/logger"
)

// Producer Redis Streams事件生产者
type Producer struct {
	client     *redis.Client
	streamName string
}

// NewProducer 创建新的事件生产者
func NewProducer(redisAddr, password string, db int, streamName string) (*Producer, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %v", err)
	}

	return &Producer{
		client:     client,
		streamName: streamName,
	}, nil
}

// PublishEvent 发布事件到Redis Streams
func (p *Producer) PublishEvent(event *Event) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 将事件转换为Redis Streams的字段格式
	fields := map[string]interface{}{
		"id":        event.ID,
		"type":      string(event.Type),
		"source":    event.Source,
		"timestamp": event.Timestamp,
	}

	// 添加事件数据
	for key, value := range event.Data {
		// 对于复杂类型，先序列化为JSON字符串
		if needsJSONSerialization(value) {
			if jsonBytes, err := json.Marshal(value); err == nil {
				fields[fmt.Sprintf("data.%s", key)] = string(jsonBytes)
			} else {
				logger.Errorf("Failed to serialize field %s: %v", key, err)
				fields[fmt.Sprintf("data.%s", key)] = fmt.Sprintf("%v", value)
			}
		} else {
			fields[fmt.Sprintf("data.%s", key)] = value
		}
	}

	// 添加元数据
	for key, value := range event.Metadata {
		fields[fmt.Sprintf("metadata.%s", key)] = value
	}

	// 发送到Redis Streams
	result := p.client.XAdd(ctx, &redis.XAddArgs{
		Stream: p.streamName,
		Values: fields,
	})

	if err := result.Err(); err != nil {
		logger.Infof("Failed to publish event to Redis Streams: stream=%s, event_id=%s, event_type=%s, error=%v",
			p.streamName, event.ID, event.Type, err)
		return fmt.Errorf("failed to publish event: %v", err)
	}

	streamID := result.Val()
	logger.Infof("Event published successfully: stream=%s, stream_id=%s, event_id=%s, event_type=%s",
		p.streamName, streamID, event.ID, event.Type)

	return nil
}

// PublishNodeEvent 发布节点事件
func (p *Producer) PublishNodeEvent(eventType EventType, nodeData *NodeEventData) error {
	event := NewEvent(eventType, "rdb", nodeData)
	event.Metadata["entity_type"] = "node"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", nodeData.GetNodeID())
	event.Metadata["node_path"] = nodeData.Path
	event.Metadata["node_ident"] = nodeData.Ident
	return p.PublishEvent(event)
}

// PublishHostEvent 发布主机事件
func (p *Producer) PublishHostEvent(eventType EventType, hostData *HostEventData) error {
	event := NewEvent(eventType, "ams", hostData)
	event.Metadata["entity_type"] = "host"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", hostData.GetID())
	return p.PublishEvent(event)
}

// PublishResourceEvent 发布资源事件
func (p *Producer) PublishResourceEvent(eventType EventType, resourceData *ResourceEventData) error {
	event := NewEvent(eventType, "rdb", resourceData)
	event.Metadata["entity_type"] = "resource"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", resourceData.ID)
	return p.PublishEvent(event)
}

// PublishResourceBindEvent 发布资源绑定事件
func (p *Producer) PublishResourceBindEvent(eventType EventType, bindData *ResourceBindEventData) error {
	event := NewEvent(eventType, "rdb", bindData)
	event.Metadata["entity_type"] = "resource_bind"
	event.Metadata["node_id"] = fmt.Sprintf("%d", bindData.GetResourceNodeID())
	event.Metadata["resource_id"] = fmt.Sprintf("%d", bindData.GetResourceID())
	event.Metadata["node_path"] = bindData.NodePath
	event.Metadata["resource_uuid"] = bindData.ResourceUUID
	return p.PublishEvent(event)
}

// PublishUserEvent 发布用户事件
func (p *Producer) PublishUserEvent(eventType EventType, userData *UserEventData) error {
	event := NewEvent(eventType, "rdb", userData)
	event.Metadata["entity_type"] = "user"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", userData.GetID())
	event.Metadata["username"] = userData.Username
	return p.PublishEvent(event)
}

// PublishPasswordChangeEvent 发布密码变更事件
func (p *Producer) PublishPasswordChangeEvent(passwordData *PasswordChangeEventData) error {
	event := NewEvent(EventUserChangePassword, "rdb", passwordData)
	event.Metadata["entity_type"] = "user"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", passwordData.GetUserID())
	event.Metadata["username"] = passwordData.Username
	return p.PublishEvent(event)
}

// PublishTeamEvent 发布团队事件
func (p *Producer) PublishTeamEvent(eventType EventType, teamData *TeamEventData) error {
	event := NewEvent(eventType, "rdb", teamData)
	event.Metadata["entity_type"] = "team"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", teamData.ID)
	event.Metadata["team_ident"] = teamData.Ident
	return p.PublishEvent(event)
}

// PublishTeamAddMemberEvent 发布团队添加成员事件
func (p *Producer) PublishTeamAddMemberEvent(teamData *TeamEventData) error {
	event := NewEvent(EventTeamAddMember, "rdb", teamData)
	event.Metadata["entity_type"] = "team"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", teamData.GetID())
	event.Metadata["team_ident"] = teamData.Ident
	return p.PublishEvent(event)
}

// PublishTeamRemoveMemberEvent 发布团队移除成员事件
func (p *Producer) PublishTeamRemoveMemberEvent(teamData *TeamEventData) error {
	event := NewEvent(EventTeamRemoveMember, "rdb", teamData)
	event.Metadata["entity_type"] = "team"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", teamData.GetID())
	event.Metadata["team_ident"] = teamData.Ident
	return p.PublishEvent(event)
}

// PublishAccountTemplateEvent 发布账号模板事件
func (p *Producer) PublishAccountTemplateEvent(eventType EventType, templateData *AccountTemplateEventData) error {
	event := NewEvent(eventType, "rdb", templateData)
	event.Metadata["entity_type"] = "account_template"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", templateData.GetID())
	event.Metadata["template_uuid"] = templateData.UUID
	event.Metadata["template_name"] = templateData.Name
	return p.PublishEvent(event)
}

// PublishAccountEvent 发布账号事件
func (p *Producer) PublishAccountEvent(eventType EventType, accountData *AccountEventData) error {
	event := NewEvent(eventType, "rdb", accountData)
	event.Metadata["entity_type"] = "account"
	event.Metadata["entity_id"] = fmt.Sprintf("%d", accountData.GetID())
	event.Metadata["account_uuid"] = accountData.UUID
	event.Metadata["username"] = accountData.Username
	event.Metadata["asset_uuid"] = accountData.AssetUUID
	return p.PublishEvent(event)
}

// PublishAccountBindEvent 发布账号绑定事件
func (p *Producer) PublishAccountBindEvent(eventType EventType, bindData *AccountBindEventData) error {
	event := NewEvent(eventType, "rdb", bindData)
	event.Metadata["entity_type"] = "account_bind"
	event.Metadata["account_id"] = fmt.Sprintf("%d", bindData.GetAccountID())
	event.Metadata["account_uuid"] = bindData.AccountUUID
	event.Metadata["asset_uuid"] = bindData.AssetUUID
	event.Metadata["binding_type"] = bindData.BindingType
	return p.PublishEvent(event)
}

// PublishAccountUnbindEvent 发布账号解绑事件
func (p *Producer) PublishAccountUnbindEvent(eventType EventType, bindData *AccountBindEventData) error {
	event := NewEvent(eventType, "rdb", bindData)
	event.Metadata["entity_type"] = "account_unbind"
	event.Metadata["account_id"] = fmt.Sprintf("%d", bindData.GetAccountID())
	event.Metadata["account_uuid"] = bindData.AccountUUID
	event.Metadata["asset_uuid"] = bindData.AssetUUID
	event.Metadata["binding_type"] = bindData.BindingType
	return p.PublishEvent(event)
}

// needsJSONSerialization 判断值是否需要JSON序列化
func needsJSONSerialization(value interface{}) bool {
	if value == nil {
		return false
	}

	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.Slice, reflect.Array:
		// 数组和切片需要序列化
		return true
	case reflect.Map, reflect.Struct:
		// 映射和结构体需要序列化
		return true
	case reflect.Ptr:
		// 指针类型，检查指向的类型
		if v.IsNil() {
			return false
		}
		return needsJSONSerialization(v.Elem().Interface())
	default:
		// 基本类型不需要序列化
		return false
	}
}

// PublishPermissionEvent 发布权限事件
func (p *Producer) PublishPermissionEvent(eventType EventType, permissionData *PermissionEventData) error {
	event := NewEvent(eventType, "rdb", permissionData)
	event.Metadata["entity_type"] = "permission"
	event.Metadata["node_id"] = fmt.Sprintf("%d", permissionData.GetPermissionNodeID())
	event.Metadata["node_path"] = permissionData.NodePath
	return p.PublishEvent(event)
}

// Close 关闭生产者
func (p *Producer) Close() error {
	return p.client.Close()
}

// GetStreamInfo 获取流信息
func (p *Producer) GetStreamInfo() (*redis.XInfoStream, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := p.client.XInfoStream(ctx, p.streamName)
	if err := result.Err(); err != nil {
		return nil, err
	}

	return result.Val(), nil
}

// TrimStream 修剪流，保留最近的消息
func (p *Producer) TrimStream(maxLen int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result := p.client.XTrimMaxLen(ctx, p.streamName, maxLen)
	return result.Err()
}
