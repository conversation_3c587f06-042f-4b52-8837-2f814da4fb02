# JumpServer-Sync 重试机制和死信队列技术实现文档

## 概述

本文档详细描述了 JumpServer-Sync 模块中重试机制和死信队列的技术实现，包括架构设计、代码实现、数据流转和实际示例。

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   RDB/AMS       │───▶│  Redis Streams   │───▶│   Consumer      │
│   事件发布      │    │  主队列          │    │   事件消费      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  重试机制       │
                                               │  (指数退避)     │
                                               └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  错误分类器     │
                                               │  (可重试判断)   │
                                               └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  死信队列       │
                                               │  (失败消息)     │
                                               └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  HTTP管理接口   │
                                               │  (重新处理)     │
                                               └─────────────────┘
```

### 核心组件

1. **Consumer** - 事件消费者，集成重试逻辑
2. **RetryConfig** - 重试配置结构
3. **DeadLetterQueue** - 死信队列实现
4. **AdminHandler** - HTTP管理接口

## 重试机制实现

### 1. 重试配置结构

```go
// events/consumer.go
type RetryConfig struct {
    MaxAttempts     int           // 最大重试次数
    InitialInterval time.Duration // 初始延迟时间
    MaxInterval     time.Duration // 最大延迟时间
    Multiplier      float64       // 退避倍数
    EnableDLQ       bool          // 启用死信队列
}
```

### 2. Consumer集成重试逻辑

```go
// events/consumer.go
type Consumer struct {
    client          *redis.Client
    streamName      string
    groupName       string
    consumerName    string
    batchSize       int64
    pollInterval    time.Duration
    handler         EventHandler
    retryConfig     *RetryConfig      // 重试配置
    deadLetterQueue DeadLetterQueue   // 死信队列
    stopChan        chan struct{}
}
```

### 3. 重试处理核心逻辑

```go
// events/consumer.go
func (c *Consumer) handleEventWithRetry(event *Event, streamID string) error {
    if c.retryConfig == nil {
        return c.handler.HandleEvent(event)
    }

    var lastErr error

    // 重试循环：attempt 0, 1, 2, 3...
    for attempt := 0; attempt <= c.retryConfig.MaxAttempts; attempt++ {
        if attempt > 0 {
            // 指数退避延迟
            delay := c.calculateBackoffDelay(attempt)
            logger.Infof("Retrying event %s, attempt %d/%d, delay=%v",
                event.ID, attempt, c.retryConfig.MaxAttempts, delay)
            time.Sleep(delay)
        }

        // 尝试处理事件
        if err := c.handler.HandleEvent(event); err == nil {
            if attempt > 0 {
                logger.Infof("Event %s processed successfully after %d retries", 
                           event.ID, attempt)
            }
            return nil
        } else {
            lastErr = err
            logger.Warningf("Event %s failed on attempt %d: %v", 
                          event.ID, attempt, err)

            // 判断是否为不可重试错误
            if !c.isRetryableError(err) {
                logger.Errorf("Non-retryable error for event %s: %v", 
                             event.ID, err)
                break
            }
        }
    }

    // 所有重试都失败，发送到死信队列
    if c.deadLetterQueue != nil {
        logger.Errorf("Event %s failed after %d attempts, sending to dead letter queue: %v",
            event.ID, c.retryConfig.MaxAttempts, lastErr)

        if err := c.deadLetterQueue.SendToDeadLetter(event, lastErr, 
                                                   c.retryConfig.MaxAttempts, streamID); err != nil {
            logger.Errorf("Failed to send event %s to dead letter queue: %v", 
                         event.ID, err)
            return fmt.Errorf("event processing failed and DLQ send failed: %v", err)
        }

        return nil // 返回nil表示消息已处理（放入死信队列）
    }

    return fmt.Errorf("event %s failed after %d attempts: %v", 
                     event.ID, c.retryConfig.MaxAttempts, lastErr)
}
```

### 4. 指数退避算法

```go
// events/consumer.go
func (c *Consumer) calculateBackoffDelay(attempt int) time.Duration {
    if attempt <= 0 {
        return 0
    }

    delay := c.retryConfig.InitialInterval
    for i := 1; i < attempt; i++ {
        delay = time.Duration(float64(delay) * c.retryConfig.Multiplier)
        if delay > c.retryConfig.MaxInterval {
            delay = c.retryConfig.MaxInterval
            break
        }
    }

    return delay
}
```

**延迟计算示例**：
- 配置：InitialInterval=1s, Multiplier=2.0, MaxInterval=30s
- 延迟序列：0s → 1s → 2s → 4s → 8s → 16s → 30s → 30s...

### 5. 错误分类器

```go
// events/consumer.go
func (c *Consumer) isRetryableError(err error) bool {
    if err == nil {
        return false
    }

    errStr := strings.ToLower(err.Error())

    // 网络相关错误可重试
    if strings.Contains(errStr, "connection") ||
       strings.Contains(errStr, "network") ||
       strings.Contains(errStr, "dial") ||
       strings.Contains(errStr, "refused") ||
       strings.Contains(errStr, "reset") {
        return true
    }

    // 超时错误可重试
    if strings.Contains(errStr, "timeout") ||
       strings.Contains(errStr, "deadline") ||
       strings.Contains(errStr, "context deadline exceeded") {
        return true
    }

    // 服务器错误可重试
    if strings.Contains(errStr, "500") ||
       strings.Contains(errStr, "502") ||
       strings.Contains(errStr, "503") ||
       strings.Contains(errStr, "504") ||
       strings.Contains(errStr, "internal server error") ||
       strings.Contains(errStr, "service unavailable") ||
       strings.Contains(errStr, "bad gateway") {
        return true
    }

    // 认证错误不可重试
    if strings.Contains(errStr, "401") ||
       strings.Contains(errStr, "unauthorized") ||
       strings.Contains(errStr, "403") ||
       strings.Contains(errStr, "forbidden") ||
       strings.Contains(errStr, "authentication") ||
       strings.Contains(errStr, "invalid token") {
        return false
    }

    // 资源不存在错误不可重试
    if strings.Contains(errStr, "404") ||
       strings.Contains(errStr, "not found") ||
       strings.Contains(errStr, "does not exist") {
        return false
    }

    // 验证错误不可重试
    if strings.Contains(errStr, "400") ||
       strings.Contains(errStr, "bad request") ||
       strings.Contains(errStr, "validation") ||
       strings.Contains(errStr, "invalid") ||
       strings.Contains(errStr, "malformed") {
        return false
    }

    // 默认为可重试
    return true
}
```

## 死信队列实现

### 1. 死信队列结构

```go
// deadletter/deadletter.go
type Queue struct {
    redis      *redis.Client
    mainStream string
    deadStream string
}

// 死信消息结构
type DeadLetterMessage struct {
    OriginalEvent *events.Event `json:"original_event"`
    ErrorMessage  string        `json:"error_message"`
    FailedAt      int64         `json:"failed_at"`
    RetryCount    int           `json:"retry_count"`
    LastError     string        `json:"last_error"`
    FailureReason string        `json:"failure_reason"`
    StreamID      string        `json:"stream_id"`
}
```

### 2. 发送到死信队列

```go
// deadletter/deadletter.go
func (q *Queue) SendToDeadLetter(event *events.Event, err error, retryCount int, streamID string) error {
    // 1. 构造死信消息
    deadMessage := events.DeadLetterMessage{
        OriginalEvent: event,                    // 完整的原始事件
        ErrorMessage:  err.Error(),
        FailedAt:      time.Now().Unix(),
        RetryCount:    retryCount,
        LastError:     err.Error(),
        FailureReason: string(q.classifyError(err)),
        StreamID:      streamID,                 // 原始Redis Stream ID
    }

    // 2. 序列化为JSON
    data, err := json.Marshal(deadMessage)
    if err != nil {
        return fmt.Errorf("failed to marshal dead letter message: %v", err)
    }

    // 3. 发送到死信队列
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    result := q.redis.XAdd(ctx, &redis.XAddArgs{
        Stream: q.deadStream,
        Values: map[string]interface{}{
            "data":        string(data),
            "event_type":  event.Type,
            "event_id":    event.ID,
            "failed_at":   time.Now().Format("2006-01-02 15:04:05"),
            "retry_count": retryCount,
            "error_type":  string(q.classifyError(err)),
            "stream_id":   streamID,
        },
    })

    if err := result.Err(); err != nil {
        return fmt.Errorf("failed to send message to dead letter queue: %v", err)
    }

    logger.Infof("Event %s sent to dead letter queue: %s", event.ID, result.Val())
    return nil
}
```

### 3. 重新处理实现

```go
// deadletter/deadletter.go
func (q *Queue) ReprocessMessage(messageID string) error {
    // 1. 获取死信消息
    deadMsg, err := q.GetDeadMessageByID(messageID)
    if err != nil {
        return fmt.Errorf("failed to get dead message: %v", err)
    }

    // 2. 重新发送到主队列（恢复原始格式）
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    // 构造与原始事件相同的消息格式
    values := map[string]interface{}{
        "id":        deadMsg.OriginalEvent.ID,
        "type":      string(deadMsg.OriginalEvent.Type),
        "source":    deadMsg.OriginalEvent.Source,
        "timestamp": deadMsg.OriginalEvent.Timestamp,
        
        // 添加重新处理的元数据
        "metadata.reprocessed":          "true",
        "metadata.original_failed_at":   deadMsg.FailedAt,
        "metadata.original_retry_count": deadMsg.RetryCount,
        "metadata.original_stream_id":   deadMsg.StreamID,
    }

    // 添加原始事件的数据字段
    for key, value := range deadMsg.OriginalEvent.Data {
        dataKey := fmt.Sprintf("data.%s", key)
        // 如果值是复杂对象，序列化为JSON
        if jsonData, err := json.Marshal(value); err == nil {
            values[dataKey] = string(jsonData)
        } else {
            values[dataKey] = value
        }
    }

    // 添加原始事件的元数据字段
    for key, value := range deadMsg.OriginalEvent.Metadata {
        metaKey := fmt.Sprintf("metadata.%s", key)
        values[metaKey] = value
    }

    result := q.redis.XAdd(ctx, &redis.XAddArgs{
        Stream: q.mainStream,
        Values: values,
    })

    if err := result.Err(); err != nil {
        return fmt.Errorf("failed to reprocess message: %v", err)
    }

    // 3. 从死信队列删除消息
    if err := q.DeleteMessage(messageID); err != nil {
        logger.Warningf("Failed to delete reprocessed message from DLQ: %v", err)
    }

    logger.Infof("Message %s reprocessed successfully: %s", messageID, result.Val())
    return nil
}
```

## HTTP管理接口实现

### 1. 管理接口结构

```go
// http/admin.go
type AdminHandler struct {
    deadLetterQueue *deadletter.Queue
}

func (h *AdminHandler) RegisterAdminRoutes(r *gin.Engine) {
    admin := r.Group("/api/admin")
    {
        dlq := admin.Group("/dead-letter")
        {
            dlq.GET("/messages", h.ListDeadMessages)
            dlq.GET("/messages/:id", h.GetDeadMessage)
            dlq.POST("/messages/:id/reprocess", h.ReprocessDeadMessage)
            dlq.POST("/messages/batch-reprocess", h.BatchReprocessDeadMessages)
            dlq.DELETE("/messages/:id", h.DeleteDeadMessage)
            dlq.GET("/stats", h.GetDeadLetterStats)
        }
        admin.GET("/health", h.HealthCheck)
        admin.GET("/status", h.GetSystemStatus)
    }
}
```

### 2. 重新处理接口

```go
// http/admin.go
func (h *AdminHandler) ReprocessDeadMessage(c *gin.Context) {
    messageID := c.Param("id")
    if messageID == "" {
        c.JSON(http.StatusBadRequest, gin.H{
            "error": "Message ID is required",
        })
        return
    }

    err := h.deadLetterQueue.ReprocessMessage(messageID)
    if err != nil {
        if strings.Contains(err.Error(), "not found") {
            c.JSON(http.StatusNotFound, gin.H{
                "error":  "Message not found",
                "detail": err.Error(),
            })
        } else {
            logger.Errorf("Failed to reprocess message %s: %v", messageID, err)
            c.JSON(http.StatusInternalServerError, gin.H{
                "error":  "Failed to reprocess message",
                "detail": err.Error(),
            })
        }
        return
    }

    logger.Infof("Dead message reprocessed successfully: %s", messageID)
    c.JSON(http.StatusOK, gin.H{
        "success": true,
        "message": "Message reprocessed successfully",
        "data": gin.H{
            "message_id": messageID,
        },
    })
}
```

## 配置集成

### 1. 配置文件结构

```yaml
# jumpserver-sync.yml
sync:
  retry:
    max_attempts: 3          # 最大重试次数
    initial_interval: 1s     # 初始延迟时间
    max_interval: 30s        # 最大延迟时间
    multiplier: 2.0          # 退避倍数
    enable_dlq: true         # 启用死信队列

  dead_letter:
    enabled: true                           # 启用死信队列
    stream_name: "arboris:sync:events:dead" # 死信队列名称
    max_messages: 10000                     # 最大消息数
    ttl: 604800                            # 消息保留时间(秒)
```

### 2. 主程序集成

```go
// jumpserver-sync.go
func main() {
    // ... 初始化代码 ...

    // 创建Redis客户端
    redisClient := redis.NewClient(&redis.Options{
        Addr:     config.Config.Redis.Addr,
        Password: config.Config.Redis.Password,
        DB:       config.Config.Redis.DB,
    })

    // 创建死信队列
    dlq := deadletter.NewQueue(redisClient, config.Config.Sync.Consumer.Stream)

    // 创建重试配置
    retryConfig := &events.RetryConfig{
        MaxAttempts:     config.Config.Sync.Retry.MaxAttempts,
        InitialInterval: config.Config.Sync.Retry.InitialInterval,
        MaxInterval:     config.Config.Sync.Retry.MaxInterval,
        Multiplier:      config.Config.Sync.Retry.Multiplier,
        EnableDLQ:       config.Config.Sync.Retry.EnableDLQ,
    }

    // 创建消费者
    consumer, err := events.NewConsumer(
        config.Config.Redis.Addr,
        config.Config.Redis.Password,
        config.Config.Redis.DB,
        config.Config.Sync.Consumer.Stream,
        config.Config.Sync.Consumer.Group,
        config.Config.Sync.Consumer.Consumer,
        int64(config.Config.Sync.Consumer.BatchSize),
        config.Config.Sync.Consumer.PollInterval,
        handler,
    )

    // 设置重试配置和死信队列
    consumer.SetRetryConfig(retryConfig)
    consumer.SetDeadLetterQueue(dlq)

    // 创建管理接口
    adminHandler := http.NewAdminHandler(dlq)
    http.SetAdminHandler(adminHandler)

    // 启动服务
    http.Start()
    consumer.Start()
}
```

## 实际运行示例

### 示例1：用户创建事件重试流程

#### 1. 原始事件发布
```bash
# RDB模块发布用户创建事件
redis-cli XADD arboris:sync:events "*" \
  id "user-create-123" \
  type "user.create" \
  source "rdb" \
  timestamp $(date +%s) \
  data.user_id "123" \
  data.username "john.doe" \
  data.email "<EMAIL>"
```

#### 2. 模拟JumpServer服务不可用
```bash
# 临时阻断JumpServer连接
sudo iptables -A OUTPUT -d jumpserver_ip -j DROP
```

#### 3. 重试过程日志
```
[INFO] Processing event: stream_id=1703123456789-0, event_id=user-create-123, event_type=user.create
[WARN] Event user-create-123 failed on attempt 0: dial tcp jumpserver_ip:8080: connect: connection refused
[INFO] Error classified as: NETWORK_ERROR (retryable)
[INFO] Retrying event user-create-123, attempt 1/3, delay=1s
[WARN] Event user-create-123 failed on attempt 1: dial tcp jumpserver_ip:8080: connect: connection refused
[INFO] Retrying event user-create-123, attempt 2/3, delay=2s
[WARN] Event user-create-123 failed on attempt 2: dial tcp jumpserver_ip:8080: connect: connection refused
[INFO] Retrying event user-create-123, attempt 3/3, delay=4s
[WARN] Event user-create-123 failed on attempt 3: dial tcp jumpserver_ip:8080: connect: connection refused
[ERROR] Event user-create-123 failed after 3 attempts, sending to dead letter queue: connection refused
[INFO] Event user-create-123 sent to dead letter queue: 1703123470123-0
[INFO] Event processed successfully: stream_id=1703123456789-0, event_id=user-create-123, event_type=user.create
```

#### 4. 死信队列中的消息
```json
{
  "original_event": {
    "id": "user-create-123",
    "type": "user.create",
    "source": "rdb",
    "timestamp": 1703123456,
    "data": {
      "user_id": "123",
      "username": "john.doe",
      "email": "<EMAIL>"
    },
    "metadata": {}
  },
  "error_message": "dial tcp jumpserver_ip:8080: connect: connection refused",
  "failed_at": 1703123470,
  "retry_count": 3,
  "failure_reason": "NETWORK_ERROR",
  "stream_id": "1703123456789-0"
}
```

#### 5. 恢复服务并重新处理
```bash
# 恢复JumpServer连接
sudo iptables -D OUTPUT -d jumpserver_ip -j DROP

# 获取死信消息ID
MESSAGE_ID=$(curl -s http://localhost:8002/api/admin/dead-letter/messages | jq -r '.data.messages[0].stream_id')

# 重新处理消息
curl -X POST http://localhost:8002/api/admin/dead-letter/messages/$MESSAGE_ID/reprocess

# 响应
{
  "success": true,
  "message": "Message reprocessed successfully",
  "data": {
    "message_id": "1703123470123-0"
  }
}
```

#### 6. 重新处理的消息格式
```json
{
  "id": "user-create-123",
  "type": "user.create",
  "source": "rdb",
  "timestamp": 1703123456,
  "data.user_id": "123",
  "data.username": "john.doe",
  "data.email": "<EMAIL>",
  "metadata.reprocessed": "true",
  "metadata.original_failed_at": "1703123470",
  "metadata.original_retry_count": "3",
  "metadata.original_stream_id": "1703123456789-0"
}
```

#### 7. 成功处理日志
```
[INFO] Processing event: stream_id=1703123480456-0, event_id=user-create-123, event_type=user.create
[INFO] Event metadata contains reprocessed=true, this is a reprocessed message
[INFO] Creating user in JumpServer: john.doe
[INFO] User created successfully in JumpServer: john.doe (id: js-user-456)
[INFO] Event processed successfully: stream_id=1703123480456-0, event_id=user-create-123, event_type=user.create
```

### 示例2：认证错误（不可重试）

#### 1. 认证错误事件
```
[INFO] Processing event: stream_id=1703123500789-0, event_id=user-create-456, event_type=user.create
[WARN] Event user-create-456 failed on attempt 0: HTTP 401 Unauthorized: Invalid token
[INFO] Error classified as: AUTH_ERROR (non-retryable)
[ERROR] Non-retryable error for event user-create-456: HTTP 401 Unauthorized: Invalid token
[ERROR] Event user-create-456 failed after 0 attempts, sending to dead letter queue: Invalid token
[INFO] Event user-create-456 sent to dead letter queue: 1703123500890-0
```

#### 2. 死信队列中的认证错误消息
```json
{
  "original_event": { ... },
  "error_message": "HTTP 401 Unauthorized: Invalid token",
  "failed_at": 1703123500,
  "retry_count": 0,
  "failure_reason": "AUTH_ERROR",
  "stream_id": "1703123500789-0"
}
```

## 监控和运维

### 1. 死信队列统计
```bash
curl -s http://localhost:8002/api/admin/dead-letter/stats | jq .
```

```json
{
  "success": true,
  "data": {
    "total_count": 25,
    "error_type_stats": {
      "NETWORK_ERROR": 15,
      "SERVER_ERROR": 8,
      "AUTH_ERROR": 2
    },
    "event_type_stats": {
      "user.create": 10,
      "node.update": 8,
      "host.create": 7
    }
  }
}
```

### 2. 批量重新处理
```bash
curl -X POST http://localhost:8002/api/admin/dead-letter/messages/batch-reprocess \
  -H "Content-Type: application/json" \
  -d '{
    "message_ids": [
      "1703123456789-0",
      "1703123456790-0",
      "1703123456791-0"
    ]
  }'
```

### 3. Redis监控命令
```bash
# 查看主队列状态
redis-cli XINFO STREAM arboris:sync:events

# 查看死信队列状态
redis-cli XINFO STREAM arboris:sync:events:dead

# 查看消费者组状态
redis-cli XINFO GROUPS arboris:sync:events

# 查看未确认消息
redis-cli XPENDING arboris:sync:events jumpserver-sync
```

## 性能特性

### 1. 内存使用
- **原始消息确认**：重试失败进入死信队列后，原始消息被确认，不占用Redis PEL
- **死信队列容量**：可配置最大消息数和TTL，自动清理过期消息
- **批量处理**：支持批量重新处理，减少API调用次数

### 2. 可靠性保证
- **数据完整性**：死信队列保存完整的原始事件数据
- **幂等性**：重新处理的消息包含重处理标记，支持幂等处理
- **错误分类**：智能区分可重试和不可重试错误，避免无效重试

### 3. 可观测性
- **详细日志**：完整的重试过程日志
- **统计信息**：错误类型分布、事件类型统计
- **HTTP接口**：实时查看和管理死信队列

## 总结

JumpServer-Sync的重试机制和死信队列实现提供了：

1. **智能重试**：指数退避、错误分类、可配置参数
2. **数据保护**：失败消息完整保存，支持重新处理
3. **运维友好**：HTTP管理接口、批量操作、统计监控
4. **高可靠性**：消息不丢失、无重复消费、内存安全

这个实现确保了在各种故障场景下，JumpServer同步服务都能稳定运行并提供完善的故障恢复能力。
```
