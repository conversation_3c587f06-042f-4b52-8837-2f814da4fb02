package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// LoggerConfig 日志配置（与RDB模块保持一致）
type LoggerConfig struct {
	Dir       string `yaml:"dir"`
	Level     string `yaml:"level"`
	KeepHours uint   `yaml:"keepHours"`
}

// ConfigT 主配置结构（与RDB风格保持一致）
type ConfigT struct {
	Logger     LoggerConfig     `yaml:"logger"`
	HTTP       httpSection      `yaml:"http"`
	Redis      RedisConfig      `yaml:"redis"`
	JumpServer JumpServerConfig `yaml:"jumpserver"`
	Sync       SyncConfig       `yaml:"sync"`
	Monitor    MonitorConfig    `yaml:"monitor"`
}

type httpSection struct {
	Mode string `yaml:"mode"`
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enable    bool   `yaml:"enable"`
	Port      int    `yaml:"port"`
	Path      string `yaml:"path"`
	Namespace string `yaml:"namespace"`
	Subsystem string `yaml:"subsystem"`
}

// RedisConfig Redis 配置
type RedisConfig struct {
	Addr     string        `yaml:"addr"`
	Password string        `yaml:"password"`
	DB       int           `yaml:"db"`
	Timeout  TimeoutConfig `yaml:"timeout"`
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	Connect time.Duration `yaml:"connect"`
	Read    time.Duration `yaml:"read"`
	Write   time.Duration `yaml:"write"`
}

// JumpServerConfig JumpServer 配置
type JumpServerConfig struct {
	BaseURL      string        `yaml:"base_url"`
	Username     string        `yaml:"username"`
	Password     string        `yaml:"password"`
	Token        string        `yaml:"token"`
	Organization string        `yaml:"organization"`
	Timeout      time.Duration `yaml:"timeout"`
	Retry        RetryConfig   `yaml:"retry"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts     int           `yaml:"max_attempts"`
	InitialInterval time.Duration `yaml:"initial_interval"`
	MaxInterval     time.Duration `yaml:"max_interval"`
	Multiplier      float64       `yaml:"multiplier"`
	EnableDLQ       bool          `yaml:"enable_dlq"`
}

// ErrorHandlingConfig 错误处理配置
type ErrorHandlingConfig struct {
	RetryPatterns   []string `yaml:"retry_patterns"`   // 重试错误模式
	DLQPatterns     []string `yaml:"dlq_patterns"`     // 进入DLQ错误模式
	DiscardPatterns []string `yaml:"discard_patterns"` // 直接丢弃错误模式
}

// SyncConfig 同步配置
type SyncConfig struct {
	Consumer      ConsumerConfig      `yaml:"consumer"`
	Mapping       MappingConfig       `yaml:"mapping"`
	Rules         RulesConfig         `yaml:"rules"`
	Retry         RetryConfig         `yaml:"retry"`
	DeadLetter    DeadLetterConfig    `yaml:"dead_letter"`
	ErrorHandling ErrorHandlingConfig `yaml:"error_handling"`
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	Stream       string        `yaml:"stream"`
	Group        string        `yaml:"group"`
	Consumer     string        `yaml:"consumer"`
	BatchSize    int           `yaml:"batch_size"`
	PollInterval time.Duration `yaml:"poll_interval"`
}

// MappingConfig 映射配置
type MappingConfig struct {
	NodeRules    []NodeRule    `yaml:"node_rules"`
	MachineRules []MachineRule `yaml:"machine_rules"`
}

// NodeRule 节点映射规则
type NodeRule struct {
	Pattern      string `yaml:"pattern"`
	TargetPrefix string `yaml:"target_prefix"`
	Description  string `yaml:"description"`
}

// MachineRule 机器映射规则
type MachineRule struct {
	Condition   string            `yaml:"condition"`
	Platform    string            `yaml:"platform"`
	Protocols   []ProtocolConfig  `yaml:"protocols"`
	Labels      map[string]string `yaml:"labels"`
	Description string            `yaml:"description"`
}

// ProtocolConfig 协议配置
type ProtocolConfig struct {
	Name string `yaml:"name"`
	Port int    `yaml:"port"`
}

// RulesConfig 同步规则配置
type RulesConfig struct {
	IncludePaths []string          `yaml:"include_paths"`
	ExcludePaths []string          `yaml:"exclude_paths"`
	Filters      map[string]string `yaml:"filters"`
}

// DeadLetterConfig 死信队列配置
type DeadLetterConfig struct {
	Enabled     bool   `yaml:"enabled"`
	StreamName  string `yaml:"stream_name"`
	MaxMessages int64  `yaml:"max_messages"`
	TTL         int64  `yaml:"ttl"` // 消息保留时间（秒）
}

// 全局配置实例
var GlobalConfig *ConfigT

// Config 全局配置变量（与RDB风格保持一致）
var Config *ConfigT

// Parse 解析配置文件（与RDB风格保持一致）
func Parse() error {
	ymlFile := getYmlFile()
	if ymlFile == "" {
		return fmt.Errorf("configuration file not found")
	}

	var c ConfigT
	data, err := os.ReadFile(ymlFile)
	if err != nil {
		return fmt.Errorf("cannot read yml[%s]: %v", ymlFile, err)
	}

	if err := yaml.Unmarshal(data, &c); err != nil {
		return fmt.Errorf("cannot parse yml[%s]: %v", ymlFile, err)
	}

	Config = &c
	fmt.Println("config.file:", ymlFile)

	// 设置默认值
	Config.SetDefaults()

	// 验证配置
	if err := Config.Validate(); err != nil {
		return fmt.Errorf("config validation failed: %v", err)
	}

	// 设置全局配置
	GlobalConfig = Config

	return nil
}

// getYmlFile 获取配置文件路径（与RDB风格保持一致）
func getYmlFile() string {
	yml := "etc/jumpserver-sync.local.yml"
	if _, err := os.Stat(yml); err == nil {
		return yml
	}

	yml = "etc/jumpserver-sync.yml"
	if _, err := os.Stat(yml); err == nil {
		return yml
	}

	return ""
}

// Validate 验证配置
func (c *ConfigT) Validate() error {
	// MySQL配置现在通过mysql.yml文件读取，无需验证

	if c.Redis.Addr == "" {
		return fmt.Errorf("redis.addr is required")
	}

	if c.JumpServer.BaseURL == "" {
		return fmt.Errorf("jumpserver.base_url is required")
	}

	if c.JumpServer.Username == "" && c.JumpServer.Token == "" {
		return fmt.Errorf("jumpserver.username or jumpserver.token is required")
	}

	if c.JumpServer.Username != "" && c.JumpServer.Password == "" {
		return fmt.Errorf("jumpserver.password is required when username is provided")
	}

	if c.Sync.Consumer.Stream == "" {
		return fmt.Errorf("sync.consumer.stream is required")
	}

	if c.Sync.Consumer.Group == "" {
		return fmt.Errorf("sync.consumer.group is required")
	}

	if c.Sync.Consumer.Consumer == "" {
		return fmt.Errorf("sync.consumer.consumer is required")
	}

	return nil
}

// SetDefaults 设置默认值
func (c *ConfigT) SetDefaults() {

	// MySQL配置现在通过mysql.yml文件读取，无需设置默认值

	// Redis默认值
	if c.Redis.DB == 0 {
		c.Redis.DB = 0
	}
	if c.Redis.Timeout.Connect == 0 {
		c.Redis.Timeout.Connect = 5 * time.Second
	}
	if c.Redis.Timeout.Read == 0 {
		c.Redis.Timeout.Read = 3 * time.Second
	}
	if c.Redis.Timeout.Write == 0 {
		c.Redis.Timeout.Write = 3 * time.Second
	}

	// JumpServer默认值
	if c.JumpServer.Organization == "" {
		c.JumpServer.Organization = "Default"
	}
	if c.JumpServer.Timeout == 0 {
		c.JumpServer.Timeout = 30 * time.Second
	}
	if c.JumpServer.Retry.MaxAttempts == 0 {
		c.JumpServer.Retry.MaxAttempts = 3
	}
	if c.JumpServer.Retry.InitialInterval == 0 {
		c.JumpServer.Retry.InitialInterval = 1 * time.Second
	}
	if c.JumpServer.Retry.MaxInterval == 0 {
		c.JumpServer.Retry.MaxInterval = 30 * time.Second
	}
	if c.JumpServer.Retry.Multiplier == 0 {
		c.JumpServer.Retry.Multiplier = 2.0
	}

	// 同步默认值
	if c.Sync.Consumer.BatchSize == 0 {
		c.Sync.Consumer.BatchSize = 10
	}
	if c.Sync.Consumer.PollInterval == 0 {
		c.Sync.Consumer.PollInterval = 5 * time.Second
	}

	// 重试默认值
	if c.Sync.Retry.MaxAttempts == 0 {
		c.Sync.Retry.MaxAttempts = 3
	}
	if c.Sync.Retry.InitialInterval == 0 {
		c.Sync.Retry.InitialInterval = 1 * time.Second
	}
	if c.Sync.Retry.MaxInterval == 0 {
		c.Sync.Retry.MaxInterval = 30 * time.Second
	}
	if c.Sync.Retry.Multiplier == 0 {
		c.Sync.Retry.Multiplier = 2.0
	}
	// EnableDLQ 默认为true

	// 死信队列默认值
	if c.Sync.DeadLetter.StreamName == "" {
		c.Sync.DeadLetter.StreamName = c.Sync.Consumer.Stream + ":dead"
	}
	if c.Sync.DeadLetter.MaxMessages == 0 {
		c.Sync.DeadLetter.MaxMessages = 10000 // 默认最多保留10000条死信消息
	}
	if c.Sync.DeadLetter.TTL == 0 {
		c.Sync.DeadLetter.TTL = 7 * 24 * 3600 // 默认保留7天
	}
}
