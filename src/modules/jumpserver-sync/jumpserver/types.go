package jumpserver

// Node JumpServer节点结构
type Node struct {
	ID       string            `json:"id"`
	Key      string            `json:"key"`
	Value    string            `json:"value"`
	Parent   string            `json:"parent"`
	Meta     map[string]string `json:"meta"`
	FullPath string            `json:"full_path"`
}

// Asset JumpServer资产结构（简化版，只保留必要字段）
type Asset struct {
	ID        string      `json:"id"`
	Hostname  string      `json:"hostname"`
	IP        string      `json:"ip"`
	Address   string      `json:"address"`  // 资产地址
	Name      string      `json:"name"`     // 资产名称
	Platform  interface{} `json:"platform"` // 平台信息（可能是字符串或对象）
	Protocols []Protocol  `json:"protocols"`
	IsActive  bool        `json:"is_active"`
	Comment   string      `json:"comment"`
	Labels    interface{} `json:"labels"` // 标签（可能是map或数组）
	Nodes     interface{} `json:"nodes"`  // 节点（可能是字符串数组或对象数组）
	// 其他字段使用interface{}忽略，只要能解析到ID即可
	Category     interface{} `json:"category,omitempty"`
	Type         interface{} `json:"type,omitempty"`
	Connectivity interface{} `json:"connectivity,omitempty"`
	OrgId        string      `json:"org_id,omitempty"`
	OrgName      string      `json:"org_name,omitempty"`
	DateCreated  string      `json:"date_created,omitempty"`
	DateUpdated  string      `json:"date_updated,omitempty"`
	CreatedBy    string      `json:"created_by,omitempty"`
}

// Protocol 协议配置
type Protocol struct {
	Name string `json:"name"`
	Port int    `json:"port"`
}

// AuthInfo 认证信息
type AuthInfo struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Token    string `json:"token"`
}

// APIResponse JumpServer API响应
type APIResponse struct {
	Count    int         `json:"count"`
	Next     string      `json:"next"`
	Previous string      `json:"previous"`
	Results  interface{} `json:"results"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Detail string            `json:"detail"`
	Errors map[string]string `json:"errors"`
}

// SourceInfo JumpServer source字段结构
type SourceInfo struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// RoleInfo JumpServer角色信息结构
type RoleInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
}

// User JumpServer用户结构
type User struct {
	ID          string        `json:"id"`
	Username    string        `json:"username"`
	Name        string        `json:"name"`
	Email       string        `json:"email"`
	Phone       interface{}   `json:"phone"` // 可能是null或字符串
	Password    string        `json:"password,omitempty"`
	IsActive    bool          `json:"is_active"`
	IsSuperuser bool          `json:"is_superuser"`
	DateJoined  string        `json:"date_joined"`
	LastLogin   interface{}   `json:"last_login"`   // 可能是null或字符串
	DateExpired interface{}   `json:"date_expired"` // 用户失效时间，可能是null或字符串
	Comment     interface{}   `json:"comment"`      // 可能是null或字符串
	Source      interface{}   `json:"source"`       // 可能是字符串或对象
	Groups      []interface{} `json:"groups"`       // 可能是字符串数组或对象数组
	UserGroups  []interface{} `json:"user_groups"`
	SystemRoles []interface{} `json:"system_roles"` // 角色对象数组
	OrgRoles    []interface{} `json:"org_roles"`    // 角色对象数组
}

// UserGroup JumpServer用户组结构
type UserGroup struct {
	ID          string        `json:"id,omitempty"`
	Name        string        `json:"name"`
	Comment     string        `json:"comment"`
	Users       []interface{} `json:"users"`  // 支持字符串数组或对象数组，不能为null
	Labels      []interface{} `json:"labels"` // 支持字符串数组或对象数组，不能为null
	OrgID       string        `json:"org_id,omitempty"`
	OrgName     string        `json:"org_name,omitempty"`
	DateCreated interface{}   `json:"date_created,omitempty"` // 支持多种时间格式
	CreatedBy   string        `json:"created_by,omitempty"`
	UsersAmount int           `json:"users_amount,omitempty"` // 用户数量字段
}

// GetUsernames 获取用户组中的用户名列表
func (ug *UserGroup) GetUsernames() []string {
	// 初始化为空数组，确保不返回nil
	usernames := make([]string, 0)

	for _, user := range ug.Users {
		switch u := user.(type) {
		case string:
			// 如果是字符串，直接添加
			usernames = append(usernames, u)
		case map[string]interface{}:
			// 如果是对象，提取username字段
			if username, ok := u["username"].(string); ok {
				usernames = append(usernames, username)
			}
		}
	}

	return usernames
}

// SetUsernames 设置用户组的用户名列表（用于创建/更新请求）
func (ug *UserGroup) SetUsernames(usernames []string) {
	ug.Users = make([]interface{}, len(usernames))
	for i, username := range usernames {
		ug.Users[i] = username
	}
}

// User2GroupRelation 用户组关系结构
type User2GroupRelation struct {
	ID               int    `json:"id"`
	User             string `json:"user"`
	UserDisplay      string `json:"user_display"`
	UserGroup        string `json:"usergroup"`
	UserGroupDisplay string `json:"usergroup_display"`
}

// AssetPermission JumpServer资产权限结构
type AssetPermission struct {
	ID          string                   `json:"id"`
	Name        string                   `json:"name"`
	Users       []map[string]string      `json:"users"`       // [{"pk": "user_id"}]
	UserGroups  []map[string]string      `json:"user_groups"` // [{"pk": "group_id"}]
	Assets      []map[string]string      `json:"assets"`      // [{"pk": "asset_id"}]
	Nodes       []map[string]string      `json:"nodes"`       // [{"pk": "node_id"}]
	Accounts    []string                 `json:"accounts"`
	Protocols   []string                 `json:"protocols"`
	Actions     []map[string]interface{} `json:"actions"`
	IsActive    bool                     `json:"is_active"`
	DateStart   string                   `json:"date_start"`
	DateExpired string                   `json:"date_expired"`
	Comment     string                   `json:"comment"`
}

// UserAsset 用户资产结构（用于权限查询）
type UserAsset struct {
	ID       string `json:"id"`
	Hostname string `json:"hostname"`
	IP       string `json:"ip"`
	Address  string `json:"address"`
	Name     string `json:"name"`
	Platform string `json:"platform"`
	Comment  string `json:"comment"`
}

// SecretTypeChoice JumpServer密文类型选择结构
type SecretTypeChoice struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// PasswordRules 密码规则结构
type PasswordRules struct {
	Length         int    `json:"length"`
	Lowercase      bool   `json:"lowercase"`
	Uppercase      bool   `json:"uppercase"`
	Digit          bool   `json:"digit"`
	Symbol         bool   `json:"symbol"`
	ExcludeSymbols string `json:"exclude_symbols"`
}

// AccountTemplate 账号模板结构
type AccountTemplate struct {
	ID             string        `json:"id,omitempty"` // 创建时忽略，JumpServer自动生成UUID
	Name           string        `json:"name"`
	Username       string        `json:"username"`
	SecretType     interface{}   `json:"secret_type"` // 可能是字符串或对象
	Secret         string        `json:"secret,omitempty"`
	Passphrase     string        `json:"passphrase,omitempty"`
	SecretStrategy interface{}   `json:"secret_strategy"`          // 可能是字符串或对象
	PasswordRules  interface{}   `json:"password_rules,omitempty"` // 密码规则对象
	PushParams     interface{}   `json:"push_params,omitempty"`    // 推送参数对象
	Platforms      []interface{} `json:"platforms,omitempty"`      // 平台数组
	Labels         []interface{} `json:"labels,omitempty"`         // 标签数组
	SpecInfo       interface{}   `json:"spec_info,omitempty"`      // 规格信息对象
	SuFrom         interface{}   `json:"su_from,omitempty"`        // 可能是null或字符串
	OrgId          string        `json:"org_id,omitempty"`         // 组织ID
	OrgName        string        `json:"org_name,omitempty"`       // 组织名称
	Privileged     bool          `json:"privileged"`
	IsActive       bool          `json:"is_active"`
	AutoPush       bool          `json:"auto_push"`
	Comment        string        `json:"comment"`
	DateCreated    string        `json:"date_created,omitempty"`
	DateUpdated    string        `json:"date_updated,omitempty"`
	CreatedBy      string        `json:"created_by,omitempty"` // 创建者
}

// GetSecretType 获取密文类型字符串值
func (t *AccountTemplate) GetSecretType() string {
	switch v := t.SecretType.(type) {
	case string:
		return v
	case map[string]interface{}:
		if value, ok := v["value"].(string); ok {
			return value
		}
		if label, ok := v["label"].(string); ok {
			return label
		}
	case SecretTypeChoice:
		return v.Value
	default:
		return "password" // 默认值
	}
	return "password"
}

// SetSecretType 设置密文类型（发送给JumpServer时使用字符串）
func (t *AccountTemplate) SetSecretType(secretType string) {
	t.SecretType = secretType
}

// GetSecretStrategy 获取密文策略字符串值
func (t *AccountTemplate) GetSecretStrategy() string {
	switch v := t.SecretStrategy.(type) {
	case string:
		return v
	case map[string]interface{}:
		if value, ok := v["value"].(string); ok {
			return value
		}
		if label, ok := v["label"].(string); ok {
			return label
		}
	default:
		return "specific" // 默认值
	}
	return "specific"
}

// SetSecretStrategy 设置密文策略（发送给JumpServer时使用字符串）
func (t *AccountTemplate) SetSecretStrategy(secretStrategy string) {
	t.SecretStrategy = secretStrategy
}

// Account 账号结构
type Account struct {
	ID          string      `json:"id,omitempty"` // 创建时忽略，JumpServer自动生成UUID
	Name        string      `json:"name"`
	Username    string      `json:"username"`
	SecretType  interface{} `json:"secret_type"` // 可能是字符串或对象
	Secret      string      `json:"secret,omitempty"`
	Passphrase  string      `json:"passphrase,omitempty"`
	Template    string      `json:"template,omitempty"`
	Asset       interface{} `json:"asset"`  // 可能是字符串（创建时）或对象（查询时）
	Source      interface{} `json:"source"` // 可能是字符串或对象
	Privileged  bool        `json:"privileged"`
	IsActive    bool        `json:"is_active"`
	SecretReset bool        `json:"secret_reset"`
	PushNow     bool        `json:"push_now"`
	Comment     string      `json:"comment"`
	DateCreated string      `json:"date_created,omitempty"`
	DateUpdated string      `json:"date_updated,omitempty"`
}

// GetSecretType 获取密文类型字符串值
func (a *Account) GetSecretType() string {
	switch v := a.SecretType.(type) {
	case string:
		return v
	case map[string]interface{}:
		if value, ok := v["value"].(string); ok {
			return value
		}
		if label, ok := v["label"].(string); ok {
			return label
		}
	case SecretTypeChoice:
		return v.Value
	default:
		return "password" // 默认值
	}
	return "password"
}

// SetSecretType 设置密文类型（发送给JumpServer时使用字符串）
func (a *Account) SetSecretType(secretType string) {
	a.SecretType = secretType
}

// GetAssetID 获取资产ID
func (a *Account) GetAssetID() string {
	switch v := a.Asset.(type) {
	case string:
		return v
	case map[string]interface{}:
		if id, ok := v["id"].(string); ok {
			return id
		}
	}
	return ""
}

// SetAssetID 设置资产ID（发送给JumpServer时使用字符串）
func (a *Account) SetAssetID(assetID string) {
	a.Asset = assetID
}

// GetSourceValue 获取来源值
func (a *Account) GetSourceValue() string {
	switch v := a.Source.(type) {
	case string:
		return v
	case map[string]interface{}:
		if value, ok := v["value"].(string); ok {
			return value
		}
		if label, ok := v["label"].(string); ok {
			return label
		}
	}
	return ""
}

// SetSource 设置来源（发送给JumpServer时使用字符串）
func (a *Account) SetSource(source string) {
	a.Source = source
}

// PhoneInfo 电话信息结构
type PhoneInfo struct {
	Code  string `json:"code"`
	Phone int64  `json:"phone"`
}

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Username string     `json:"username"`
	Name     string     `json:"name"`
	Email    string     `json:"email"`
	Phone    *PhoneInfo `json:"phone"`
	IsActive bool       `json:"is_active"`
	Password string     `json:"password,omitempty"`
}

// UserCreateResponse 用户创建响应
type UserCreateResponse struct {
	User     *User  `json:"user"`
	Password string `json:"password"`
}
