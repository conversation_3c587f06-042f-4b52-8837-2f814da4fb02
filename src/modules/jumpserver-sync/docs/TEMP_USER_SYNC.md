# JumpServer临时用户同步逻辑

## 概述

RDB模块支持两种用户类型：永久用户和临时用户。本文档描述了这两种用户类型在JumpServer中的同步逻辑。

## 用户类型定义

### RDB中的用户类型

```go
const (
    USER_T_NATIVE = 0  // 永久账号 (permanent account)
    USER_T_TEMP   = 1  // 临时账号 (temporary account)
)
```

### 用户状态

```go
const (
    USER_S_ACTIVE     = 0  // 活跃
    USER_S_INACTIVE   = 1  // 非活跃
    USER_S_LOCKED     = 2  // 锁定
    USER_S_FROZEN     = 3  // 冻结
    USER_S_WRITEN_OFF = 4  // 注销
)
```

## 临时用户字段

- `ActiveBegin`: 临时用户生效时间（Unix时间戳）
- `ActiveEnd`: 临时用户失效时间（Unix时间戳，0表示永不过期）

## 同步逻辑

### 1. 用户创建同步

#### 永久用户
- 在JumpServer中创建用户，不设置失效时间
- Comment标记为"Permanent user - Org: {组织名}"

#### 临时用户（有效期内）
- 检查当前时间是否在有效期内
- 如果有效，在JumpServer中创建用户并设置`date_expired`字段
- Comment标记为"Temporary user (expires: {过期时间}) - Org: {组织名}"

#### 临时用户（已过期）
- 跳过创建，记录日志
- 不在JumpServer中创建用户

### 2. 用户更新同步

#### 永久用户
- 更新用户信息
- 清除`date_expired`字段（如果之前是临时用户）
- 更新Comment为"Permanent user - Org: {组织名}"

#### 临时用户（有效期内）
- 更新用户信息
- 设置或更新`date_expired`字段
- 更新Comment为"Temporary user (expires: {过期时间}) - Org: {组织名}"

#### 临时用户（已过期）
- 自动将用户状态设为非活跃（Status = 1）
- 在JumpServer中禁用用户（IsActive = false）
- 更新Comment为"Temporary user expired at {过期时间} - Org: {组织名}"

### 3. 过期用户自动检查

系统每小时自动检查一次过期的临时用户：

1. 查询数据库中所有过期的临时用户（`type=1 AND active_end>0 AND active_end<当前时间`）
2. 在RDB中将过期用户状态设为非活跃
3. 在JumpServer中禁用过期用户
4. 记录操作日志

## JumpServer用户结构扩展

```go
type User struct {
    // ... 其他字段
    DateExpired interface{} `json:"date_expired"` // 用户失效时间，ISO 8601格式
    // ... 其他字段
}
```

## 时间格式转换

RDB使用Unix时间戳，JumpServer使用ISO 8601格式：

```go
// Unix时间戳转ISO 8601
expiredTime := time.Unix(activeEnd, 0).UTC().Format("2006-01-02T15:04:05Z")
```

## 事件流程

### 用户创建流程
```
RDB用户创建 → 发布UserCreateEvent → JumpServer同步处理器 → 检查用户类型和有效期 → 创建JumpServer用户
```

### 用户更新流程
```
RDB用户更新 → 发布UserUpdateEvent → JumpServer同步处理器 → 检查用户类型和有效期 → 更新JumpServer用户
```

### 过期检查流程
```
定时器触发 → 查询过期临时用户 → 禁用RDB用户 → 禁用JumpServer用户 → 记录日志
```

## 配置说明

### 过期检查间隔
默认每小时检查一次，可以通过修改代码调整：

```go
ticker := time.NewTicker(1 * time.Hour) // 修改这里的时间间隔
```

## 日志记录

系统会记录以下关键操作：

1. 临时用户创建（包含有效期信息）
2. 临时用户过期检查
3. 过期用户自动禁用
4. 用户类型转换（临时→永久，永久→临时）

## 测试

使用测试脚本验证同步逻辑：

```bash
# 运行临时用户同步测试
go run src/modules/jumpserver-sync/test/temp_user_sync_test.go
```

## 注意事项

1. **时区处理**: 所有时间计算都使用UTC时间
2. **过期检查**: 过期检查器在JumpServer同步服务启动时自动启动
3. **错误处理**: 同步失败不会影响RDB的正常操作，只会记录错误日志
4. **性能考虑**: 过期检查使用批量查询，避免对数据库造成压力

## 故障排除

### 常见问题

1. **临时用户未在JumpServer中过期**
   - 检查过期检查器是否正常运行
   - 查看JumpServer同步服务日志

2. **时间不一致**
   - 确保RDB和JumpServer服务器时间同步
   - 检查时区设置

3. **同步失败**
   - 检查JumpServer连接配置
   - 查看详细错误日志
