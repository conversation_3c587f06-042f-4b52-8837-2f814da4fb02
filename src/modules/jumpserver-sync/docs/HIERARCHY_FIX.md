# 多层级节点创建问题修复文档

## 🐛 问题描述

您发现了一个关键问题：**当前的逻辑导致只能创建2个层级的节点**，无法正确创建深层级的节点树。

### 问题根本原因

在原有的 `handleNodeCreate` 方法中，父节点查找使用了**全局搜索**而不是**层级搜索**：

```go
// 问题代码
parentJSNode, err := h.jsClient.GetNodeByValue(parentNodeData.Name)
```

这个方法会在整个 JumpServer 中搜索名为 `parentNodeData.Name` 的节点，**不考虑层级关系**。

### 问题场景分析

假设要创建路径：`/公司/技术部/后端组/核心团队`

1. **创建 "公司"** - ✅ 成功（根节点）
2. **创建 "技术部"** - ✅ 成功（父节点是"公司"）  
3. **创建 "后端组"** - ❌ **问题出现**
   - 查找父节点 "技术部" 时，使用 `GetNodeByValue("技术部")`
   - 如果系统中有多个 "技术部"（比如在不同公司下），会返回第一个找到的
   - 可能返回错误的 "技术部" 节点，导致 "后端组" 被创建在错误的父节点下

4. **创建 "核心团队"** - ❌ **更严重的问题**
   - 查找父节点 "后端组" 时，如果上一步创建错了位置，这里就找不到正确的父节点
   - 或者找到了错误位置的 "后端组"

## ✅ 修复方案

### 1. 实现递归层级查找

新增 `ensureParentNodeExists` 方法，使用**递归层级查找**而不是全局查找：

```go
func (h *Handler) ensureParentNodeExists(nodeData *NodeInfo) (string, error) {
    // 如果是根节点，直接查找
    if nodeData.PID == 0 {
        existingNode, err := h.jsClient.GetNodeByParentAndValue("", nodeData.Name)
        if err != nil {
            return "", fmt.Errorf("failed to find root node: %v", err)
        }
        
        if existingNode != nil {
            return existingNode.ID, nil
        }
        
        // 根节点不存在，创建它
        return h.createNodeFromNodeData(nodeData, "")
    }

    // 非根节点，需要先确保其父节点存在
    grandParentData, err := h.getNodeByID(nodeData.PID)
    if err != nil {
        return "", fmt.Errorf("failed to get grandparent node: %v", err)
    }

    // 递归确保祖父节点存在
    grandParentID, err := h.ensureParentNodeExists(grandParentData)
    if err != nil {
        return "", fmt.Errorf("failed to ensure grandparent exists: %v", err)
    }

    // 在祖父节点下查找当前节点
    existingNode, err := h.jsClient.GetNodeByParentAndValue(grandParentID, nodeData.Name)
    if err != nil {
        return "", fmt.Errorf("failed to find node under parent: %v", err)
    }

    if existingNode != nil {
        return existingNode.ID, nil
    }

    // 节点不存在，创建它
    return h.createNodeFromNodeData(nodeData, grandParentID)
}
```

### 2. 新增 NodeInfo 映射方法

在 mapper 中新增 `MapNodeInfoToJumpServer` 方法，支持将 `NodeInfo` 类型映射为 JumpServer 节点：

```go
func (m *Mapper) MapNodeInfoToJumpServer(nodeData *NodeInfo) (*jumpserver.Node, error) {
    // 应用节点映射规则
    key := m.applyNodeRules(nodeData.Path)

    // 构建JumpServer节点
    jsNode := &jumpserver.Node{
        Key:      key,
        Value:    nodeData.Name,
        Parent:   m.getParentKey(key),
        FullPath: key,
        Meta: map[string]string{
            "rdb_id":   fmt.Sprintf("%d", nodeData.ID),
            "rdb_path": nodeData.Path,
            "cate":     nodeData.Cate,
            "note":     nodeData.Note,
        },
    }

    return jsNode, nil
}
```

### 3. 修改节点创建逻辑

```go
func (h *Handler) handleNodeCreate(event *events.Event) error {
    // ... 前置检查 ...

    if nodeData.GetNodePID() != 0 {
        // 获取父节点信息
        parentNodeData, err := h.getNodeByID(nodeData.GetNodePID())
        if err != nil {
            return fmt.Errorf("failed to get parent node: %v", err)
        }

        logger.Infof("Creating node: current_node=%s (path=%s), parent_node=%s (path=%s)", 
            nodeData.Name, nodeData.Path, parentNodeData.Name, parentNodeData.Path)

        // 递归确保父节点存在 - 使用层级查找而不是全局查找
        parentID, err = h.ensureParentNodeExists(parentNodeData)
        if err != nil {
            return fmt.Errorf("failed to ensure parent node exists: %v", err)
        }

        logger.Infof("Parent node resolved: parent_id=%s, parent_name=%s", parentID, parentNodeData.Name)
    }

    // ... 后续创建逻辑 ...
}
```

## 🔍 修复逻辑详解

### 递归查找过程

以创建 `/公司/技术部/后端组/核心团队` 为例：

1. **创建 "核心团队"**
   - 调用 `ensureParentNodeExists(后端组)`
   
2. **确保 "后端组" 存在**
   - `后端组.PID != 0`，需要先确保其父节点存在
   - 调用 `ensureParentNodeExists(技术部)`
   
3. **确保 "技术部" 存在**
   - `技术部.PID != 0`，需要先确保其父节点存在
   - 调用 `ensureParentNodeExists(公司)`
   
4. **确保 "公司" 存在**
   - `公司.PID == 0`，这是根节点
   - 使用 `GetNodeByParentAndValue("", "公司")` 查找
   - 如果不存在，创建根节点 "公司"
   - 返回 "公司" 的 ID
   
5. **回到 "技术部"**
   - 使用 `GetNodeByParentAndValue(公司ID, "技术部")` 查找
   - 如果不存在，在 "公司" 下创建 "技术部"
   - 返回 "技术部" 的 ID
   
6. **回到 "后端组"**
   - 使用 `GetNodeByParentAndValue(技术部ID, "后端组")` 查找
   - 如果不存在，在 "技术部" 下创建 "后端组"
   - 返回 "后端组" 的 ID
   
7. **最终创建 "核心团队"**
   - 在 "后端组" 下创建 "核心团队"

### 关键改进

1. **层级查找**：每次查找都指定了正确的父节点ID，避免了全局搜索的问题
2. **递归创建**：自动确保整个父节点链都存在
3. **精确定位**：通过 `parentID + nodeName` 的组合精确定位节点

## 🧪 测试验证

创建了 `hierarchy_test.go` 测试脚本，验证以下场景：

1. **5层级节点树**：`/测试公司/技术部/后端组/核心团队/开发小组`
2. **7层级节点树**：`/集团/子公司/事业部/技术中心/研发部/后端组/微服务团队`
3. **分支插入**：在现有层级中插入新分支
4. **同名不同父**：在不同父节点下创建同名子节点
5. **层级查找**：验证节点查找功能的正确性

## 📁 修改的文件

1. **src/modules/jumpserver-sync/sync/handler.go**
   - 修改 `handleNodeCreate` 方法
   - 新增 `ensureParentNodeExists` 方法
   - 新增 `createNodeFromNodeData` 方法

2. **src/modules/jumpserver-sync/mapper/mapper.go**
   - 新增 `NodeInfo` 类型定义
   - 新增 `MapNodeInfoToJumpServer` 方法

3. **src/modules/jumpserver-sync/test/hierarchy_test.go** (新增)
   - 多层级节点创建测试脚本

## 🚀 运行测试

```bash
# 运行层级测试脚本（需要配置JumpServer连接信息）
cd src/modules/jumpserver-sync
go run test/hierarchy_test.go
```

## 📊 修复效果

✅ **支持任意深度的层级节点树**
✅ **正确的父子关系建立**
✅ **避免了全局搜索导致的错误关联**
✅ **支持在不同父节点下创建同名子节点**
✅ **递归创建确保完整的节点链**
✅ **详细的日志输出便于调试**

现在您可以创建任意深度的节点层级了！🎉
