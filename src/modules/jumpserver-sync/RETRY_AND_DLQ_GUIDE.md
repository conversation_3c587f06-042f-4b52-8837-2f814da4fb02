# JumpServer-Sync 重试机制和死信队列使用指南

## 概述

JumpServer-Sync 模块现已集成智能重试机制和死信队列功能，提供了强大的错误处理和故障恢复能力。

## 功能特性

### 🔄 智能重试机制
- **指数退避策略**：1s → 2s → 4s → 8s...
- **错误分类**：自动识别可重试和不可重试错误
- **可配置参数**：最大重试次数、延迟时间、退避倍数

### 💀 死信队列
- **失败消息保存**：重试失败的消息自动进入死信队列
- **手动重处理**：支持单个或批量重新处理
- **统计分析**：提供详细的失败原因分析

### 🌐 管理接口
- **RESTful API**：完整的死信队列管理接口
- **Web界面友好**：JSON格式响应，易于集成
- **实时监控**：支持查看系统状态和统计信息

## 配置说明

### 配置文件示例

```yaml
# jumpserver-sync.yml
sync:
  # 重试配置
  retry:
    max_attempts: 3          # 最大重试次数
    initial_interval: 1s     # 初始延迟时间
    max_interval: 30s        # 最大延迟时间
    multiplier: 2.0          # 退避倍数
    enable_dlq: true         # 启用死信队列

  # 死信队列配置
  dead_letter:
    enabled: true                           # 启用死信队列
    stream_name: "arboris:sync:events:dead" # 死信队列名称
    max_messages: 10000                     # 最大消息数
    ttl: 604800                            # 消息保留时间(秒)
```

### 配置参数说明

| 参数 | 说明 | 默认值 | 建议值 |
|------|------|--------|--------|
| `max_attempts` | 最大重试次数 | 3 | 3-5 |
| `initial_interval` | 初始延迟 | 1s | 1s-5s |
| `max_interval` | 最大延迟 | 30s | 30s-300s |
| `multiplier` | 退避倍数 | 2.0 | 1.5-3.0 |
| `max_messages` | 死信队列容量 | 10000 | 5000-50000 |
| `ttl` | 消息保留时间 | 7天 | 3-30天 |

## 错误分类

系统自动将错误分为以下类型：

### 🔄 可重试错误
- **网络错误**：连接超时、网络中断
- **服务器错误**：5xx HTTP状态码
- **超时错误**：请求超时、上下文超时

### ❌ 不可重试错误
- **认证错误**：401、403 HTTP状态码
- **验证错误**：400 HTTP状态码、数据格式错误
- **资源不存在**：404 HTTP状态码

## API 接口

### 基础URL
```
http://localhost:8002/api/admin
```

### 死信队列管理

#### 1. 查看死信消息列表
```bash
GET /api/admin/dead-letter/messages?limit=50&offset=0
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "original_event": {
          "id": "event-123",
          "type": "user.create",
          "data": {"user_id": 456}
        },
        "error_message": "JumpServer API error: 500 Internal Server Error",
        "failed_at": 1703123456,
        "retry_count": 3,
        "failure_reason": "SERVER_ERROR",
        "stream_id": "1703123456789-0"
      }
    ],
    "count": 1,
    "limit": 50,
    "offset": 0
  }
}
```

#### 2. 获取单个死信消息
```bash
GET /api/admin/dead-letter/messages/{message_id}
```

#### 3. 重新处理死信消息
```bash
POST /api/admin/dead-letter/messages/{message_id}/reprocess
```

#### 4. 批量重新处理
```bash
POST /api/admin/dead-letter/messages/batch-reprocess
Content-Type: application/json

{
  "message_ids": ["1703123456789-0", "1703123456790-0"]
}
```

#### 5. 删除死信消息
```bash
DELETE /api/admin/dead-letter/messages/{message_id}
```

#### 6. 获取统计信息
```bash
GET /api/admin/dead-letter/stats
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "total_count": 25,
    "error_type_stats": {
      "SERVER_ERROR": 15,
      "NETWORK_ERROR": 8,
      "TIMEOUT_ERROR": 2
    },
    "event_type_stats": {
      "user.create": 10,
      "node.update": 8,
      "host.create": 7
    }
  }
}
```

### 系统状态

#### 健康检查
```bash
GET /api/admin/health
```

#### 系统状态
```bash
GET /api/admin/status
```

## 运维指南

### 日常监控

#### 1. 检查死信队列状态
```bash
curl -s http://localhost:8002/api/admin/dead-letter/stats | jq .
```

#### 2. 监控失败率
建议设置告警：
- 死信队列消息数 > 100
- 特定错误类型占比 > 50%
- 系统整体失败率 > 10%

### 故障处理

#### 1. 网络问题导致的失败
```bash
# 查看网络相关错误
curl -s "http://localhost:8002/api/admin/dead-letter/messages?limit=100" | \
  jq '.data.messages[] | select(.failure_reason == "NETWORK_ERROR")'

# 网络恢复后批量重处理
curl -X POST http://localhost:8002/api/admin/dead-letter/messages/batch-reprocess \
  -H "Content-Type: application/json" \
  -d '{"message_ids": ["id1", "id2", "id3"]}'
```

#### 2. JumpServer服务异常
```bash
# 查看服务器错误
curl -s "http://localhost:8002/api/admin/dead-letter/messages?limit=100" | \
  jq '.data.messages[] | select(.failure_reason == "SERVER_ERROR")'

# 服务恢复后重处理
# 建议分批处理，避免瞬间压力过大
```

#### 3. 认证问题
```bash
# 查看认证错误（通常需要更新配置）
curl -s "http://localhost:8002/api/admin/dead-letter/messages?limit=100" | \
  jq '.data.messages[] | select(.failure_reason == "AUTH_ERROR")'

# 修复认证配置后重处理
```

### 性能优化

#### 1. 调整重试参数
```yaml
# 高负载环境
retry:
  max_attempts: 5
  initial_interval: 2s
  max_interval: 60s
  multiplier: 1.5

# 低延迟要求
retry:
  max_attempts: 2
  initial_interval: 500ms
  max_interval: 10s
  multiplier: 3.0
```

#### 2. 死信队列维护
```bash
# 定期清理过期消息（建议设置cron任务）
# 删除7天前的消息
curl -X DELETE "http://localhost:8002/api/admin/dead-letter/messages/cleanup?before=$(date -d '7 days ago' +%s)"
```

## 最佳实践

### 1. 监控告警
- 设置死信队列大小告警
- 监控特定错误类型趋势
- 关注重试成功率

### 2. 故障恢复
- 优先处理网络和超时错误
- 认证错误需要先修复配置
- 验证错误通常需要数据修复

### 3. 性能调优
- 根据业务特点调整重试参数
- 合理设置死信队列容量
- 定期清理历史数据

## 故障排查

### 常见问题

#### Q: 消息一直重试失败怎么办？
A: 
1. 检查错误类型和原因
2. 确认JumpServer服务状态
3. 验证网络连接
4. 检查认证配置

#### Q: 死信队列消息过多怎么处理？
A:
1. 分析失败原因分布
2. 修复根本问题
3. 分批重新处理
4. 清理无效消息

#### Q: 如何调整重试策略？
A:
1. 分析当前失败模式
2. 根据业务需求调整参数
3. 监控调整后的效果
4. 逐步优化配置

## 测试验证

### 功能测试脚本

创建测试脚本 `test_retry_dlq.sh`：

```bash
#!/bin/bash

BASE_URL="http://localhost:8002/api/admin"

echo "=== JumpServer-Sync 重试和死信队列功能测试 ==="

# 1. 检查系统状态
echo "1. 检查系统状态..."
curl -s "$BASE_URL/health" | jq .

# 2. 获取死信队列统计
echo "2. 获取死信队列统计..."
curl -s "$BASE_URL/dead-letter/stats" | jq .

# 3. 查看死信消息（如果有）
echo "3. 查看死信消息..."
curl -s "$BASE_URL/dead-letter/messages?limit=5" | jq .

# 4. 模拟故障恢复（如果有死信消息）
echo "4. 测试重新处理功能..."
MESSAGE_ID=$(curl -s "$BASE_URL/dead-letter/messages?limit=1" | jq -r '.data.messages[0].stream_id // empty')

if [ ! -z "$MESSAGE_ID" ]; then
    echo "重新处理消息: $MESSAGE_ID"
    curl -X POST "$BASE_URL/dead-letter/messages/$MESSAGE_ID/reprocess" | jq .
else
    echo "没有死信消息需要处理"
fi

echo "=== 测试完成 ==="
```

### 压力测试

创建压力测试脚本 `stress_test.sh`：

```bash
#!/bin/bash

# 模拟大量事件发送到Redis Streams
for i in {1..100}; do
    redis-cli XADD arboris:sync:events "*" \
        id "test-event-$i" \
        type "user.create" \
        source "test" \
        timestamp $(date +%s) \
        data '{"user_id": '$i', "username": "test'$i'"}'
done

echo "已发送100个测试事件"
```

## 总结

重试机制和死信队列大大提升了JumpServer-Sync的可靠性和运维友好性。通过合理配置和监控，可以确保系统在各种异常情况下都能稳定运行，并提供完善的故障恢复能力。

### 主要收益

1. **可靠性提升**：自动重试机制减少临时故障影响
2. **数据不丢失**：死信队列确保失败消息可恢复
3. **运维友好**：完整的管理接口和监控能力
4. **故障恢复**：支持手动和批量重新处理
5. **性能优化**：智能退避策略避免系统过载
