# 重新处理数据格式测试

## 问题描述

之前的重新处理逻辑发送的数据格式与主队列消费者期望的格式不匹配，导致重新处理的消息无法被正确解析。

## 修复前的数据格式

### 死信队列重新发送的格式（错误）
```json
{
  "data": "{\"id\":\"event-123\",\"type\":\"user.create\",\"source\":\"rdb\",\"timestamp\":1703123456,\"data\":{\"user_id\":123,\"username\":\"john.doe\"}}",
  "reprocessed": "true",
  "original_failed_at": 1703123456,
  "original_retry_count": 3,
  "original_stream_id": "1703123456789-0"
}
```

### 消费者期望的格式
```json
{
  "id": "event-123",
  "type": "user.create", 
  "source": "rdb",
  "timestamp": 1703123456,
  "data.user_id": 123,
  "data.username": "john.doe"
}
```

## 修复后的数据格式

### 死信队列重新发送的格式（正确）
```json
{
  "id": "event-123",
  "type": "user.create",
  "source": "rdb", 
  "timestamp": 1703123456,
  "data.user_id": "123",
  "data.username": "john.doe",
  "metadata.reprocessed": "true",
  "metadata.original_failed_at": 1703123456,
  "metadata.original_retry_count": 3,
  "metadata.original_stream_id": "1703123456789-0"
}
```

## 解析过程对比

### 修复前（解析失败）
```go
// parseMessage() 处理修复前的格式
for key, value := range message.Values {
    switch key {
    case "id":          // ❌ 找不到，event.ID = ""
    case "type":        // ❌ 找不到，event.Type = ""
    case "source":      // ❌ 找不到，event.Source = ""
    case "timestamp":   // ❌ 找不到，event.Timestamp = 0
    case "data":        // ❌ 不匹配任何模式，被忽略
    }
}
// 结果：event 对象几乎为空，处理会失败
```

### 修复后（解析成功）
```go
// parseMessage() 处理修复后的格式
for key, value := range message.Values {
    switch key {
    case "id":          // ✅ event.ID = "event-123"
    case "type":        // ✅ event.Type = "user.create"
    case "source":      // ✅ event.Source = "rdb"
    case "timestamp":   // ✅ event.Timestamp = 1703123456
    default:
        if strings.HasPrefix(key, "data.") {
            // ✅ event.Data["user_id"] = 123
            // ✅ event.Data["username"] = "john.doe"
        } else if strings.HasPrefix(key, "metadata.") {
            // ✅ event.Metadata["reprocessed"] = "true"
            // ✅ event.Metadata["original_failed_at"] = "1703123456"
        }
    }
}
// 结果：完整的 event 对象，可以正常处理
```

## 测试验证

### 1. 创建测试事件
```bash
# 发送一个会失败的测试事件
redis-cli XADD arboris:sync:events "*" \
  id "test-reprocess-123" \
  type "user.create" \
  source "test" \
  timestamp $(date +%s) \
  data.user_id "999" \
  data.username "test-reprocess"
```

### 2. 模拟失败进入死信队列
```bash
# 临时断开JumpServer连接，让事件失败
sudo iptables -A OUTPUT -d jumpserver_ip -j DROP

# 等待事件重试失败并进入死信队列
sleep 30

# 检查死信队列
curl -s http://localhost:8002/api/admin/dead-letter/messages | jq .
```

### 3. 测试重新处理
```bash
# 恢复JumpServer连接
sudo iptables -D OUTPUT -d jumpserver_ip -j DROP

# 获取死信消息ID
MESSAGE_ID=$(curl -s http://localhost:8002/api/admin/dead-letter/messages | jq -r '.data.messages[0].stream_id')

# 重新处理消息
curl -X POST http://localhost:8002/api/admin/dead-letter/messages/$MESSAGE_ID/reprocess

# 检查是否成功处理
curl -s http://localhost:8002/api/admin/dead-letter/stats | jq .
```

### 4. 验证数据格式
```bash
# 监控主队列中重新处理的消息格式
redis-cli XREAD STREAMS arboris:sync:events $
```

## 关键改进

### 1. **完整的字段映射**
- ✅ 正确映射 `id`, `type`, `source`, `timestamp`
- ✅ 正确映射 `data.*` 字段
- ✅ 正确映射 `metadata.*` 字段

### 2. **数据类型处理**
```go
// 复杂对象自动序列化为JSON
if jsonData, err := json.Marshal(value); err == nil {
    values[dataKey] = string(jsonData)
} else {
    values[dataKey] = value
}
```

### 3. **重新处理标记**
```go
// 添加重新处理的元数据，便于追踪
"metadata.reprocessed": "true",
"metadata.original_failed_at": deadMsg.FailedAt,
"metadata.original_retry_count": deadMsg.RetryCount,
"metadata.original_stream_id": deadMsg.StreamID,
```

## 总结

修复后的重新处理机制确保了：

1. **数据格式兼容**：重新发送的消息格式与原始消息完全一致
2. **完整性保证**：所有原始数据和元数据都被正确保留
3. **可追踪性**：添加了重新处理的标记，便于审计和调试
4. **类型安全**：复杂对象自动序列化，避免类型转换错误

现在重新处理的消息可以被主队列正确解析和处理了！
