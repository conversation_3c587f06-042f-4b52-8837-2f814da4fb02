package http

import (
	"arboris/src/common/address"
	"context"
	"fmt"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"net/http"
	"os"
	"strings"
	"time"

	"arboris/src/modules/jumpserver-sync/config"

	"github.com/gin-gonic/gin"
)

var srv = &http.Server{
	ReadTimeout:    30 * time.Second,
	WriteTimeout:   30 * time.Second,
	MaxHeaderBytes: 1 << 20,
}

var skipPaths = []string{"/health"}

// 全局管理接口处理器
var adminHandler *AdminHandler

// SetAdminHandler 设置管理接口处理器
func SetAdminHandler(handler *AdminHandler) {
	adminHandler = handler
}

func Start() {
	c := config.Config

	if strings.ToLower(c.HTTP.Mode) == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	Config(r)
	srv.Addr = address.GetHTTPListen("jumpserver-sync")
	srv.Handler = r

	go func() {
		fmt.Println("http.listening:", srv.Addr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("listening %s occur error: %s\n", srv.Addr, err)
			os.Exit(3)
		}
	}()
}

// Shutdown 关闭HTTP服务器
func Shutdown() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		fmt.Println("cannot shutdown http server:", err)
		os.Exit(2)
	}

	// catching ctx.Done(). timeout of 5 seconds.
	select {
	case <-ctx.Done():
		fmt.Println("shutdown http server timeout of 5 seconds.")
	default:
		fmt.Println("http server stopped")
	}
}

// Config 配置路由
func Config(r *gin.Engine) {
	// 添加 CORS 中间件
	r.Use(corsMiddleware())

	// Swagger 文档路由 - 启用认证信息持久化，默认展开所有分类
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler,
		ginSwagger.PersistAuthorization(true),
		ginSwagger.DocExpansion("list"),
	))

	// 健康检查
	r.GET("/health", healthCheck)

	// 服务信息
	r.GET("/info", serviceInfo)

	// 管理接口
	if adminHandler != nil {
		adminHandler.RegisterAdminRoutes(r)
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string `json:"status" example:"ok"`
	Timestamp int64  `json:"timestamp" example:"**********"`
}

// @Summary 健康检查
// @Description 检查JumpServer同步服务的健康状态
// @Tags Health
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse "服务正常"
// @Router /health [get]
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
	})
}

// ServiceInfoResponse 服务信息响应
type ServiceInfoResponse struct {
	Version string            `json:"version" example:"1.0.0"`
	Uptime  int64             `json:"uptime" example:"**********"`
	Config  ServiceConfigInfo `json:"config"`
}

// ServiceConfigInfo 服务配置信息
type ServiceConfigInfo struct {
	Redis      RedisConfigInfo      `json:"redis"`
	JumpServer JumpServerConfigInfo `json:"jumpserver"`
	Sync       SyncConfigInfo       `json:"sync"`
}

// RedisConfigInfo Redis配置信息
type RedisConfigInfo struct {
	Addr string `json:"addr" example:"127.0.0.1:6379"`
	DB   int    `json:"db" example:"0"`
}

// JumpServerConfigInfo JumpServer配置信息
type JumpServerConfigInfo struct {
	BaseURL      string `json:"base_url" example:"http://jumpserver.example.com"`
	Organization string `json:"organization" example:"Default"`
}

// SyncConfigInfo 同步配置信息
type SyncConfigInfo struct {
	Stream       string `json:"stream" example:"arboris:sync:events"`
	Group        string `json:"group" example:"jumpserver-sync"`
	Consumer     string `json:"consumer" example:"jumpserver-sync-1"`
	BatchSize    int    `json:"batch_size" example:"10"`
	PollInterval string `json:"poll_interval" example:"5s"`
}

// @Summary 获取服务信息
// @Description 获取JumpServer同步服务的版本、运行时间和配置信息
// @Tags Health
// @Accept json
// @Produce json
// @Success 200 {object} ServiceInfoResponse "服务信息"
// @Router /info [get]
func serviceInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"version": "1.0.0",
		"uptime":  time.Now().Unix(),
		"config": gin.H{
			"redis": gin.H{
				"addr": config.Config.Redis.Addr,
				"db":   config.Config.Redis.DB,
			},
			"jumpserver": gin.H{
				"base_url":     config.Config.JumpServer.BaseURL,
				"organization": config.Config.JumpServer.Organization,
			},
			"sync": gin.H{
				"stream":        config.Config.Sync.Consumer.Stream,
				"group":         config.Config.Sync.Consumer.Group,
				"consumer":      config.Config.Sync.Consumer.Consumer,
				"batch_size":    config.Config.Sync.Consumer.BatchSize,
				"poll_interval": config.Config.Sync.Consumer.PollInterval,
			},
		},
	})
}

// ApiResponse 通用API响应结构
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// renderJSON 渲染JSON响应
func renderJSON(c *gin.Context, code int, message string, data interface{}) {
	c.JSON(code, ApiResponse{
		Code:    code,
		Message: message,
		Data:    data,
	})
}
