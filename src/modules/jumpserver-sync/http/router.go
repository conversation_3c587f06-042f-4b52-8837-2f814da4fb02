package http

import (
	"net/http"
	"strconv"
	"strings"

	_ "arboris/docs-jumpserver-sync" // 导入生成的docs包
	"arboris/src/modules/jumpserver-sync/deadletter"
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
)

// AdminHandler 管理接口处理器
type AdminHandler struct {
	deadLetterQueue *deadletter.Queue
}

// NewAdminHandler 创建管理接口处理器
func NewAdminHandler(dlq *deadletter.Queue) *AdminHandler {
	return &AdminHandler{
		deadLetterQueue: dlq,
	}
}

// RegisterAdminRoutes 注册管理接口路由
func (h *AdminHandler) RegisterAdminRoutes(r *gin.Engine) {
	admin := r.Group("/api/admin")
	{
		// 死信队列管理
		dlq := admin.Group("/dead-letter")
		{
			dlq.GET("/messages", h.ListDeadMessages)
			dlq.GET("/messages/all", h.ListAllDeadMessages)
			dlq.GET("/messages/ids", h.ListDeadMessageIDs)
			dlq.GET("/messages/:id", h.GetDeadMessage)
			dlq.GET("/messages/stream/:stream_id", h.GetDeadMessageByStreamID)
			dlq.POST("/messages/:id/reprocess", h.ReprocessDeadMessage)
			dlq.POST("/messages/stream/:stream_id/reprocess", h.ReprocessDeadMessageByStreamID)
			dlq.POST("/messages/batch-reprocess", h.BatchReprocessDeadMessages)
			dlq.DELETE("/messages/:id", h.DeleteDeadMessage)
			dlq.GET("/stats", h.GetDeadLetterStats)
		}

		// 系统状态
		admin.GET("/health", h.HealthCheck)
		admin.GET("/status", h.GetSystemStatus)
	}
}

// DeadMessageListResponse 死信消息列表响应
type DeadMessageListResponse struct {
	Success bool                `json:"success" example:"true"`
	Data    DeadMessageListData `json:"data"`
}

// DeadMessageListData 死信消息列表数据
type DeadMessageListData struct {
	Messages []interface{} `json:"messages"`
	Limit    int           `json:"limit" example:"50"`
	Offset   int           `json:"offset" example:"0"`
	Count    int           `json:"count" example:"10"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error  string `json:"error" example:"Failed to list dead messages"`
	Detail string `json:"detail,omitempty" example:"connection timeout"`
}

// @Summary 列出死信队列消息
// @Description 获取死信队列中的消息列表，支持分页查询
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param limit query int false "每页数量，默认50，最大200" minimum(1) maximum(200) default(50)
// @Param offset query int false "偏移量，默认0" minimum(0) default(0)
// @Success 200 {object} DeadMessageListResponse "成功获取死信消息列表"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages [get]

func (h *AdminHandler) ListDeadMessages(c *gin.Context) {
	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200 // 最大限制
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// 获取死信消息
	messages, err := h.deadLetterQueue.ListDeadMessages(limit, offset)
	if err != nil {
		logger.Errorf("Failed to list dead messages: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Failed to list dead messages",
			"detail": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"messages": messages,
			"limit":    limit,
			"offset":   offset,
			"count":    len(messages),
		},
	})
}

// @Summary 获取所有死信消息
// @Description 获取死信队列中的所有消息，不分页
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} DeadMessageListResponse "成功获取所有死信消息"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/all [get]
func (h *AdminHandler) ListAllDeadMessages(c *gin.Context) {
	// 获取所有死信消息（设置一个较大的限制）
	messages, err := h.deadLetterQueue.ListDeadMessages(1000, 0)
	if err != nil {
		logger.Errorf("Failed to list all dead messages: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Failed to list all dead messages",
			"detail": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"messages": messages,
			"count":    len(messages),
		},
	})
}

// MessageIDsResponse 消息ID列表响应
type MessageIDsResponse struct {
	Success bool           `json:"success" example:"true"`
	Data    MessageIDsData `json:"data"`
}

// MessageIDsData 消息ID列表数据
type MessageIDsData struct {
	IDs   []string `json:"ids" example:"[\"1755594093213-0\",\"1755594093214-0\"]"`
	Count int      `json:"count" example:"2"`
}

// @Summary 获取死信消息ID列表
// @Description 获取死信队列中所有消息的ID列表
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} MessageIDsResponse "成功获取消息ID列表"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/ids [get]
func (h *AdminHandler) ListDeadMessageIDs(c *gin.Context) {
	ids, err := h.deadLetterQueue.ListDeadMessageIDs()
	if err != nil {
		logger.Errorf("Failed to list dead message IDs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Failed to list dead message IDs",
			"detail": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"ids":   ids,
			"count": len(ids),
		},
	})
}

// @Summary 根据Stream ID获取死信消息
// @Description 根据Redis Stream ID获取死信队列中的消息详情
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param stream_id path string true "Redis Stream ID" example:"1755594093213-0"
// @Success 200 {object} DeadMessageResponse "成功获取死信消息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "消息不存在"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/stream/{stream_id} [get]
func (h *AdminHandler) GetDeadMessageByStreamID(c *gin.Context) {
	streamID := c.Param("stream_id")
	if streamID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Stream ID is required",
		})
		return
	}

	message, err := h.deadLetterQueue.GetDeadMessageByStreamID(streamID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error":  "Message not found",
				"detail": err.Error(),
			})
		} else {
			logger.Errorf("Failed to get dead message by stream ID %s: %v", streamID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to get dead message",
				"detail": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    message,
	})
}

// DeadMessageResponse 单个死信消息响应
type DeadMessageResponse struct {
	Success bool        `json:"success" example:"true"`
	Data    interface{} `json:"data"`
}

// @Summary 获取单个死信消息
// @Description 根据消息ID获取死信队列中的单个消息详情
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path string true "消息ID"
// @Success 200 {object} DeadMessageResponse "成功获取死信消息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "消息不存在"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/{id} [get]
func (h *AdminHandler) GetDeadMessage(c *gin.Context) {
	messageID := c.Param("id")
	if messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Message ID is required",
		})
		return
	}

	message, err := h.deadLetterQueue.GetDeadMessageByID(messageID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error":  "Message not found",
				"detail": err.Error(),
			})
		} else {
			logger.Errorf("Failed to get dead message %s: %v", messageID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to get dead message",
				"detail": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    message,
	})
}

// ReprocessResponse 重新处理响应
type ReprocessResponse struct {
	Success bool                  `json:"success" example:"true"`
	Message string                `json:"message" example:"Message reprocessed successfully"`
	Data    ReprocessResponseData `json:"data"`
}

// ReprocessResponseData 重新处理响应数据
type ReprocessResponseData struct {
	MessageID string `json:"message_id" example:"msg-123456"`
}

// @Summary 重新处理死信消息
// @Description 将指定的死信消息重新放入处理队列进行处理
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path string true "消息ID"
// @Success 200 {object} ReprocessResponse "成功重新处理消息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "消息不存在"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/{id}/reprocess [post]
func (h *AdminHandler) ReprocessDeadMessage(c *gin.Context) {
	messageID := c.Param("id")
	if messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Message ID is required",
		})
		return
	}

	err := h.deadLetterQueue.ReprocessMessage(messageID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error":  "Message not found",
				"detail": err.Error(),
			})
		} else {
			logger.Errorf("Failed to reprocess message %s: %v", messageID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to reprocess message",
				"detail": err.Error(),
			})
		}
		return
	}

	logger.Infof("Dead message reprocessed successfully: %s", messageID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Message reprocessed successfully",
		"data": gin.H{
			"message_id": messageID,
		},
	})
}

// @Summary 根据Stream ID重新处理死信消息
// @Description 根据Redis Stream ID将指定的死信消息重新放入处理队列进行处理
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param stream_id path string true "Redis Stream ID" example:"1755594094875-0"
// @Success 200 {object} ReprocessResponse "成功重新处理消息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "消息不存在"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/stream/{stream_id}/reprocess [post]
func (h *AdminHandler) ReprocessDeadMessageByStreamID(c *gin.Context) {
	streamID := c.Param("stream_id")
	if streamID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Stream ID is required",
		})
		return
	}

	err := h.deadLetterQueue.ReprocessMessageByStreamID(streamID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error":  "Message not found",
				"detail": err.Error(),
			})
		} else {
			logger.Errorf("Failed to reprocess message by stream ID %s: %v", streamID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to reprocess message",
				"detail": err.Error(),
			})
		}
		return
	}

	logger.Infof("Dead message reprocessed successfully by stream ID: %s", streamID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Message reprocessed successfully",
		"data": gin.H{
			"stream_id": streamID,
		},
	})
}

// BatchReprocessRequest 批量重新处理请求
type BatchReprocessRequest struct {
	MessageIDs []string `json:"message_ids" binding:"required" example:"[\"msg-123\",\"msg-456\"]"`
}

// BatchReprocessResponse 批量重新处理响应
type BatchReprocessResponse struct {
	Success bool                       `json:"success" example:"true"`
	Message string                     `json:"message" example:"Batch reprocess completed"`
	Data    BatchReprocessResponseData `json:"data"`
}

// BatchReprocessResponseData 批量重新处理响应数据
type BatchReprocessResponseData struct {
	TotalCount   int      `json:"total_count" example:"10"`
	SuccessCount int      `json:"success_count" example:"8"`
	FailedCount  int      `json:"failed_count" example:"2"`
	FailedIDs    []string `json:"failed_ids" example:"[\"msg-123\",\"msg-456\"]"`
}

// @Summary 批量重新处理死信消息
// @Description 批量将多个死信消息重新放入处理队列进行处理，最多支持100个消息
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body BatchReprocessRequest true "批量重新处理请求"
// @Success 200 {object} BatchReprocessResponse "批量处理完成"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/batch-reprocess [post]
func (h *AdminHandler) BatchReprocessDeadMessages(c *gin.Context) {
	var req BatchReprocessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  "Invalid request format",
			"detail": err.Error(),
		})
		return
	}

	if len(req.MessageIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Message IDs are required",
		})
		return
	}

	if len(req.MessageIDs) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Too many messages, maximum 100 allowed",
		})
		return
	}

	successCount, failedIDs, err := h.deadLetterQueue.BatchReprocessMessages(req.MessageIDs)
	if err != nil {
		logger.Errorf("Failed to batch reprocess messages: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Failed to batch reprocess messages",
			"detail": err.Error(),
		})
		return
	}

	logger.Infof("Batch reprocess completed: success=%d, failed=%d", successCount, len(failedIDs))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Batch reprocess completed",
		"data": gin.H{
			"total_count":   len(req.MessageIDs),
			"success_count": successCount,
			"failed_count":  len(failedIDs),
			"failed_ids":    failedIDs,
		},
	})
}

// DeleteResponse 删除响应
type DeleteResponse struct {
	Success bool               `json:"success" example:"true"`
	Message string             `json:"message" example:"Message deleted successfully"`
	Data    DeleteResponseData `json:"data"`
}

// DeleteResponseData 删除响应数据
type DeleteResponseData struct {
	MessageID string `json:"message_id" example:"msg-123456"`
}

// @Summary 删除死信消息
// @Description 从死信队列中永久删除指定的消息
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path string true "消息ID"
// @Success 200 {object} DeleteResponse "成功删除消息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "消息不存在"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/messages/{id} [delete]
func (h *AdminHandler) DeleteDeadMessage(c *gin.Context) {
	messageID := c.Param("id")
	if messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Message ID is required",
		})
		return
	}

	err := h.deadLetterQueue.DeleteMessage(messageID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error":  "Message not found",
				"detail": err.Error(),
			})
		} else {
			logger.Errorf("Failed to delete message %s: %v", messageID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to delete message",
				"detail": err.Error(),
			})
		}
		return
	}

	logger.Infof("Dead message deleted successfully: %s", messageID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Message deleted successfully",
		"data": gin.H{
			"message_id": messageID,
		},
	})
}

// StatsResponse 统计信息响应
type StatsResponse struct {
	Success bool        `json:"success" example:"true"`
	Data    interface{} `json:"data"`
}

// @Summary 获取死信队列统计信息
// @Description 获取死信队列的统计信息，包括消息数量、处理状态等
// @Tags DeadLetter
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} StatsResponse "成功获取统计信息"
// @Failure 500 {object} ErrorResponse "内部服务器错误"
// @Router /api/admin/dead-letter/stats [get]
func (h *AdminHandler) GetDeadLetterStats(c *gin.Context) {
	stats, err := h.deadLetterQueue.GetStats()
	if err != nil {
		logger.Errorf("Failed to get dead letter stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Failed to get statistics",
			"detail": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// AdminHealthResponse 管理接口健康检查响应
type AdminHealthResponse struct {
	Status    string                 `json:"status" example:"healthy"`
	Service   string                 `json:"service" example:"jumpserver-sync"`
	Timestamp map[string]interface{} `json:"timestamp"`
}

// SystemStatusResponse 系统状态响应
type SystemStatusResponse struct {
	Success bool             `json:"success" example:"true"`
	Data    SystemStatusData `json:"data"`
}

// SystemStatusData 系统状态数据
type SystemStatusData struct {
	Service         string      `json:"service" example:"jumpserver-sync"`
	Status          string      `json:"status" example:"running"`
	DeadLetterQueue interface{} `json:"dead_letter_queue"`
}

// @Summary 管理接口健康检查
// @Description 检查JumpServer同步服务管理接口的健康状态
// @Tags Admin
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} AdminHealthResponse "服务健康"
// @Router /api/admin/health [get]
func (h *AdminHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "healthy",
		"service": "jumpserver-sync",
		"timestamp": gin.H{
			"unix": gin.H{
				"seconds": gin.H{
					"value": "current_timestamp",
				},
			},
		},
	})
}

// @Summary 获取系统状态
// @Description 获取JumpServer同步服务的整体运行状态，包括死信队列状态
// @Tags System
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} SystemStatusResponse "系统状态信息"
// @Router /api/admin/status [get]
func (h *AdminHandler) GetSystemStatus(c *gin.Context) {
	// 获取死信队列统计
	dlqStats, err := h.deadLetterQueue.GetStats()
	if err != nil {
		logger.Warningf("Failed to get DLQ stats: %v", err)
		dlqStats = map[string]interface{}{
			"error": "Failed to get DLQ stats",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"service":           "jumpserver-sync",
			"status":            "running",
			"dead_letter_queue": dlqStats,
		},
	})
}
