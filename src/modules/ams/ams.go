// @title AMS API
// @version 1.0
// @description 主机资产管理系统 API 文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host
// @BasePath /api/ams-ce

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-User-Token
// @description API Token 认证

package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"github.com/toolkits/pkg/logger"
	"github.com/toolkits/pkg/runner"

	"arboris/src/common/loggeri"
	"arboris/src/models"
	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/http"
	"arboris/src/modules/ams/service"
	"arboris/src/toolkits/i18n"

	_ "arboris/docs-ams" // 导入生成的docs包
)

var (
	vers *bool
	help *bool
	conf *string

	version = "1.0.0"
)

func init() {
	vers = flag.Bool("v", false, "display the version.")
	help = flag.Bool("h", false, "print this help.")
	conf = flag.String("f", "", "specify configuration file.")
	flag.Parse()

	if *vers {
		fmt.Println("Version:", version)
		os.Exit(0)
	}

	if *help {
		flag.Usage()
		os.Exit(0)
	}

	i18n.Init()

	runner.Init()
	fmt.Println("runner.cwd:", runner.Cwd)
	fmt.Println("runner.hostname:", runner.Hostname)
}

func main() {
	parseConf()

	loggeri.Init(config.Config.Logger)

	// 初始化数据库和相关数据
	models.InitMySQL("ams")

	i18n.Init(config.Config.I18n)

	// 初始化全局同步管理器
	initGlobalSyncManager()

	// 启动一致性检查器
	startConsistencyChecker()

	http.Start()

	endingProc()
}

func parseConf() {
	if err := config.Parse(); err != nil {
		fmt.Println("cannot parse configuration file:", err)
		os.Exit(1)
	}
}

// initGlobalSyncManager 初始化全局同步管理器
func initGlobalSyncManager() {
	logger.Info("Initializing global SyncManager...")
	if err := service.InitGlobalSyncManager(); err != nil {
		logger.Errorf("Failed to initialize global SyncManager: %v", err)
	} else {
		logger.Info("Global SyncManager initialized successfully")
	}
}

// startConsistencyChecker 启动一致性检查器
func startConsistencyChecker() {
	go func() {
		// 延迟5分钟启动，避免服务启动时的资源竞争
		logger.Info("Consistency checker will start in 5 minutes")

		timer := time.NewTimer(5 * time.Minute)
		defer timer.Stop()

		select {
		case <-timer.C:
			syncManager, err := service.GetGlobalSyncManager()
			if err != nil {
				logger.Errorf("Failed to get global sync manager for consistency checker: %v", err)
				if initErr := service.InitGlobalSyncManager(); initErr != nil {
					logger.Errorf("Failed to reinitialize global sync manager: %v", initErr)
					return
				}
				syncManager, err = service.GetGlobalSyncManager()
				if err != nil {
					logger.Errorf("Still failed to get sync manager after reinit: %v", err)
					return
				}
			}

			logger.Info("Starting JumpServer consistency checker")
			syncManager.StartConsistencyChecker()
		}
	}()
}

func endingProc() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	select {
	case <-c:
		fmt.Printf("stop signal caught, stopping... pid=%d\n", os.Getpid())
	}

	logger.Close()
	http.Shutdown()
	fmt.Println("process stopped successfully")
}
