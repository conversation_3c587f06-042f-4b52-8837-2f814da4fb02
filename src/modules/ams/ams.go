package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	_ "github.com/go-sql-driver/mysql"

	"github.com/toolkits/pkg/logger"
	"github.com/toolkits/pkg/runner"

	"arboris/src/common/loggeri"
	"arboris/src/models"
	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/http"
	rdbEvents "arboris/src/modules/rdb/events"
	"arboris/src/toolkits/i18n"
)

// @title AMS API
// @version 1.0
// @description Arboris AMS 模块 API 文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @BasePath /

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-User-Token
// @description API Token 认证

// @securityDefinitions.apikey CookieAuth
// @in header
// @name Cookie
// @description Cookie 认证 (ecmc-sid)

var (
	vers *bool
	help *bool
	conf *string

	version = "No Version Provided"
)

func init() {
	vers = flag.Bool("v", false, "display the version.")
	help = flag.Bool("h", false, "print this help.")
	conf = flag.String("f", "", "specify configuration file.")
	flag.Parse()

	if *vers {
		fmt.Println("Version:", version)
		os.Exit(0)
	}

	if *help {
		flag.Usage()
		os.Exit(0)
	}

	i18n.Init()

	runner.Init()
	fmt.Println("runner.cwd:", runner.Cwd)
	fmt.Println("runner.hostname:", runner.Hostname)
}

func main() {
	parseConf()

	loggeri.Init(config.Config.Logger)

	// 初始化数据库和相关数据
	models.InitMySQL("ams", "rdb")

	i18n.Init(config.Config.I18n)

	// 初始化JumpServer同步事件发布器
	if err := rdbEvents.InitJSEventProducerWithConfig(
		config.Config.Redis.Addr,
		config.Config.Redis.Password,
		config.Config.Redis.DB,
	); err != nil {
		logger.Errorf("Failed to initialize JumpServer sync event producer: %v", err)
	} else {
		logger.Info("JumpServer sync event producer initialized successfully")
	}

	http.Start()

	endingProc()
}

func parseConf() {
	if err := config.Parse(); err != nil {
		fmt.Println("cannot parse configuration file:", err)
		os.Exit(1)
	}
}

func endingProc() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	select {
	case <-c:
		fmt.Printf("stop signal caught, stopping... pid=%d\n", os.Getpid())
	}

	logger.Close()
	http.Shutdown()
	fmt.Println("process stopped successfully")
}
