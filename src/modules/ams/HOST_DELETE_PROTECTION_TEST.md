# 主机删除保护机制测试指南

## 功能概述

实现了主机删除保护机制，防止误删已绑定到节点的主机。当尝试删除已绑定到节点的主机时，系统会：

1. 阻止删除操作
2. 返回详细的绑定信息
3. 提供清晰的错误提示和解决建议

## API 变更

### 删除主机接口

**接口**: `DELETE /api/ams-ce/hosts`

**新增响应格式**:

```json
// 成功删除（无绑定主机）
{
  "err": null,
  "dat": null
}

// 部分成功（有绑定主机被阻止）
{
  "err": "成功删除 2 台主机，但无法删除 1 台主机，因为它们已绑定到节点。请先从节点解绑这些主机，然后再删除。",
  "dat": [
    {
      "host_id": 123,
      "host_name": "web-server-01",
      "host_ip": "*************",
      "host_ident": "web-01",
      "node_paths": ["/公司/研发部/Web服务", "/公司/测试部/测试环境"]
    }
  ]
}

// 全部失败（所有主机都已绑定）
{
  "err": "无法删除 3 台主机，因为它们已绑定到节点。请先从节点解绑这些主机，然后再删除。",
  "dat": [
    {
      "host_id": 123,
      "host_name": "web-server-01",
      "host_ip": "*************",
      "host_ident": "web-01",
      "node_paths": ["/公司/研发部/Web服务"]
    },
    {
      "host_id": 124,
      "host_name": "db-server-01",
      "host_ip": "*************",
      "host_ident": "db-01",
      "node_paths": ["/公司/研发部/数据库服务"]
    }
  ]
}
```

## 测试场景

### 场景1：删除未绑定的主机

**前置条件**: 主机存在但未绑定到任何节点

**测试步骤**:
1. 创建主机（不绑定到节点）
2. 调用删除接口
3. 验证删除成功

**期望结果**: 
- HTTP 200
- 主机被成功删除
- 返回成功响应

### 场景2：删除已绑定的主机

**前置条件**: 主机已绑定到一个或多个节点

**测试步骤**:
1. 创建主机并绑定到节点
2. 调用删除接口
3. 验证删除被阻止

**期望结果**:
- HTTP 400
- 主机未被删除
- 返回绑定信息和错误提示

### 场景3：批量删除混合状态主机

**前置条件**: 部分主机已绑定，部分主机未绑定

**测试步骤**:
1. 准备多台主机（部分绑定，部分未绑定）
2. 批量调用删除接口
3. 验证部分删除结果

**期望结果**:
- HTTP 400
- 未绑定的主机被删除
- 已绑定的主机被保护
- 返回详细的处理结果

## 测试用例

### 用例1：单台未绑定主机删除

```bash
# 1. 创建主机
curl -X POST "http://localhost:8002/api/ams-ce/host" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "sn": "TEST001",
    "ip": "*************",
    "ident": "test-host-01",
    "name": "测试主机01",
    "cate": "server"
  }'

# 2. 删除主机
curl -X DELETE "http://localhost:8002/api/ams-ce/hosts" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [主机ID]
  }'
```

### 用例2：单台已绑定主机删除

```bash
# 1. 创建主机
curl -X POST "http://localhost:8002/api/ams-ce/host" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "sn": "TEST002",
    "ip": "*************",
    "ident": "test-host-02",
    "name": "测试主机02",
    "cate": "server"
  }'

# 2. 绑定主机到节点
curl -X PUT "http://localhost:8002/api/ams-ce/hosts/node" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [主机ID],
    "nodeids": [节点ID]
  }'

# 3. 尝试删除主机（应该被阻止）
curl -X DELETE "http://localhost:8002/api/ams-ce/hosts" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [主机ID]
  }'
```

### 用例3：批量删除混合状态主机

```bash
# 删除多台主机（部分绑定，部分未绑定）
curl -X DELETE "http://localhost:8002/api/ams-ce/hosts" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [未绑定主机ID, 已绑定主机ID1, 已绑定主机ID2]
  }'
```

## 验证要点

1. **数据完整性**: 被保护的主机数据完整，绑定关系未被破坏
2. **错误信息**: 错误提示清晰，包含具体的绑定信息
3. **部分成功**: 批量操作时能正确处理混合状态
4. **日志记录**: 操作日志正确记录删除尝试和结果
5. **权限检查**: 仍然需要相应的删除权限

## 解绑操作指南

当主机删除被阻止时，用户需要先解绑主机：

```bash
# 从节点解绑主机
curl -X POST "http://localhost:8000/api/rdb/node/{节点ID}/resources/unbind" \
  -H "X-User-Token: your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [资源ID]
  }'
```

解绑后即可正常删除主机。
