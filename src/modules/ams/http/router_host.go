package http

import (
	"arboris/src/models"
	"fmt"

	"github.com/gin-gonic/gin"
)

// HostListResponse 主机列表响应
type HostListResponse struct {
	List  []models.Host `json:"list" description:"主机列表"`
	Total int64         `json:"total" description:"总数量"`
}

// ApiResponse 通用API响应结构
type ApiResponse struct {
	Dat interface{} `json:"dat" description:"响应数据"`
	Err string      `json:"err" description:"错误信息"`
}

// @Summary 获取主机列表
// @Description 获取主机列表，支持分页、搜索和批量筛选
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param tenant query string false "租户"
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Param batch query string false "批量IP列表，逗号分隔"
// @Param field query string false "搜索字段" default(ip)
// @Success 200 {object} ApiResponse{dat=HostListResponse} "主机列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts [get]
func hostGets(c *gin.Context) {
	tenant := queryStr(c, "tenant", "")
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")
	batch := queryStr(c, "batch", "")
	field := queryStr(c, "field", "ip")

	var total int64
	var list []models.Host
	var err error

	customField, err := models.HostFieldGet("field_ident = ?", field)
	dangerous(err)

	if customField != nil {
		// 使用自定义字段查询
		total, err = models.HostTotalForCustomField(tenant, query, batch, field)
		dangerous(err)

		list, err = models.HostGetsForCustomField(tenant, query, batch, field, limit, offset(c, limit))
		dangerous(err)
	} else {
		// 使用基础字段查询
		total, err = models.HostTotalForAdmin(tenant, query, batch, field)
		dangerous(err)

		list, err = models.HostGetsForAdmin(tenant, query, batch, field, limit, offset(c, limit))
		dangerous(err)
	}

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取单个主机信息
// @Description 根据主机ID获取主机详细信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "主机ID"
// @Success 200 {object} ApiResponse{dat=models.Host} "主机信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "主机不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/host/{id} [get]
func hostGet(c *gin.Context) {
	host, err := models.HostGet("id=?", urlParamInt64(c, "id"))
	renderData(c, host, err)
}

type idsOrIpsForm struct {
	Ids []int64  `json:"ids"`
	Ips []string `json:"ips"`
}

func (f *idsOrIpsForm) Validate() {
	if len(f.Ids) == 0 {
		if len(f.Ips) == 0 {
			bomb("args invalid")
		}
		ids, err := models.HostIdsByIps(f.Ips)
		dangerous(err)

		f.Ids = ids
	}
}

type hostNodeForm struct {
	Ids     []int64 `json:"ids"`
	NodeIds []int64 `json:"nodeids"`
}

func (f *hostNodeForm) Validate() {
	if len(f.Ids) == 0 {
		bomb("%s is empty", "ids")
	}

	if len(f.NodeIds) == 0 {
		bomb("nodeids is empty")
	}

	for _, nodeId := range f.NodeIds {
		if nodeId <= 0 {
			bomb("nodeid is illegal: %d", nodeId)
		}
	}
}

type hostNoteForm struct {
	Ids  []int64 `json:"ids"`
	Note string  `json:"note"`
}

// @Summary 修改主机备注
// @Description 批量修改主机设备的备注信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param body body hostNoteForm true "主机备注修改参数"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/note [put]
func hostNotePut(c *gin.Context) {
	var f hostNoteForm
	bind(c, &f)

	if len(f.Ids) == 0 {
		bomb("%s is empty", "ids")
	}

	loginUser(c).CheckPermGlobal("ams_host_modify")

	renderMessage(c, models.HostUpdateNote(f.Ids, f.Note))
}

type hostCateForm struct {
	Ids  []int64 `json:"ids"`
	Cate string  `json:"cate"`
}

// @Summary 修改主机类别
// @Description 批量修改主机设备的类别信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param body body hostCateForm true "主机类别修改参数"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/cate [put]
func hostCatePut(c *gin.Context) {
	var f hostCateForm
	bind(c, &f)

	if len(f.Ids) == 0 {
		bomb("%s is empty", "ids")
	}

	loginUser(c).CheckPermGlobal("ams_host_modify")

	renderMessage(c, models.HostUpdateCate(f.Ids, f.Cate))
}

// HostBindingInfo 主机绑定信息
type HostBindingInfo struct {
	HostID    int64    `json:"host_id"`
	HostName  string   `json:"host_name"`
	HostIP    string   `json:"host_ip"`
	HostIdent string   `json:"host_ident"`
	NodePaths []string `json:"node_paths"`
}

// @Summary 删除主机
// @Description 批量删除主机设备，支持通过ID或IP删除。如果主机已绑定到节点，将阻止删除并返回绑定信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param body body idsOrIpsForm true "主机删除参数"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 400 {object} ApiResponse{dat=[]HostBindingInfo} "部分主机已绑定到节点，无法删除"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts [delete]
func hostDel(c *gin.Context) {
	var f idsOrIpsForm
	bind(c, &f)
	f.Validate()

	loginUser(c).CheckPermGlobal("ams_host_delete")

	var blockedHosts []HostBindingInfo
	var successCount int

	count := len(f.Ids)
	for i := 0; i < count; i++ {
		id := f.Ids[i]

		host, err := models.HostGet("id=?", id)
		dangerous(err)

		if host == nil {
			continue
		}

		// 检查主机是否绑定到节点
		boundNodes, err := checkHostBoundToNodes(host.Id)
		dangerous(err)

		if len(boundNodes) > 0 {
			// 主机已绑定到节点，不允许删除
			blockedHosts = append(blockedHosts, HostBindingInfo{
				HostID:    host.Id,
				HostName:  host.Name,
				HostIP:    host.IP,
				HostIdent: host.Ident,
				NodePaths: boundNodes,
			})
			continue
		}

		// 执行删除
		dangerous(models.ResourceUnregister([]string{fmt.Sprintf("host-%d", host.Id)}))
		dangerous(host.Del())
		successCount++
	}

	// 如果有被阻止的主机，返回错误信息
	if len(blockedHosts) > 0 {
		errorMsg := fmt.Sprintf("无法删除 %d 台主机，因为它们已绑定到节点。请先从节点解绑这些主机，然后再删除。", len(blockedHosts))
		if successCount > 0 {
			errorMsg = fmt.Sprintf("成功删除 %d 台主机，但无法删除 %d 台主机，因为它们已绑定到节点。请先从节点解绑这些主机，然后再删除。", successCount, len(blockedHosts))
		}

		c.JSON(400, gin.H{
			"err": errorMsg,
			"dat": blockedHosts,
		})
		return
	}

	renderMessage(c, nil)
}

// checkHostBoundToNodes 检查主机是否绑定到节点，返回绑定的节点路径列表
func checkHostBoundToNodes(hostId int64) ([]string, error) {
	// 构造资源UUID
	uuid := fmt.Sprintf("host-%d", hostId)

	// 获取资源ID
	resourceIds, err := models.ResourceIdsByUUIDs([]string{uuid})
	if err != nil {
		return nil, fmt.Errorf("failed to get resource IDs: %v", err)
	}

	if len(resourceIds) == 0 {
		// 资源不存在，说明没有绑定到任何节点
		return []string{}, nil
	}

	// 获取绑定的节点ID列表
	nodeIds, err := models.NodeIdsGetByResIds(resourceIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get node IDs: %v", err)
	}

	if len(nodeIds) == 0 {
		// 没有绑定到任何节点
		return []string{}, nil
	}

	// 获取节点信息
	nodes, err := models.NodeByIds(nodeIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes: %v", err)
	}

	// 提取节点路径
	var nodePaths []string
	for _, node := range nodes {
		nodePaths = append(nodePaths, node.Path)
	}

	return nodePaths, nil
}

// mapKeyClear map key clear
func mapKeyClear(src map[string]interface{}, save map[string]struct{}) {
	var dels []string
	for k := range src {
		if _, ok := save[k]; !ok {
			dels = append(dels, k)
		}
	}

	for i := 0; i < len(dels); i++ {
		delete(src, dels[i])
	}
}

type hostTenantForm struct {
	Ids    []int64 `json:"ids"`
	Tenant string  `json:"tenant"`
}

func (f *hostTenantForm) Validate() {
	if len(f.Ids) == 0 {
		bomb("ids is empty")
	}

	if f.Tenant == "" {
		bomb("tenant is blank")
	}
}

// @Summary 主机挂载到节点
// @Description 将主机设备挂载到多个节点，支持一台主机同时挂载到多个节点
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param body body hostNodeForm true "主机节点挂载参数"
// @Success 200 {object} ApiResponse "挂载成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/node [put]
func hostNodePut(c *gin.Context) {
	var f hostNodeForm
	bind(c, &f)
	f.Validate()

	loginUser(c).CheckPermGlobal("ams_host_modify")

	// 获取所有要挂载的节点
	nodes, err := models.NodeByIds(f.NodeIds)
	dangerous(err)
	if len(nodes) == 0 {
		bomb("nodes is empty")
	}

	// 检查所有节点都是叶子节点
	for _, node := range nodes {
		if node.Leaf != 1 {
			bomb("node %s is not leaf", node.Name)
		}
	}

	hosts, err := models.HostByIds(f.Ids)
	dangerous(err)
	if len(hosts) == 0 {
		bomb("hosts is empty")
	}

	// 检查主机是否已经属于某个租户
	for _, h := range hosts {
		if h.Tenant != "" {
			bomb("%s already belongs to %s", h.Name, h.Tenant)
		}
	}

	// 获取资源UUID
	var resUuids []string
	for _, id := range f.Ids {
		idStr := fmt.Sprintf("host-%d", id)
		resUuids = append(resUuids, idStr)
	}
	if len(resUuids) == 0 {
		bomb("res is empty")
	}
	resIds, err := models.ResourceIdsByUUIDs(resUuids)
	dangerous(err)
	if len(resIds) == 0 {
		bomb("res ids is empty")
	}

	// 为每个节点绑定主机
	for _, node := range nodes {
		// 绑定租户（只在第一个节点时执行，避免重复）
		if node.Id == f.NodeIds[0] {
			tenant := node.Tenant()
			err = models.HostUpdateTenant(f.Ids, tenant)
			dangerous(err)
			dangerous(models.ResourceRegister(hosts, tenant))
		}

		// 绑定到当前节点
		dangerous(node.Bind(resIds))
	}

	renderMessage(c, nil)
}

// @Summary 修改主机租户
// @Description 管理员修改主机设备的租户，相当于分配设备到指定租户
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param body body hostTenantForm true "主机租户修改参数"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/tenant [put]
func hostTenantPut(c *gin.Context) {
	var f hostTenantForm
	bind(c, &f)
	f.Validate()

	hosts, err := models.HostByIds(f.Ids)
	dangerous(err)

	if len(hosts) == 0 {
		bomb("hosts is empty")
	}

	loginUser(c).CheckPermGlobal("ams_host_modify")

	err = models.HostUpdateTenant(f.Ids, f.Tenant)
	if err == nil {
		dangerous(models.ResourceRegister(hosts, f.Tenant))
	}

	renderMessage(c, err)
}
