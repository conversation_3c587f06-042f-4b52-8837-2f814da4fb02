// Package http provides HTTP handlers and routing for AMS-CE
package http

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func Config(r *gin.Engine) {

	// Swagger 文档路由 - 启用认证信息持久化
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler,
		ginSwagger.PersistAuthorization(true),
		ginSwagger.DocExpansion("list"),
		ginSwagger.DeepLinking(true),
	))

	notLogin := r.Group("/api/ams-ce")
	{
		notLogin.GET("/ping", ping)
	}

	userLogin := r.Group("/api/ams-ce").Use(shouldBeLogin())
	{
		userLogin.GET("/hosts", hostGets)
		userLogin.POST("/host", hostAdd)
		userLogin.GET("/host/:id", hostGet)
		userLogin.PUT("/host/:id", hostUpdate)
		userLogin.PUT("/hosts/note", hostNotePut)
		userLogin.PUT("/hosts/cate", hostCatePut)
		userLogin.DELETE("/hosts", hostDel)
		userLogin.POST("/hosts/fields", hostFieldNew)
		userLogin.GET("/hosts/fields", hostFieldsGets)
		userLogin.GET("/hosts/field/:id", hostFieldGet)
		userLogin.PUT("/hosts/field/:id", hostFieldPut)
		userLogin.DELETE("/hosts/field/:id", hostFieldDel)
		userLogin.GET("/host/:id/fields", hostFieldGets)
		userLogin.PUT("/host/:id/fields", hostFieldPuts)
		userLogin.POST("/hosts/fields/values", hostFieldValueGets)
		userLogin.POST("/host/filter", hostFilter)

		userLogin.GET("/hosts/csv/template", GetHostCSVTemplate)
		userLogin.POST("/hosts/csv/import", ImportHostsFromCSVStream)
		userLogin.GET("/hosts/csv/failed/:import_id", DownloadFailedData)

		// 用户管理
		userLogin.POST("/users", userCreate)
		userLogin.DELETE("/users/:username", userDelete)
		userLogin.PUT("/users/:username/disable", userDisable)
		userLogin.PUT("/users/:username/enable", userEnable)
		userLogin.GET("/users/:username/hosts", userHosts)

		// 主机同步
		userLogin.POST("/hosts/sync", hostSync)

		// 用户授权管理
		userLogin.POST("/permissions/grant-user", userPermissionGrant)
		userLogin.POST("/permissions/revoke-user", userPermissionRevoke)

		// 同步管理器状态监控
		userLogin.GET("/sync/status", getSyncStatus)
		userLogin.POST("/sync/reset", resetSyncManager)

		// 账号管理
		userLogin.POST("/accounts/bind", bindAccountTemplate)
		userLogin.GET("/accounts/bindings", accountBindingList)
		userLogin.DELETE("/accounts/bindings", accountBindingUnbind)
		userLogin.GET("/accounts/sync/status", getAccountSyncStatus)
	}
}
