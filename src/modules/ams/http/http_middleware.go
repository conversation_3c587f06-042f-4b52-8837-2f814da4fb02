package http

import (
	"fmt"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/config"
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
	"net/http"
)

func shouldBeLogin() gin.HandlerFunc {
	return func(c *gin.Context) {
		username := mustUsername(c)

		// 获取用户信息并检查临时用户有效期
		user, err := models.UserGet("username=?", username)
		if err != nil {
			bomb("failed to get user: %v", err)
		}
		if user == nil {
			bomb("user not found")
		}

		// 检查临时用户是否已过期
		if err := checkTempUserExpiry(user); err != nil {
			bomb(err.Error())
		}

		c.Set("username", username)
		c.Set("user", user)
		c.Next()
	}
}

func mustUsername(c *gin.Context) string {
	username := sessionUsername(c)
	if username == "" {
		username = headerUsername(c)
	}

	if username == "" {
		bomb("unauthorized")
	}

	return username
}

func sessionUsername(c *gin.Context) string {
	sess, err := models.SessionGetWithCache(readSessionId(c))
	if err != nil {
		return ""
	}
	return sess.Username
}

func headerUsername(c *gin.Context) string {
	token := c.GetHeader("X-User-Token")
	if token == "" {
		return ""
	}

	ut, err := models.UserTokenGet("token=?", token)
	if err != nil {
		logger.Warningf("UserTokenGet[%s] fail: %v", token, err)
		return ""
	}

	if ut == nil {
		return ""
	}

	// 检查Token对应的用户是否为临时用户且已过期
	user, err := models.UserGet("username=?", ut.Username)
	if err != nil {
		logger.Warningf("UserGet[%s] fail: %v", ut.Username, err)
		return ""
	}

	if user != nil {
		if err := checkTempUserExpiry(user); err != nil {
			logger.Warningf("Temporary user expired: %s, error: %v", ut.Username, err)
			return ""
		}
	}

	return ut.Username
}

// checkTempUserExpiry 检查临时用户是否已过期
func checkTempUserExpiry(user *models.User) error {
	if user.Type == models.USER_T_TEMP {
		now := time.Now().Unix()
		if now < user.ActiveBegin {
			return fmt.Errorf("临时账号尚未生效，生效时间：%s",
				time.Unix(user.ActiveBegin, 0).Format("2006-01-02 15:04:05"))
		}
		if user.ActiveEnd < now {
			return fmt.Errorf("临时账号已过期，过期时间：%s",
				time.Unix(user.ActiveEnd, 0).Format("2006-01-02 15:04:05"))
		}
	}
	return nil
}

// CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 允许所有来源
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-User-Token")
		c.Header("Access-Control-Expose-Headers", "Content-Length")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

func readSessionId(c *gin.Context) string {
	sid, err := c.Cookie(config.Config.HTTP.CookieName)
	if err != nil {
		return ""
	}
	return sid
}
