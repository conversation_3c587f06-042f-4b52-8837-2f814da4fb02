package http

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
	jsEvents "arboris/src/modules/jumpserver-sync/events"
	rdbEvents "arboris/src/modules/rdb/events"
)

// hostUpdateForm 主机更新表单
type hostUpdateForm struct {
	Name          string `json:"name"`
	Note          string `json:"note"`
	OSVersion     string `json:"os_version"`
	KernelVersion string `json:"kernel_version"`
	CPUModel      string `json:"cpu_model"`
	CPU           string `json:"cpu"`
	Mem           string `json:"mem"`
	Disk          string `json:"disk"`
	GPU           string `json:"gpu"`
	GPUModel      string `json:"gpu_model"`
	Model         string `json:"model"`
	Manufacturer  string `json:"manufacturer"`
	IDC           string `json:"idc"`
	Zone          string `json:"zone"`
	Rack          string `json:"rack"`
	Cate          string `json:"cate"`
}

// @Summary 更新主机信息
// @Description 更新单台主机的详细信息，支持基础字段。IP、ID和标识符不可修改
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "主机ID"
// @Param body body hostUpdateForm true "主机更新参数"
// @Success 200 {object} ApiResponse{dat=models.Host} "更新后的主机信息"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "主机不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/host/{id} [put]
func hostPut(c *gin.Context) {
	var f hostUpdateForm
	bind(c, &f)

	hostId := urlParamInt64(c, "id")
	if hostId <= 0 {
		bomb("invalid host id")
	}

	// 权限检查
	loginUser(c).CheckPermGlobal("ams_host_modify")

	// 获取原始主机信息
	originalHost, err := models.HostGet("id=?", hostId)
	dangerous(err)

	if originalHost == nil {
		bomb("host not found")
	}

	// 构建更新字段映射
	updateFields := buildUpdateFields(&f)
	if len(updateFields) == 0 {
		bomb("no fields to update")
	}

	// 更新主机信息
	err = originalHost.UpdateFields(updateFields)
	dangerous(err)

	// 获取更新后的主机信息
	updatedHost, err := models.HostGet("id=?", hostId)
	dangerous(err)

	// 检查是否需要同步到RDB和发布JumpServer事件
	go handleHostUpdateSync(updatedHost)

	renderData(c, updatedHost, nil)
}

// buildUpdateFields 构建更新字段映射，排除空值和不可修改字段
func buildUpdateFields(f *hostUpdateForm) map[string]interface{} {
	fields := make(map[string]interface{})

	// 只添加非空字段
	if f.Name != "" {
		fields["name"] = f.Name
	}
	if f.Note != "" {
		fields["note"] = f.Note
	}
	if f.OSVersion != "" {
		fields["os_version"] = f.OSVersion
	}
	if f.KernelVersion != "" {
		fields["kernel_version"] = f.KernelVersion
	}
	if f.CPUModel != "" {
		fields["cpu_model"] = f.CPUModel
	}
	if f.CPU != "" {
		fields["cpu"] = f.CPU
	}
	if f.Mem != "" {
		fields["mem"] = f.Mem
	}
	if f.Disk != "" {
		fields["disk"] = f.Disk
	}
	if f.GPU != "" {
		fields["gpu"] = f.GPU
	}
	if f.GPUModel != "" {
		fields["gpu_model"] = f.GPUModel
	}
	if f.Model != "" {
		fields["model"] = f.Model
	}
	if f.Manufacturer != "" {
		fields["manufacturer"] = f.Manufacturer
	}
	if f.IDC != "" {
		fields["idc"] = f.IDC
	}
	if f.Zone != "" {
		fields["zone"] = f.Zone
	}
	if f.Rack != "" {
		fields["rack"] = f.Rack
	}
	if f.Cate != "" {
		fields["cate"] = f.Cate
	}

	return fields
}

// handleHostUpdateSync 处理主机更新后的同步逻辑
func handleHostUpdateSync(host *models.Host) {
	if host == nil {
		logger.Warning("Host is nil, skipping sync")
		return
	}

	// 检查主机同步状态
	synced, bindToNode, err := checkHostSyncStatus(host.Id)
	if err != nil {
		logger.Errorf("Failed to check host sync status for host %d: %v", host.Id, err)
		return
	}

	logger.Infof("Host %d sync status: synced=%v, bindToNode=%v", host.Id, synced, bindToNode)

	// 只有当主机已同步到RDB且已挂载到节点时，才需要同步更新
	if !synced || !bindToNode {
		logger.Infof("Host %d not synced or not bound to node, skipping sync", host.Id)
		return
	}

	// 同步到RDB resource表
	err = syncHostToRDB(host)
	if err != nil {
		logger.Errorf("Failed to sync host %d to RDB: %v", host.Id, err)
		return
	}

	// 发布JumpServer更新事件
	err = publishHostUpdateEvent(host)
	if err != nil {
		logger.Errorf("Failed to publish host update event for host %d: %v", host.Id, err)
		return
	}

	logger.Infof("Host %d update synced successfully", host.Id)
}

// checkHostSyncStatus 检查主机同步状态
func checkHostSyncStatus(hostId int64) (synced bool, bindToNode bool, err error) {
	// 检查是否已同步到RDB
	uuid := fmt.Sprintf("host-%d", hostId)
	logger.Infof("uuid is %s", uuid)
	resource, err := models.ResourceGet("uuid=?", uuid)
	logger.Infof("resource is %s", resource)
	if err != nil {
		return false, false, fmt.Errorf("failed to get resource: %v", err)
	}

	if resource == nil {
		// 未同步到RDB
		return false, false, nil
	}

	// 已同步，检查是否挂载到节点
	nodeIds, err := models.NodeIdsGetByResIds([]int64{resource.Id})
	logger.Infof("nodeIds is %s", nodeIds)
	if err != nil {
		return true, false, fmt.Errorf("failed to get node IDs: %v", err)
	}

	bindToNode = len(nodeIds) > 0
	return true, bindToNode, nil
}

// syncHostToRDB 同步主机信息到RDB
func syncHostToRDB(host *models.Host) error {
	// 使用现有的ResourceRegister函数来同步
	hosts := []models.Host{*host}
	return models.ResourceRegister(hosts, host.Tenant)
}

// publishHostUpdateEvent 发布主机更新事件到JumpServer
func publishHostUpdateEvent(host *models.Host) error {
	// 检查JumpServer事件发布是否启用
	if !rdbEvents.IsJSEnabled() {
		logger.Infof("JumpServer sync disabled, skipping host update event for host %d", host.Id)
		return nil
	}

	// 构建主机事件数据
	hostEventData := &jsEvents.HostEventData{
		ID:            host.Id,
		SN:            host.SN,
		IP:            host.IP,
		Ident:         host.Ident,
		Name:          host.Name,
		Note:          host.Note,
		OSVersion:     host.OSVersion,
		KernelVersion: host.KernelVersion,
		CPUModel:      host.CPUModel,
		CPU:           host.CPU,
		Mem:           host.Mem,
		Disk:          host.Disk,
		GPU:           host.GPU,
		GPUModel:      host.GPUModel,
		Model:         host.Model,
		Manufacturer:  host.Manufacturer,
		IDC:           host.IDC,
		Zone:          host.Zone,
		Rack:          host.Rack,
		Cate:          host.Cate,
		Tenant:        host.Tenant,
		Clock:         host.Clock,
	}

	// 获取JumpServer事件生产者
	producer := rdbEvents.GetJSEventProducer()
	if producer == nil {
		return fmt.Errorf("JumpServer event producer not initialized")
	}

	// 发布主机更新事件
	err := producer.PublishHostEvent(jsEvents.EventHostUpdate, hostEventData)
	if err != nil {
		return fmt.Errorf("failed to publish host update event: %v", err)
	}

	logger.Infof("Host update event published successfully for host %d", host.Id)
	return nil
}
