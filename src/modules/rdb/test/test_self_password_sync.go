package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"log"
)

// selfPasswordForm 个人密码修改表单
type selfPasswordForm struct {
	Username string `json:"username" binding:"required"`
	OldPass  string `json:"oldpass" binding:"required"`
	NewPass  string `json:"newpass" binding:"required"`
}

func main() {
	fmt.Println("=== 测试个人密码修改同步功能 ===")

	// 测试配置
	baseURL := "http://localhost:8080" // RDB API 地址
	testUsername := "testuser"         // 测试用户名
	oldPassword := "oldpass123"        // 旧密码
	newPassword := "newpass456"        // 新密码

	// 测试场景1：正常的密码修改
	fmt.Println("\n1. 测试正常的密码修改...")
	
	// 构造请求数据
	passwordForm := selfPasswordForm{
		Username: testUsername,
		OldPass:  oldPassword,
		NewPass:  newPassword,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(passwordForm)
	if err != nil {
		log.Fatalf("JSON编码失败: %v", err)
	}

	// 发送PUT请求到个人密码修改接口
	url := fmt.Sprintf("%s/api/rdb/self/password", baseURL)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Fatalf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	// 注意：实际使用时需要添加认证头，如 JWT token
	// req.Header.Set("Authorization", "Bearer your-jwt-token")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatalf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %s\n", resp.Status)
	fmt.Printf("响应内容: %s\n", string(body))

	// 分析响应结果
	if resp.StatusCode == 200 {
		fmt.Println("✅ 密码修改请求成功")
		fmt.Println("✅ 应该已触发 JumpServer 同步事件")
		
		// 解析响应JSON
		var response map[string]interface{}
		if err := json.Unmarshal(body, &response); err == nil {
			if err, exists := response["err"]; exists && err == "" {
				fmt.Println("✅ 密码修改成功，无错误")
			} else {
				fmt.Printf("❌ 密码修改失败: %v\n", err)
			}
		}
	} else {
		fmt.Printf("❌ 密码修改请求失败: %s\n", resp.Status)
		fmt.Printf("响应内容: %s\n", string(body))
	}

	// 测试场景2：错误的旧密码
	fmt.Println("\n2. 测试错误的旧密码...")
	
	wrongPasswordForm := selfPasswordForm{
		Username: testUsername,
		OldPass:  "wrongpassword",
		NewPass:  newPassword,
	}

	jsonData2, _ := json.Marshal(wrongPasswordForm)
	req2, _ := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData2))
	req2.Header.Set("Content-Type", "application/json")

	resp2, err := client.Do(req2)
	if err != nil {
		log.Printf("发送错误密码请求失败: %v", err)
	} else {
		defer resp2.Body.Close()
		body2, _ := ioutil.ReadAll(resp2.Body)
		
		fmt.Printf("响应状态: %s\n", resp2.Status)
		fmt.Printf("响应内容: %s\n", string(body2))
		
		if resp2.StatusCode != 200 {
			fmt.Println("✅ 正确拒绝了错误的旧密码")
		} else {
			fmt.Println("❌ 应该拒绝错误的旧密码")
		}
	}

	// 测试场景3：不存在的用户
	fmt.Println("\n3. 测试不存在的用户...")
	
	nonExistentUserForm := selfPasswordForm{
		Username: "nonexistentuser",
		OldPass:  oldPassword,
		NewPass:  newPassword,
	}

	jsonData3, _ := json.Marshal(nonExistentUserForm)
	req3, _ := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData3))
	req3.Header.Set("Content-Type", "application/json")

	resp3, err := client.Do(req3)
	if err != nil {
		log.Printf("发送不存在用户请求失败: %v", err)
	} else {
		defer resp3.Body.Close()
		body3, _ := ioutil.ReadAll(resp3.Body)
		
		fmt.Printf("响应状态: %s\n", resp3.Status)
		fmt.Printf("响应内容: %s\n", string(body3))
		
		if resp3.StatusCode != 200 {
			fmt.Println("✅ 正确拒绝了不存在的用户")
		} else {
			fmt.Println("❌ 应该拒绝不存在的用户")
		}
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n功能说明:")
	fmt.Println("1. ✅ 个人密码修改接口已增强")
	fmt.Println("2. ✅ 密码修改成功后会发布同步事件")
	fmt.Println("3. ✅ 事件包含密码变更和用户更新两种类型")
	fmt.Println("4. ✅ JumpServer 同步模块会处理这些事件")
	fmt.Println("5. ✅ 保持了原有的错误处理逻辑")
	
	fmt.Println("\n注意事项:")
	fmt.Println("- 需要配置正确的 RDB API 地址")
	fmt.Println("- 需要有效的用户认证（JWT token）")
	fmt.Println("- 需要确保 JumpServer 同步服务正在运行")
	fmt.Println("- 密码同步失败不会影响本地密码修改")
}
