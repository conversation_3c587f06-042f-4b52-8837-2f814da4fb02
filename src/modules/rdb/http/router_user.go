package http

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/str"

	"arboris/src/models"
	jsEvents "arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/rdb/auth"
	"arboris/src/modules/rdb/events"
)

// 用户列表响应结构
type UserListResponse struct {
	List  []models.User `json:"list"`
	Total int64         `json:"total"`
}

// @Summary 获取用户列表
// @Description 获取系统用户列表，支持分页和搜索
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Param conditions query string false "查询条件"
// @Param org query string false "组织"
// @Param ids query string false "用户ID列表，逗号分隔"
// @Success 200 {object} ApiResponse{dat=UserListResponse} "用户列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/users [get]
// @Summary 获取用户列表
// @Description 获取系统用户列表，支持分页和搜索
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量，默认20"
// @Param p query int false "页码，默认1"
// @Param query query string false "搜索关键词"
// @Param org query string false "组织筛选"
// @Param ids query string false "用户ID列表，逗号分隔"
// @Success 200 {object} ApiResponse{dat=object{list=[]models.User,total=int64}} "用户列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/users [get]
func userListGet(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")
	conditions := queryStr(c, "conditions", "")
	org := queryStr(c, "org", "")
	ids := str.IdsInt64(queryStr(c, "ids", ""))

	list, total, err := models.UserAndTotalGets(query, org, conditions, limit, offset(c, limit), ids)
	dangerous(err)

	for i := 0; i < len(list); i++ {
		list[i].UUID = ""
		auth.PrepareUser(&list[i])
	}

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取用户列表(V1接口)
// @Description 获取用户列表，支持分页、搜索、组织筛选
// @Tags V1接口
// @Accept json
// @Produce json
// @Security ServiceAuth
// @Param limit query int false "每页数量" default(20)
// @Param query query string false "搜索关键词"
// @Param org query string false "组织筛选"
// @Param ids query string false "用户ID列表，逗号分隔"
// @Success 200 {object} ApiResponse{dat=object{list=[]models.User,total=int64}} "用户列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /v1/rdb/users [get]
func v1UserListGet(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")
	org := queryStr(c, "org", "")
	ids := str.IdsInt64(queryStr(c, "ids", ""))

	list, total, err := models.UserAndTotalGets(query, org, "", limit, offset(c, limit), ids)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, err)
}

type userProfileForm struct {
	Username     string `json:"username"`
	Password     string `json:"password"`
	Dispname     string `json:"dispname"`
	Phone        string `json:"phone"`
	Email        string `json:"email"`
	Im           string `json:"im"`
	IsRoot       int    `json:"is_root"`
	LeaderId     int64  `json:"leader_id"`
	Typ          int    `json:"typ"`    // 用户类型：0-长期账号，1-临时账号
	Status       int    `json:"status"` // 用户状态：0-激活，1-未激活，2-锁定，3-冻结，4-注销
	Organization string `json:"organization"`
	ActiveBegin  int64  `json:"active_begin"` // 临时账号开始时间（Unix时间戳）
	ActiveEnd    int64  `json:"active_end"`   // 临时账号结束时间（Unix时间戳）
}

// @Summary 创建用户
// @Description 超级管理员创建新用户，支持创建长期账号和临时账号
// @Description 用户类型(typ): 0-长期账号, 1-临时账号
// @Description 用户状态(status): 0-激活, 1-未激活, 2-锁定, 3-冻结, 4-注销
// @Description 临时账号需要设置active_begin和active_end字段
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param user body userProfileForm true "用户信息"
// @Success 200 {object} ApiResponse "创建成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/users [post]
func userAddPost(c *gin.Context) {
	root := loginRoot(c)

	var f userProfileForm
	dangerous(c.ShouldBind(&f))
	dangerous(auth.CheckPassword(f.Password))

	// 验证临时用户的时间设置
	if f.Typ == 1 { // 临时账号
		if f.ActiveBegin <= 0 || f.ActiveEnd <= 0 {
			dangerous(fmt.Errorf("临时账号必须设置有效的开始时间和结束时间"))
		}
		if f.ActiveBegin >= f.ActiveEnd {
			dangerous(fmt.Errorf("临时账号的开始时间必须早于结束时间"))
		}
		if f.ActiveEnd <= time.Now().Unix() {
			dangerous(fmt.Errorf("临时账号的结束时间必须晚于当前时间"))
		}
	} else {
		f.ActiveBegin = 0
		f.ActiveEnd = 0
	}

	pass, err := models.CryptoPass(f.Password)
	dangerous(err)

	now := time.Now().Unix()
	b, _ := json.Marshal([]string{pass})
	u := models.User{
		Username:     f.Username,
		Password:     pass,
		Passwords:    string(b),
		Dispname:     f.Dispname,
		Phone:        f.Phone,
		Email:        f.Email,
		Im:           f.Im,
		IsRoot:       f.IsRoot,
		LeaderId:     f.LeaderId,
		Organization: f.Organization,
		Type:         f.Typ,         // 设置用户类型
		Status:       f.Status,      // 设置用户状态
		ActiveBegin:  f.ActiveBegin, // 设置临时账号开始时间
		ActiveEnd:    f.ActiveEnd,   // 设置临时账号结束时间
		UpdatedAt:    now,
		UUID:         models.GenUUIDForUser(f.Username),
	}

	if f.LeaderId != 0 {
		u.LeaderName = User(f.LeaderId).Username
	}

	err = u.Save()
	if err == nil {
		// 记录操作日志，包含用户类型信息
		userTypeDesc := "长期账号"
		if f.Typ == 1 {
			userTypeDesc = fmt.Sprintf("临时账号(有效期: %s - %s)",
				time.Unix(f.ActiveBegin, 0).Format("2006-01-02 15:04:05"),
				time.Unix(f.ActiveEnd, 0).Format("2006-01-02 15:04:05"))
		}
		go models.OperationLogNew(root.Username, "user", u.Id,
			fmt.Sprintf("UserCreate %s is_root? %v type: %s", u.Username, f.IsRoot == 1, userTypeDesc))

		// 发布用户创建事件到JumpServer同步
		go func() {
			userData := &jsEvents.UserEventData{
				ID:           u.Id,
				UUID:         u.UUID,
				Username:     u.Username,
				Password:     f.Password, // 使用明文密码
				Dispname:     u.Dispname,
				Email:        u.Email,
				Phone:        u.Phone,
				Organization: u.Organization,
				Status:       u.Status, // 使用实际状态
				IsRoot:       u.IsRoot,
				Type:         u.Type,        // 使用实际用户类型
				ActiveBegin:  u.ActiveBegin, // 使用实际开始时间
				ActiveEnd:    u.ActiveEnd,   // 使用实际结束时间
			}
			events.PublishUserCreateEvent(userData)
		}()
	}

	renderMessage(c, err)
}

// @Summary 获取用户详情
// @Description 超级管理员获取指定用户的详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "用户ID"
// @Success 200 {object} ApiResponse{dat=models.User} "用户详情"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/user/{id}/profile [get]
func userProfileGet(c *gin.Context) {
	user := User(urlParamInt64(c, "id"))
	user.UUID = ""

	auth.PrepareUser(user)

	renderData(c, user, nil)
}

// @Summary 更新用户信息
// @Description 超级管理员更新指定用户的信息，支持更新临时账号的有效期
// @Description 用户类型(typ): 0-长期账号, 1-临时账号
// @Description 用户状态(status): 0-激活, 1-未激活, 2-锁定, 3-冻结, 4-注销
// @Description 临时账号需要设置active_begin和active_end字段
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "用户ID"
// @Param user body userProfileForm true "用户信息"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/user/{id}/profile [put]
func userProfilePut(c *gin.Context) {
	root := loginRoot(c)

	var f userProfileForm
	bind(c, &f)

	arr := make([]string, 0, 5)

	target := User(urlParamInt64(c, "id"))

	if f.LeaderId != target.LeaderId {
		target.LeaderId = f.LeaderId
		if f.LeaderId == 0 {
			target.LeaderName = ""
		} else {
			leader := User(f.LeaderId)
			target.LeaderName = leader.Username
		}
	}

	if f.Dispname != target.Dispname {
		arr = append(arr, fmt.Sprintf("dispname: %s -> %s", target.Dispname, f.Dispname))
		target.Dispname = f.Dispname
	}

	if f.Phone != target.Phone {
		arr = append(arr, fmt.Sprintf("phone: %s -> %s", target.Phone, f.Phone))
		target.Phone = f.Phone
	}

	if f.Email != target.Email {
		arr = append(arr, fmt.Sprintf("email: %s -> %s", target.Email, f.Email))
		target.Email = f.Email
	}

	if f.Im != target.Im {
		arr = append(arr, fmt.Sprintf("im: %s -> %s", target.Im, f.Im))
		target.Im = f.Im
	}

	if f.IsRoot != target.IsRoot {
		arr = append(arr, fmt.Sprintf("is_root? %v -> %v", target.IsRoot == 1, f.IsRoot == 1))
		target.IsRoot = f.IsRoot
	}

	if f.Typ != target.Type {
		arr = append(arr, fmt.Sprintf("typ: %d -> %d", target.Type, f.Typ))
		target.Type = f.Typ
	}

	if f.Status != target.Status {
		arr = append(arr, fmt.Sprintf("typ: %d -> %d", target.Status, f.Status))
		target.Status = f.Status
		if target.Status == models.USER_S_ACTIVE {
			target.LoginErrNum = 0
		}
	}

	if f.Organization != target.Organization {
		arr = append(arr, fmt.Sprintf("organization: %s -> %s", target.Organization, f.Organization))
		target.Organization = f.Organization
	}

	// 处理临时用户的时间字段
	if f.ActiveBegin != target.ActiveBegin {
		oldTime := "未设置"
		newTime := "未设置"
		if target.ActiveBegin > 0 {
			oldTime = time.Unix(target.ActiveBegin, 0).Format("2006-01-02 15:04:05")
		}
		if f.ActiveBegin > 0 {
			newTime = time.Unix(f.ActiveBegin, 0).Format("2006-01-02 15:04:05")
		}
		arr = append(arr, fmt.Sprintf("active_begin: %s -> %s", oldTime, newTime))
		target.ActiveBegin = f.ActiveBegin
	}

	if f.ActiveEnd != target.ActiveEnd {
		oldTime := "未设置"
		newTime := "未设置"
		if target.ActiveEnd > 0 {
			oldTime = time.Unix(target.ActiveEnd, 0).Format("2006-01-02 15:04:05")
		}
		if f.ActiveEnd > 0 {
			newTime = time.Unix(f.ActiveEnd, 0).Format("2006-01-02 15:04:05")
		}
		arr = append(arr, fmt.Sprintf("active_end: %s -> %s", oldTime, newTime))
		target.ActiveEnd = f.ActiveEnd
	}

	// 验证临时用户的时间设置
	if f.Typ == 1 { // 临时账号
		if f.ActiveBegin <= 0 || f.ActiveEnd <= 0 {
			dangerous(fmt.Errorf("临时账号必须设置有效的开始时间和结束时间"))
		}
		if f.ActiveBegin >= f.ActiveEnd {
			dangerous(fmt.Errorf("临时账号的开始时间必须早于结束时间"))
		}
	}

	target.UpdatedAt = time.Now().Unix()

	err := target.Update("dispname", "phone", "email", "im", "is_root", "leader_id", "leader_name", "typ", "status", "organization", "active_begin", "active_end", "login_err_num", "updated_at")
	if err == nil && len(arr) > 0 {
		content := strings.Join(arr, "，")
		go models.OperationLogNew(root.Username, "user", target.Id, fmt.Sprintf("UserModify %s %s", target.Username, content))

		// 发布用户更新事件到JumpServer同步
		go func() {
			userData := &jsEvents.UserEventData{
				ID:           target.Id,
				UUID:         target.UUID,
				Username:     target.Username,
				Password:     "", // 更新时不包含密码
				Dispname:     target.Dispname,
				Email:        target.Email,
				Phone:        target.Phone,
				Organization: target.Organization,
				Status:       target.Status,
				IsRoot:       target.IsRoot,
				Type:         target.Type,
				ActiveBegin:  target.ActiveBegin,
				ActiveEnd:    target.ActiveEnd,
			}
			events.PublishUserUpdateEvent(userData)
		}()
	}

	renderMessage(c, err)
}

type userPasswordForm struct {
	Password string `json:"password" binding:"required"`
}

// @Summary 重置用户密码
// @Description 超级管理员重置指定用户的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "用户ID"
// @Param password body userPasswordForm true "新密码"
// @Success 200 {object} ApiResponse "密码重置成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/user/{id}/password [put]
func userPasswordPut(c *gin.Context) {
	root := loginRoot(c)

	var f userPasswordForm
	bind(c, &f)
	dangerous(auth.CheckPassword(f.Password))

	user := User(urlParamInt64(c, "id"))
	err := auth.ChangePassword(user, f.Password)

	if err == nil {
		go models.OperationLogNew(root.Username, "user", user.Id, fmt.Sprintf("UserChangePassword %s", user.Username))

		// 发布密码变更事件到JumpServer同步
		go func() {
			passwordData := &jsEvents.PasswordChangeEventData{
				UserID:      user.Id,
				Username:    user.Username,
				NewPassword: f.Password, // 使用明文密码
			}
			events.PublishPasswordChangeEvent(passwordData)
		}()
	}

	renderMessage(c, err)
}

// @Summary 删除用户
// @Description 超级管理员删除指定用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "用户ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/user/{id} [delete]
func userDel(c *gin.Context) {
	root := loginRoot(c)

	id := urlParamInt64(c, "id")
	target, err := models.UserGet("id=?", id)
	dangerous(err)

	if target == nil {
		renderMessage(c, nil)
		return
	}

	if target.Username == "root" {
		bomb("cannot delete root user")
	}

	err = target.Del()
	if err == nil {
		go models.OperationLogNew(root.Username, "user", target.Id, fmt.Sprintf("UserDelete %s", target.Username))

		// 发布用户删除事件到JumpServer同步
		go func() {
			userData := &jsEvents.UserEventData{
				ID:           target.Id,
				UUID:         target.UUID,
				Username:     target.Username,
				Password:     "", // 删除时不包含密码
				Dispname:     target.Dispname,
				Email:        target.Email,
				Phone:        target.Phone,
				Organization: target.Organization,
				Status:       target.Status,
				IsRoot:       target.IsRoot,
				Type:         target.Type,
				ActiveBegin:  target.ActiveBegin,
				ActiveEnd:    target.ActiveEnd,
			}
			events.PublishUserDeleteEvent(userData)
		}()
	}

	renderMessage(c, err)
}

// @Summary 启用用户
// @Description 超级管理员启用指定用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "用户ID"
// @Success 200 {object} ApiResponse "启用成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/user/{id}/enable [put]
func userEnable(c *gin.Context) {
	root := loginRoot(c)

	id := urlParamInt64(c, "id")
	target, err := models.UserGet("id=?", id)
	dangerous(err)

	if target == nil {
		bomb("user not found")
	}

	if target.Username == "root" {
		bomb("cannot modify root user status")
	}

	// 更新用户状态为启用
	target.Status = models.USER_S_ACTIVE
	target.UpdatedAt = time.Now().Unix()

	err = target.Update("status", "updated_at")
	if err == nil {
		go models.OperationLogNew(root.Username, "user", target.Id, fmt.Sprintf("UserEnable %s", target.Username))

		// 发布用户启用事件到JumpServer同步
		go func() {
			userData := &jsEvents.UserEventData{
				ID:           target.Id,
				UUID:         target.UUID,
				Username:     target.Username,
				Password:     "", // 状态变更时不包含密码
				Dispname:     target.Dispname,
				Email:        target.Email,
				Phone:        target.Phone,
				Organization: target.Organization,
				Status:       target.Status,
				IsRoot:       target.IsRoot,
				Type:         target.Type,
				ActiveBegin:  target.ActiveBegin,
				ActiveEnd:    target.ActiveEnd,
			}
			events.PublishUserEnableEvent(userData)
		}()
	}

	renderMessage(c, err)
}

// @Summary 禁用用户
// @Description 超级管理员禁用指定用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "用户ID"
// @Success 200 {object} ApiResponse "禁用成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/user/{id}/disable [put]
func userDisable(c *gin.Context) {
	root := loginRoot(c)

	id := urlParamInt64(c, "id")
	target, err := models.UserGet("id=?", id)
	dangerous(err)

	if target == nil {
		bomb("user not found")
	}

	if target.Username == "root" {
		bomb("cannot modify root user status")
	}

	// 更新用户状态为禁用
	target.Status = models.USER_S_INACTIVE
	target.UpdatedAt = time.Now().Unix()

	err = target.Update("status", "updated_at")
	if err == nil {
		go models.OperationLogNew(root.Username, "user", target.Id, fmt.Sprintf("UserDisable %s", target.Username))

		// 发布用户禁用事件到JumpServer同步
		go func() {
			userData := &jsEvents.UserEventData{
				ID:           target.Id,
				UUID:         target.UUID,
				Username:     target.Username,
				Password:     "", // 状态变更时不包含密码
				Dispname:     target.Dispname,
				Email:        target.Email,
				Phone:        target.Phone,
				Organization: target.Organization,
				Status:       target.Status,
				IsRoot:       target.IsRoot,
				Type:         target.Type,
				ActiveBegin:  target.ActiveBegin,
				ActiveEnd:    target.ActiveEnd,
			}
			events.PublishUserDisableEvent(userData)
		}()
	}

	renderMessage(c, err)
}

// @Summary 根据UUID获取用户(V1接口)
// @Description 根据用户UUID获取用户详细信息
// @Tags V1接口
// @Accept json
// @Produce json
// @Security ServiceAuth
// @Param uuid query string true "用户UUID"
// @Success 200 {object} ApiResponse{dat=models.User} "用户信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /v1/rdb/get-user-by-uuid [get]
func v1UserGetByUUID(c *gin.Context) {
	user, err := models.UserGet("uuid=?", queryStr(c, "uuid"))
	dangerous(err)

	if user == nil {
		renderMessage(c, "user not found")
		return
	}

	renderData(c, user, nil)
}

// @Summary 批量根据UUID获取用户(V1接口)
// @Description 根据用户UUID列表批量获取用户信息
// @Tags V1接口
// @Accept json
// @Produce json
// @Security ServiceAuth
// @Param uuids query string true "用户UUID列表，逗号分隔"
// @Success 200 {object} ApiResponse{dat=[]models.User} "用户列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /v1/rdb/get-users-by-uuids [get]
func v1UserGetByUUIDs(c *gin.Context) {
	uuids := strings.Split(queryStr(c, "uuids"), ",")
	users, err := models.UserGetByUUIDs(uuids)
	renderData(c, users, err)
}

func v1UserGetByIds(c *gin.Context) {
	ids := queryStr(c, "ids")
	users, err := models.UserGetByIds(str.IdsInt64(ids))
	renderData(c, users, err)
}

func v1UserGetByNames(c *gin.Context) {
	names := strings.Split(queryStr(c, "names"), ",")
	users, err := models.UserGetByNames(names)
	renderData(c, users, err)
}

func v1UserGetByToken(c *gin.Context) {
	ut, err := models.UserTokenGet("token=?", queryStr(c, "token"))
	dangerous(err)

	if ut == nil {
		renderMessage(c, "token not found")
		return
	}

	user, err := models.UserGet("id=?", ut.UserId)
	dangerous(err)

	if user == nil {
		renderMessage(c, "user not found")
		return
	}

	renderData(c, user, nil)
}

// @Summary 生成用户邀请Token
// @Description 生成用户注册邀请Token，用于用户自助注册
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=string} "邀请Token"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/users/invite [get]
func userInviteGet(c *gin.Context) {
	token, err := models.CryptoPass(fmt.Sprint(time.Now().UnixNano()))
	dangerous(err)

	err = models.InviteNew(token, loginUsername(c))
	renderData(c, token, err)
}

type userInviteForm struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Dispname string `json:"dispname"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	Im       string `json:"im"`
	Token    string `json:"token" binding:"required"`
}

// @Summary 通过邀请Token注册用户
// @Description 使用邀请Token进行用户自助注册
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body userInviteForm true "用户注册信息"
// @Success 200 {object} ApiResponse "注册成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/users/invite [post]
func userInvitePost(c *gin.Context) {
	var f userInviteForm
	bind(c, &f)

	err := func() error {
		if err := auth.CheckPassword(f.Password); err != nil {
			return err
		}

		inv, err := models.InviteGet("token=?", f.Token)
		if err != nil {
			return err
		}

		if inv.Expire < time.Now().Unix() {
			return _e("invite url already expired")
		}

		u := models.User{
			Username: f.Username,
			Dispname: f.Dispname,
			Phone:    f.Phone,
			Email:    f.Email,
			Im:       f.Im,
			UUID:     models.GenUUIDForUser(f.Username),
		}

		u.Password, err = models.CryptoPass(f.Password)
		if err != nil {
			return err
		}
		if err = u.Save(); err != nil {
			return err
		}

		return inv.Del()
	}()

	renderMessage(c, err)
}

func v1UserIdsGetByTeamIds(c *gin.Context) {
	ids := queryStr(c, "ids")
	userIds, err := models.UserIdsByTeamIds(str.IdsInt64(ids))
	renderData(c, userIds, err)
}
