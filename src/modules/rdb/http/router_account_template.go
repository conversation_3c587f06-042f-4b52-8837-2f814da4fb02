package http

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
	"arboris/src/modules/jumpserver-sync/events"
	rdbEvents "arboris/src/modules/rdb/events"
)

// AccountTemplateListResponse 账号模板列表响应结构
type AccountTemplateListResponse struct {
	List  []models.AccountTemplate `json:"list"`
	Total int64                    `json:"total"`
}

// AccountTemplateAdd @Summary 创建账号模板
// @Description 创建账号模板，用于批量创建账号
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param template body models.AccountTemplate true "账号模板信息"
// @Success 200 {object} ApiResponse{dat=models.AccountTemplate} "创建的账号模板"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/account-templates [post]
func accountTemplateAdd(c *gin.Context) {
	var req models.AccountTemplate
	bind(c, &req)

	// 设置创建者
	req.Creator = loginUser(c).Username

	// 验证数据
	dangerous(req.Validate())

	// 检查名称是否已存在
	exists, err := models.AccountTemplateGetByName(req.Name)
	dangerous(err)
	if exists != nil {
		bomb("账号模板名称已存在")
	}

	// 保存到数据库
	dangerous(req.Save())

	// 发布创建事件
	publishAccountTemplateEvent(events.EventAccountTemplateCreate, &req)

	renderData(c, req, nil)
}

// @Summary 获取账号模板列表
// @Description 获取账号模板列表，支持分页和按名称查询。name参数支持逗号分隔的多个名称进行批量查询
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param name query string false "模板名称，支持逗号分隔的多个名称，如: name=模板1,模板2,模板3"
// @Success 200 {object} ApiResponse{dat=AccountTemplateListResponse} "账号模板列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/account-templates [get]
func accountTemplateGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	offset := queryInt(c, "offset", 0)
	nameParam := queryStr(c, "name", "")

	where := ""
	args := []interface{}{}

	if nameParam != "" {
		// 支持逗号分隔的多个名称
		names := strings.Split(nameParam, ",")
		var trimmedNames []string
		for _, name := range names {
			trimmed := strings.TrimSpace(name)
			if trimmed != "" {
				trimmedNames = append(trimmedNames, trimmed)
			}
		}

		if len(trimmedNames) > 0 {
			placeholders := make([]string, len(trimmedNames))
			for i := range placeholders {
				placeholders[i] = "?"
				args = append(args, trimmedNames[i])
			}
			where = "name IN (" + strings.Join(placeholders, ",") + ")"
		}
	}

	total, err := models.AccountTemplateTotal(where, args...)
	dangerous(err)

	list, err := models.AccountTemplateGetsWithPaging(where, limit, offset, args...)
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取账号模板详情
// @Description 根据ID获取账号模板详情
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号模板ID"
// @Success 200 {object} ApiResponse{dat=models.AccountTemplate} "账号模板详情"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号模板不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/account-templates/{id} [get]
func accountTemplateGet(c *gin.Context) {
	id := urlParamInt64(c, "id")

	template, err := models.AccountTemplateGetById(id)
	dangerous(err)

	if template == nil {
		bomb("账号模板不存在")
	}

	renderData(c, template, nil)
}

// @Summary 更新账号模板
// @Description 更新账号模板信息
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号模板ID"
// @Param template body models.AccountTemplate true "账号模板信息"
// @Success 200 {object} ApiResponse{dat=models.AccountTemplate} "更新后的账号模板"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号模板不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/account-templates/{id} [put]
func accountTemplateUpdate(c *gin.Context) {
	id := urlParamInt64(c, "id")

	template, err := models.AccountTemplateGetById(id)
	dangerous(err)

	if template == nil {
		bomb("账号模板不存在")
	}

	var req models.AccountTemplate
	bind(c, &req)

	// 保持原有的ID和创建者
	req.Id = template.Id
	req.Creator = template.Creator

	// 验证数据
	dangerous(req.Validate())

	// 检查名称是否与其他模板冲突
	if req.Name != template.Name {
		exists, err := models.AccountTemplateGetByName(req.Name)
		dangerous(err)
		if exists != nil && exists.Id != template.Id {
			bomb("账号模板名称已存在")
		}
	}

	// 更新数据库
	dangerous(req.Update())

	// 发布更新事件，包含原始模板名称
	publishAccountTemplateUpdateEvent(template, &req)

	renderData(c, req, nil)
}

// @Summary 删除账号模板
// @Description 删除账号模板，删除前会检查是否有账号在使用
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号模板ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 400 {object} ApiResponse "参数错误或有账号正在使用"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号模板不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/account-templates/{id} [delete]
func accountTemplateDelete(c *gin.Context) {
	id := urlParamInt64(c, "id")

	template, err := models.AccountTemplateGetById(id)
	dangerous(err)

	if template == nil {
		bomb("账号模板不存在")
	}

	if template != nil {
		// 检查是否有账号正在使用此模板
		accounts, err := models.AccountGets("template_id=?", template.Id)
		dangerous(err)

		if len(accounts) > 0 {
			bomb("无法删除：还有 %d 个账号正在使用此模板", len(accounts))
		}
	}
	// 删除数据库记录
	dangerous(template.Delete())

	// 发布删除事件
	publishAccountTemplateEvent(events.EventAccountTemplateDelete, template)

	renderMessage(c, "删除成功")
}

// publishAccountTemplateEvent 发布账号模板事件
func publishAccountTemplateEvent(eventType events.EventType, template *models.AccountTemplate) {
	eventData := &events.AccountTemplateEventData{
		ID:             template.Id,
		Name:           template.Name,
		Username:       template.Username,
		SecretType:     template.SecretType,
		Secret:         template.Secret,
		Passphrase:     template.Passphrase,
		SecretStrategy: template.SecretStrategy,
		SuFrom:         template.SuFrom,
		Privileged:     template.Privileged,
		IsActive:       template.IsActive,
		AutoPush:       template.AutoPush,
		Comment:        template.Comment,
		Creator:        template.Creator,
		CreatedAt:      template.CreatedAt.Unix(),
		UpdatedAt:      template.UpdatedAt.Unix(),
	}

	// 使用JumpServer同步事件生产者发布事件
	producer := rdbEvents.GetJSEventProducer()
	if producer != nil {
		if err := producer.PublishAccountTemplateEvent(eventType, eventData); err != nil {
			logger.Errorf("发布账号模板事件失败: %v", err)
		}
	} else {
		logger.Warning("JumpServer事件生产者未初始化")
	}
}

// publishAccountTemplateUpdateEvent 发布账号模板更新事件，包含原始模板信息
func publishAccountTemplateUpdateEvent(originalTemplate, updatedTemplate *models.AccountTemplate) {
	eventData := &events.AccountTemplateEventData{
		ID:             updatedTemplate.Id,
		Name:           updatedTemplate.Name,
		Username:       updatedTemplate.Username,
		SecretType:     updatedTemplate.SecretType,
		Secret:         updatedTemplate.Secret,
		Passphrase:     updatedTemplate.Passphrase,
		SecretStrategy: updatedTemplate.SecretStrategy,
		SuFrom:         updatedTemplate.SuFrom,
		Privileged:     updatedTemplate.Privileged,
		IsActive:       updatedTemplate.IsActive,
		AutoPush:       updatedTemplate.AutoPush,
		Comment:        updatedTemplate.Comment,
		Creator:        updatedTemplate.Creator,
		CreatedAt:      updatedTemplate.CreatedAt.Unix(),
		UpdatedAt:      updatedTemplate.UpdatedAt.Unix(),
		// 添加原始模板名称用于查找
		OriginalName:   originalTemplate.Name,
	}

	// 使用JumpServer同步事件生产者发布事件
	producer := rdbEvents.GetJSEventProducer()
	if producer != nil {
		if err := producer.PublishAccountTemplateEvent(events.EventAccountTemplateUpdate, eventData); err != nil {
			logger.Errorf("发布账号模板更新事件失败: %v", err)
		}
	} else {
		logger.Warning("JumpServer事件生产者未初始化")
	}
}
