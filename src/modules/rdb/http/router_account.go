package http

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
	"arboris/src/modules/jumpserver-sync/events"
	rdbEvents "arboris/src/modules/rdb/events"
)

// AccountListResponse 账号列表响应结构
type AccountListResponse struct {
	List  []models.Account `json:"list"`
	Total int64            `json:"total"`
}

// AccountCreateRequest 账号创建请求结构（支持单个和批量）
type AccountCreateRequest struct {
	Name        string  `json:"name" binding:"required"`
	Username    string  `json:"username" binding:"required"`
	SecretType  string  `json:"secret_type"`
	Secret      string  `json:"secret,omitempty"`
	Passphrase  string  `json:"passphrase,omitempty"`
	ResourceIds []int64 `json:"resource_ids" binding:"required,min=1"` // 统一使用resource.id
	SuFrom      int64   `json:"su_from"`
	Privileged  bool    `json:"privileged"`
	IsActive    bool    `json:"is_active"`
	SecretReset bool    `json:"secret_reset"`
	PushNow     bool    `json:"push_now"`
	OnInvalid   string  `json:"on_invalid"`
	Comment     string  `json:"comment"`
}

// AccountCreateResponse 账号创建响应结构
type AccountCreateResponse struct {
	Account *models.Account       `json:"account"`
	Results []AccountCreateResult `json:"results"`
}

// AccountCreateResult 账号创建结果
type AccountCreateResult struct {
	ResourceId int64  `json:"resource_id"` // 资源ID
	State      string `json:"state"`       // success, error
	Error      string `json:"error,omitempty"`
}

// @Summary 创建账号
// @Description 直接创建账号，支持单个或批量创建。如需使用模板创建账号，请使用模板应用接口。可以指定template_id使用模板创建，模板中的配置会作为默认值
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param account body AccountCreateRequest true "账号信息"
// @Success 200 {object} ApiResponse{dat=AccountCreateResponse} "创建结果"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts [post]
func accountAdd(c *gin.Context) {
	var req AccountCreateRequest
	bind(c, &req)

	creator := loginUser(c).Username
	response := AccountCreateResponse{
		Results: []AccountCreateResult{},
	}

	// 获取资源列表
	resources, err := getResourcesByIds(req.ResourceIds)
	if err != nil {
		bomb(err.Error())
	}

	// 检查是否存在重复的账号（在任何一个资源上）
	validResourceIds := []int64{}
	for _, resource := range resources {
		// 通过绑定关系表检查是否存在重复账号
		bindings, err := models.AccountResourceBindingGets("resource_id=?", resource.Id)
		if err != nil {
			response.Results = append(response.Results, AccountCreateResult{
				ResourceId: resource.Id,
				State:      "error",
				Error:      "检查账号重复失败: " + err.Error(),
			})
			continue
		}

		// 检查是否有相同用户名的账号
		hasConflict := false
		for _, binding := range bindings {
			account, err := models.AccountGetById(binding.AccountId)
			if err != nil {
				continue
			}
			if account != nil && account.Username == req.Username {
				response.Results = append(response.Results, AccountCreateResult{
					ResourceId: resource.Id,
					State:      "error",
					Error:      "该资源上已存在相同用户名的账号",
				})
				hasConflict = true
				break
			}
		}

		if !hasConflict {
			validResourceIds = append(validResourceIds, resource.Id)
		}
	}

	// 如果没有有效资源，直接返回
	if len(validResourceIds) == 0 {
		renderData(c, response, nil)
		return
	}

	// 创建账号
	account := models.Account{
		Name:        req.Name,
		Username:    req.Username,
		SecretType:  req.SecretType,
		Secret:      req.Secret,
		Passphrase:  req.Passphrase,
		SuFrom:      req.SuFrom,
		Privileged:  req.Privileged,
		IsActive:    req.IsActive,
		SecretReset: req.SecretReset,
		PushNow:     req.PushNow,
		OnInvalid:   req.OnInvalid,
		Comment:     req.Comment,
		Creator:     creator,
	}

	// 设置默认值
	if account.SecretType == "" {
		account.SecretType = "password"
	}
	if account.OnInvalid == "" {
		account.OnInvalid = "error"
	}
	if account.Source == "" {
		account.Source = "local"
	}

	// 验证账号数据
	dangerous(account.Validate())

	// 保存账号到数据库
	dangerous(account.Save())

	// 为每个有效资源创建绑定关系
	resourceMap := make(map[int64]*models.Resource)
	for _, resource := range resources {
		resourceMap[resource.Id] = &resource
	}

	for _, resourceId := range validResourceIds {
		resource := resourceMap[resourceId]

		binding := models.AccountResourceBinding{
			AccountId:   account.Id,
			ResourceId:  resource.Id,
			BindingType: "manual",
			IsActive:    true,
			Creator:     creator,
		}

		if err := binding.Save(); err != nil {
			response.Results = append(response.Results, AccountCreateResult{
				ResourceId: resourceId,
				State:      "error",
				Error:      "创建绑定关系失败: " + err.Error(),
			})
		} else {
			// 发布账号绑定事件
			if err := publishAccountBindEvent(&binding); err != nil {
				logger.Errorf("Failed to publish account bind event: account_id=%d, resource_id=%d, error=%v",
					account.Id, resourceId, err)
				// 不中断流程，只记录错误
			}

			response.Results = append(response.Results, AccountCreateResult{
				ResourceId: resourceId,
				State:      "success",
			})
		}
	}

	// 发布创建事件
	publishAccountEvent(events.EventAccountCreate, &account)

	// 设置响应中的账号信息
	response.Account = &account

	renderData(c, response, nil)
}

// getResourcesByIds 根据资源ID列表获取资源
func getResourcesByIds(resourceIds []int64) ([]models.Resource, error) {
	if len(resourceIds) == 0 {
		return nil, fmt.Errorf("资源ID列表不能为空")
	}

	var resources []models.Resource
	for _, id := range resourceIds {
		resource, err := models.ResourceGet("id=?", id)
		if err != nil {
			return nil, fmt.Errorf("查询资源失败 (id=%d): %v", id, err)
		}
		if resource == nil {
			return nil, fmt.Errorf("资源不存在 (id=%d)", id)
		}
		resources = append(resources, *resource)
	}

	return resources, nil
}

// @Summary 获取账号列表
// @Description 获取账号列表，支持分页和搜索
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param search query string false "搜索关键字"
// @Param resource_id query int false "资源ID"
// @Param template_id query int false "模板ID"
// @Success 200 {object} ApiResponse{dat=AccountListResponse} "账号列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts [get]
func accountGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	offset := queryInt(c, "offset", 0)
	search := queryStr(c, "search", "")
	resourceIdStr := queryStr(c, "resource_id", "")
	templateId := queryInt64(c, "template_id", 0)

	where := ""
	args := []interface{}{}

	conditions := []string{}
	if search != "" {
		conditions = append(conditions, "(name LIKE ? OR username LIKE ? OR comment LIKE ?)")
		searchPattern := "%" + search + "%"
		args = append(args, searchPattern, searchPattern, searchPattern)
	}

	// 如果指定了资源ID，需要通过绑定关系表查询
	if resourceIdStr != "" {
		resourceId := urlParamInt64FromStr(resourceIdStr)

		// 验证资源是否存在
		resource, err := models.ResourceGet("id=?", resourceId)
		dangerous(err)
		if resource == nil {
			bomb(fmt.Sprintf("资源不存在 (id=%d)", resourceId))
		}

		// 通过绑定关系表获取账号ID列表
		bindings, err := models.AccountResourceBindingGets("resource_id=? AND is_active=1", resource.Id)
		dangerous(err)

		var accountIds []int64
		for _, binding := range bindings {
			accountIds = append(accountIds, binding.AccountId)
		}

		if len(accountIds) == 0 {
			// 没有找到绑定的账号
			renderData(c, gin.H{
				"list":  []models.Account{},
				"total": 0,
			}, nil)
			return
		}

		// 添加账号ID条件
		conditions = append(conditions, "id IN (?)")
		args = append(args, accountIds)
	}

	if templateId > 0 {
		conditions = append(conditions, "template_id = ?")
		args = append(args, templateId)
	}

	if len(conditions) > 0 {
		where = strings.Join(conditions, " AND ")
	}

	total, err := models.AccountTotal(where, args...)
	dangerous(err)

	list, err := models.AccountGetsWithPaging(where, limit, offset, args...)
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取账号详情
// @Description 根据ID获取账号详情
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号ID"
// @Success 200 {object} ApiResponse{dat=models.Account} "账号详情"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/{id} [get]
func accountGet(c *gin.Context) {
	id := urlParamInt64(c, "id")

	account, err := models.AccountGetById(id)
	dangerous(err)

	if account == nil {
		bomb("账号不存在")
	}

	renderData(c, account, nil)
}

// AccountUpdateRequest 账号更新请求结构
type AccountUpdateRequest struct {
	Name        string `json:"name"`
	SecretType  string `json:"secret_type"`
	Secret      string `json:"secret,omitempty"`
	Passphrase  string `json:"passphrase,omitempty"`
	SuFrom      int64  `json:"su_from"`
	Privileged  bool   `json:"privileged"`
	IsActive    bool   `json:"is_active"`
	SecretReset bool   `json:"secret_reset"`
	PushNow     bool   `json:"push_now"`
	OnInvalid   string `json:"on_invalid"`
	Comment     string `json:"comment"`
}

// @Summary 更新账号
// @Description 更新账号信息（不包括用户名和资产绑定）
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号ID"
// @Param account body AccountUpdateRequest true "账号信息"
// @Success 200 {object} ApiResponse{dat=models.Account} "更新后的账号"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/{id} [put]
func accountUpdate(c *gin.Context) {
	id := urlParamInt64(c, "id")

	account, err := models.AccountGetById(id)
	dangerous(err)

	if account == nil {
		bomb("账号不存在")
	}

	// 保存原始账号信息用于事件发布
	originalAccount := *account

	var req AccountUpdateRequest
	bind(c, &req)

	// 更新账号字段
	if req.Name != "" {
		account.Name = req.Name
	}
	if req.SecretType != "" {
		account.SecretType = req.SecretType
	}
	if req.Secret != "" {
		account.Secret = req.Secret
	}
	if req.Passphrase != "" {
		account.Passphrase = req.Passphrase
	}
	if req.SuFrom != 0 {
		account.SuFrom = req.SuFrom
	}
	if req.OnInvalid != "" {
		account.OnInvalid = req.OnInvalid
	}
	if req.Comment != "" {
		account.Comment = req.Comment
	}

	account.Privileged = req.Privileged
	account.IsActive = req.IsActive
	account.SecretReset = req.SecretReset
	account.PushNow = req.PushNow

	// 验证数据
	dangerous(account.Validate())

	// 更新数据库
	dangerous(account.Update())

	// 发布更新事件，包含原始账号信息
	publishAccountUpdateEvent(&originalAccount, account)

	renderData(c, account, nil)
}

// @Summary 删除账号
// @Description 删除账号，删除前会检查是否有资源绑定关系
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 400 {object} ApiResponse "参数错误或有资源绑定关系"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/{id} [delete]
func accountDelete(c *gin.Context) {
	id := urlParamInt64(c, "id")

	account, err := models.AccountGetById(id)
	dangerous(err)

	if account == nil {
		bomb("账号不存在")
	}

	// 检查账号是否有绑定关系
	bindings, err := models.AccountResourceBindingGets("account_id=?", account.Id)
	dangerous(err)

	if len(bindings) > 0 {
		bomb("无法删除：该账号还有 %d 个资源绑定关系，请先解除绑定", len(bindings))
	}

	// 删除数据库记录
	dangerous(account.Delete())

	// 发布删除事件
	publishAccountEvent(events.EventAccountDelete, account)

	renderMessage(c, "删除成功")
}

// publishAccountEvent 发布账号事件
func publishAccountEvent(eventType events.EventType, account *models.Account) {
	// 获取账号绑定的资源信息
	resources, _ := account.GetBoundResources()
	resourceUUID := ""
	resourceName := ""
	resourceAddress := ""

	// 如果有绑定的资源，取第一个作为主要资源信息（兼容旧的事件格式）
	if len(resources) > 0 {
		resource := resources[0]
		resourceUUID = resource.UUID
		resourceName = resource.Ident
		resourceAddress = resource.Labels
	}

	eventData := &events.AccountEventData{
		ID:           account.Id,
		Name:         account.Name,
		Username:     account.Username,
		SecretType:   account.SecretType,
		Secret:       account.Secret,
		Passphrase:   account.Passphrase,
		AssetUUID:    resourceUUID,    // 资源UUID（兼容性）
		AssetName:    resourceName,    // 资源名称（兼容性）
		AssetAddress: resourceAddress, // 资源地址（兼容性）
		TemplateID:   account.TemplateId,
		Source:       account.Source,
		Privileged:   account.Privileged,
		IsActive:     account.IsActive,
		SecretReset:  account.SecretReset,
		PushNow:      account.PushNow,
		Comment:      account.Comment,
		Creator:      account.Creator,
		CreatedAt:    account.CreatedAt.Unix(),
		UpdatedAt:    account.UpdatedAt.Unix(),
	}

	// 使用JumpServer同步事件生产者发布事件
	producer := rdbEvents.GetJSEventProducer()
	if producer != nil {
		if err := producer.PublishAccountEvent(eventType, eventData); err != nil {
			logger.Errorf("发布账号事件失败: %v", err)
		}
	} else {
		logger.Warning("JumpServer事件生产者未初始化")
	}
}

// publishAccountUpdateEvent 发布账号更新事件，包含原始账号信息
func publishAccountUpdateEvent(originalAccount, updatedAccount *models.Account) {
	// 获取账号绑定的资源信息
	resources, _ := updatedAccount.GetBoundResources()
	resourceUUID := ""
	resourceName := ""
	resourceAddress := ""
	if len(resources) > 0 {
		resourceUUID = resources[0].UUID
		resourceName = resources[0].Name
		// 尝试从Extend字段解析IP地址
		if resources[0].Extend != "" {
			var extendData map[string]interface{}
			if err := json.Unmarshal([]byte(resources[0].Extend), &extendData); err == nil {
				if ip, ok := extendData["ip"].(string); ok {
					resourceAddress = ip
				}
			}
		}
		// 如果没有IP，使用资源名称作为地址
		if resourceAddress == "" {
			resourceAddress = resourceName
		}
	}

	// 构建事件数据，包含更新后的信息
	eventData := &events.AccountEventData{
		ID:           updatedAccount.Id,
		UUID:         "", // Account模型中没有UUID字段，使用空字符串
		Name:         updatedAccount.Name,
		Username:     updatedAccount.Username,
		SecretType:   updatedAccount.SecretType,
		Secret:       updatedAccount.Secret,
		Passphrase:   updatedAccount.Passphrase,
		AssetUUID:    resourceUUID,
		AssetName:    resourceName,
		AssetAddress: resourceAddress,
		TemplateID:   updatedAccount.TemplateId,
		Source:       updatedAccount.Source,
		Privileged:   updatedAccount.Privileged,
		IsActive:     updatedAccount.IsActive,
		SecretReset:  updatedAccount.SecretReset,
		PushNow:      updatedAccount.PushNow,
		Comment:      updatedAccount.Comment,
		Creator:      updatedAccount.Creator,
		CreatedAt:    updatedAccount.CreatedAt.Unix(),
		UpdatedAt:    updatedAccount.UpdatedAt.Unix(),
		// 添加原始账号信息用于查找
		OriginalName:     originalAccount.Name,
		OriginalUsername: originalAccount.Username,
	}

	// 使用JumpServer同步事件生产者发布事件
	producer := rdbEvents.GetJSEventProducer()
	if producer != nil {
		if err := producer.PublishAccountEvent(events.EventAccountUpdate, eventData); err != nil {
			logger.Errorf("发布账号更新事件失败: %v", err)
		}
	} else {
		logger.Warning("JumpServer事件生产者未初始化")
	}
}

// AccountTemplateApplyRequest 账号模板应用请求结构
type AccountTemplateApplyRequest struct {
	TemplateId  int64   `json:"template_id" binding:"required"`
	ResourceIds []int64 `json:"resource_ids" binding:"required,min=1"` // 统一使用resource.id
	Comment     string  `json:"comment"`                               // 可选，覆盖模板中的备注
}

// @Summary 应用账号模板
// @Description 将账号模板应用到多个资产，批量创建账号
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param request body AccountTemplateApplyRequest true "模板应用请求"
// @Success 200 {object} ApiResponse{dat=AccountCreateResponse} "应用结果"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/account-templates/apply [post]
func accountTemplateApply(c *gin.Context) {
	var req AccountTemplateApplyRequest
	bind(c, &req)

	creator := loginUser(c).Username

	// 获取账号模板
	template, err := models.AccountTemplateGetById(req.TemplateId)
	dangerous(err)
	if template == nil {
		bomb("指定的账号模板不存在")
	}

	response := AccountCreateResponse{
		Results: []AccountCreateResult{},
	}

	// 获取资源列表
	resources, err := getResourcesByIds(req.ResourceIds)
	if err != nil {
		bomb(err.Error())
	}

	// 检查是否存在重复的账号（在任何一个资源上）
	validResourceIds := []int64{}
	for _, resource := range resources {
		// 通过绑定关系表检查是否存在重复账号
		bindings, err := models.AccountResourceBindingGets("resource_id=?", resource.Id)
		if err != nil {
			response.Results = append(response.Results, AccountCreateResult{
				ResourceId: resource.Id,
				State:      "error",
				Error:      "检查账号重复失败: " + err.Error(),
			})
			continue
		}

		// 检查是否有相同用户名的账号
		hasConflict := false
		for _, binding := range bindings {
			account, err := models.AccountGetById(binding.AccountId)
			if err != nil {
				continue
			}
			if account != nil && account.Username == template.Username {
				response.Results = append(response.Results, AccountCreateResult{
					ResourceId: resource.Id,
					State:      "error",
					Error:      "该资源上已存在相同用户名的账号",
				})
				hasConflict = true
				break
			}
		}

		if !hasConflict {
			validResourceIds = append(validResourceIds, resource.Id)
		}
	}

	// 如果没有剩余的有效资源，直接返回
	if len(validResourceIds) == 0 {
		renderData(c, response, nil)
		return
	}

	// 从模板创建账号（一个账号绑定多个资产）
	account := models.Account{
		Name:        template.Name,
		Username:    template.Username,
		SecretType:  template.SecretType,
		Secret:      template.Secret,
		Passphrase:  template.Passphrase,
		TemplateId:  template.Id,
		SuFrom:      template.SuFrom,
		Source:      "template",
		Privileged:  template.Privileged,
		IsActive:    template.IsActive,
		SecretReset: false,
		Comment:     template.Comment,
		Creator:     creator,
	}

	if req.Comment != "" {
		account.Comment = req.Comment
	}

	// 验证账号数据
	if err := account.Validate(); err != nil {
		bomb("账号数据验证失败: " + err.Error())
	}

	// 保存账号到数据库
	if err := account.Save(); err != nil {
		bomb("保存账号失败: " + err.Error())
	}

	// 为每个有效资源创建绑定关系
	resourceMap := make(map[int64]*models.Resource)
	for _, resource := range resources {
		resourceMap[resource.Id] = &resource
	}

	for _, resourceId := range validResourceIds {
		resource := resourceMap[resourceId]

		binding := models.AccountResourceBinding{
			AccountId:   account.Id,
			ResourceId:  resource.Id,
			BindingType: "template",
			IsActive:    true,
			Creator:     creator,
		}

		if err := binding.Save(); err != nil {
			response.Results = append(response.Results, AccountCreateResult{
				ResourceId: resourceId,
				State:      "error",
				Error:      "创建绑定关系失败: " + err.Error(),
			})
		} else {
			response.Results = append(response.Results, AccountCreateResult{
				ResourceId: resourceId,
				State:      "success",
			})
		}
	}

	// 发布创建事件
	publishAccountEvent(events.EventAccountCreate, &account)

	// 设置响应中的账号信息
	response.Account = &account

	renderData(c, response, nil)
}

// AccountBindingRequest 账号绑定请求结构
type AccountBindingRequest struct {
	AccountIds  []int64 `json:"account_ids" binding:"required,min=1"`
	ResourceIds []int64 `json:"resource_ids" binding:"required,min=1"` // 统一使用resource.id
}

// AccountBindingResponse 账号绑定响应结构
type AccountBindingResponse struct {
	Success []models.AccountResourceBinding `json:"success"`
	Failed  []AccountBindingError           `json:"failed"`
	Total   int                             `json:"total"`
}

// AccountBindingError 账号绑定错误信息
type AccountBindingError struct {
	AccountId  int64  `json:"account_id"`
	ResourceId int64  `json:"resource_id"`
	Error      string `json:"error"`
}

// @Summary 批量绑定账号到资源
// @Description 将多个账号绑定到多个资源，建立绑定关系
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param request body AccountBindingRequest true "绑定请求"
// @Success 200 {object} ApiResponse{dat=AccountBindingResponse} "绑定结果"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/bind [post]
func accountBind(c *gin.Context) {
	var req AccountBindingRequest
	bind(c, &req)

	// 获取资源列表
	resources, err := getResourcesByIds(req.ResourceIds)
	if err != nil {
		bomb(err.Error())
	}

	response := AccountBindingResponse{
		Success: []models.AccountResourceBinding{},
		Failed:  []AccountBindingError{},
		Total:   len(req.AccountIds) * len(resources),
	}

	// 验证所有账号是否存在
	accountMap := make(map[int64]*models.Account)
	for _, accountId := range req.AccountIds {
		account, err := models.AccountGetById(accountId)
		if err != nil {
			for _, resource := range resources {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "查询账号失败: " + err.Error(),
				})
			}
			continue
		}
		if account == nil {
			for _, resource := range resources {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "账号不存在",
				})
			}
			continue
		}
		accountMap[accountId] = account
	}

	// 构建资源映射
	resourceMap := make(map[string]*models.Resource)
	for i := range resources {
		resource := &resources[i]
		resourceMap[resource.UUID] = resource
	}

	// 创建绑定关系
	for _, accountId := range req.AccountIds {
		_, accountExists := accountMap[accountId]
		if !accountExists {
			continue // 已经在上面的验证中记录了错误
		}

		for _, resource := range resources {
			// 检查绑定关系是否已存在
			exists, err := models.AccountResourceBindingGet("account_id=? AND resource_id=?", accountId, resource.Id)
			if err != nil {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "检查绑定关系失败: " + err.Error(),
				})
				continue
			}
			if exists != nil {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "绑定关系已存在",
				})
				continue
			}

			// 创建绑定关系
			binding := models.AccountResourceBinding{
				AccountId:   accountId,
				ResourceId:  resource.Id,
				BindingType: "manual",
				IsActive:    true,
				Creator:     loginUser(c).Username,
			}

			if err := binding.Save(); err != nil {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "保存绑定关系失败: " + err.Error(),
				})
				continue
			}

			// 发布账号绑定事件
			if err := publishAccountBindEvent(&binding); err != nil {
				logger.Errorf("Failed to publish account bind event: account_id=%d, resource_id=%d, error=%v",
					accountId, resource.Id, err)
				// 不中断流程，只记录错误
			}

			// 添加到成功列表
			response.Success = append(response.Success, binding)
		}
	}

	renderData(c, response, nil)
}

// @Summary 批量解绑账号与资源
// @Description 解除账号与资源的绑定关系
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param request body AccountBindingRequest true "解绑请求"
// @Success 200 {object} ApiResponse{dat=AccountBindingResponse} "解绑结果"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/unbind [post]
func accountUnbind(c *gin.Context) {
	var req AccountBindingRequest
	bind(c, &req)

	// 获取资源列表
	resources, err := getResourcesByIds(req.ResourceIds)
	if err != nil {
		bomb(err.Error())
	}

	response := AccountBindingResponse{
		Success: []models.AccountResourceBinding{},
		Failed:  []AccountBindingError{},
		Total:   len(req.AccountIds) * len(resources),
	}

	// 解除绑定关系
	for _, accountId := range req.AccountIds {
		for _, resource := range resources {
			// 查找绑定关系
			binding, err := models.AccountResourceBindingGet("account_id=? AND resource_id=?", accountId, resource.Id)
			if err != nil {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "查询绑定关系失败: " + err.Error(),
				})
				continue
			}
			if binding == nil {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "绑定关系不存在",
				})
				continue
			}

			// 删除绑定关系
			if err := binding.Delete(); err != nil {
				response.Failed = append(response.Failed, AccountBindingError{
					AccountId:  accountId,
					ResourceId: resource.Id,
					Error:      "删除绑定关系失败: " + err.Error(),
				})
				continue
			}

			// 发布账号解绑事件
			if err := publishAccountUnbindEvent(binding); err != nil {
				logger.Errorf("Failed to publish account unbind event: account_id=%d, resource_id=%d, error=%v",
					accountId, resource.Id, err)
				// 不中断流程，只记录错误
			}

			// 添加到成功列表
			response.Success = append(response.Success, *binding)
		}
	}

	renderData(c, response, nil)
}

// @Summary 获取账号绑定关系
// @Description 获取指定账号的所有资产绑定关系
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "账号ID"
// @Success 200 {object} ApiResponse{dat=[]models.AccountResourceBinding} "绑定关系列表"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "账号不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/{id}/bindings [get]
func accountBindingsGet(c *gin.Context) {
	id := urlParamInt64(c, "id")

	account, err := models.AccountGetById(id)
	dangerous(err)

	if account == nil {
		bomb("账号不存在")
	}

	bindings, err := account.GetAccountBindings()
	dangerous(err)

	renderData(c, bindings, nil)
}

// @Summary 获取资源的账号列表
// @Description 获取指定资源上的所有账号
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param resource_id query int true "资源ID"
// @Success 200 {object} ApiResponse{dat=[]models.Account} "账号列表"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/resources/accounts [get]
func resourceAccountsGet(c *gin.Context) {
	resourceIdStr := c.Query("resource_id")
	if resourceIdStr == "" {
		bomb("资源ID不能为空")
	}

	resourceId := urlParamInt64FromStr(resourceIdStr)

	// 验证资源是否存在
	resource, err := models.ResourceGet("id=?", resourceId)
	dangerous(err)
	if resource == nil {
		bomb(fmt.Sprintf("资源不存在 (id=%d)", resourceId))
	}

	// 通过绑定关系表获取该资源上的所有账号
	bindings, err := models.AccountResourceBindingGets("resource_id=? AND is_active=1", resource.Id)
	dangerous(err)

	if len(bindings) == 0 {
		renderData(c, []models.Account{}, nil)
		return
	}

	// 获取账号ID列表
	var accountIds []int64
	for _, binding := range bindings {
		accountIds = append(accountIds, binding.AccountId)
	}

	// 查询账号详情
	accounts, err := models.AccountGets("id IN (?)", accountIds)
	dangerous(err)

	renderData(c, accounts, nil)
}

// urlParamInt64FromStr 从字符串转换为int64
func urlParamInt64FromStr(str string) int64 {
	id, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		bomb("参数格式错误: " + str)
	}
	return id
}

// AccountStatistics 账号统计信息
type AccountStatistics struct {
	TotalAccounts    int64 `json:"total_accounts"`
	ActiveAccounts   int64 `json:"active_accounts"`
	InactiveAccounts int64 `json:"inactive_accounts"`
	TemplateAccounts int64 `json:"template_accounts"`
	ManualAccounts   int64 `json:"manual_accounts"`
	TotalAssets      int64 `json:"total_assets"`
	TotalBindings    int64 `json:"total_bindings"`
}

// @Summary 获取账号统计信息
// @Description 获取账号的统计信息
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=AccountStatistics} "统计信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/accounts/statistics [get]
func accountStatisticsGet(c *gin.Context) {
	stats := AccountStatistics{}

	// 总账号数
	total, err := models.AccountTotal("")
	dangerous(err)
	stats.TotalAccounts = total

	// 活跃账号数
	active, err := models.AccountTotal("is_active=?", true)
	dangerous(err)
	stats.ActiveAccounts = active

	// 非活跃账号数
	stats.InactiveAccounts = total - active

	// 模板账号数
	template, err := models.AccountTotal("source=?", "template")
	dangerous(err)
	stats.TemplateAccounts = template

	// 手动账号数
	manual, err := models.AccountTotal("source=? OR source=?", "local", "manual")
	dangerous(err)
	stats.ManualAccounts = manual

	// 总资源数（通过绑定关系表统计，去重）
	var resourceCount int64
	_, err = models.DB["rdb"].SQL("SELECT COUNT(DISTINCT asset_uuid) FROM jumpserver_account_asset_binding WHERE is_active=1").Get(&resourceCount)
	dangerous(err)
	stats.TotalAssets = resourceCount

	// 总绑定关系数
	bindingCount, err := models.DB["rdb"].Count(new(models.AccountResourceBinding))
	dangerous(err)
	stats.TotalBindings = bindingCount

	renderData(c, stats, nil)
}

// publishAccountUnbindEvent 发布账号解绑事件
func publishAccountUnbindEvent(binding *models.AccountResourceBinding) error {
	// 获取账号信息
	account := &models.Account{}
	has, err := models.DB["rdb"].ID(binding.AccountId).Get(account)
	if err != nil {
		return fmt.Errorf("failed to get account: %v", err)
	}
	if !has {
		return fmt.Errorf("account not found: id=%d", binding.AccountId)
	}

	// 获取资源信息
	resource := &models.Resource{}
	has, err = models.DB["rdb"].ID(binding.ResourceId).Get(resource)
	if err != nil {
		return fmt.Errorf("failed to get resource: %v", err)
	}
	if !has {
		return fmt.Errorf("resource not found: id=%d", binding.ResourceId)
	}

	// 构造事件数据
	eventData := &events.AccountBindEventData{
		AccountID:   binding.AccountId,
		ResourceID:  binding.ResourceId,
		BindingType: "manual", // 默认为手动绑定
		IsActive:    false,    // 解绑时设置为false
		Creator:     "system", // 系统操作
		CreatedAt:   binding.CreatedAt.Unix(),
	}

	logger.Infof("Publishing account unbind event: account_id=%d, resource_id=%d, binding_id=%d",
		binding.AccountId, binding.ResourceId, binding.Id)

	// 使用JumpServer同步事件生产者发布事件
	producer := rdbEvents.GetJSEventProducer()
	if producer != nil {
		return producer.PublishAccountUnbindEvent(events.EventAccountUnbind, eventData)
	} else {
		logger.Errorf("JumpServer event producer not available")
		return fmt.Errorf("event producer not available")
	}
}

// publishAccountBindEvent 发布账号绑定事件
func publishAccountBindEvent(binding *models.AccountResourceBinding) error {
	// 获取账号信息
	account := &models.Account{}
	has, err := models.DB["rdb"].ID(binding.AccountId).Get(account)
	if err != nil {
		return fmt.Errorf("failed to get account: %v", err)
	}
	if !has {
		return fmt.Errorf("account not found: id=%d", binding.AccountId)
	}

	// 获取资源信息
	resource := &models.Resource{}
	has, err = models.DB["rdb"].ID(binding.ResourceId).Get(resource)
	if err != nil {
		return fmt.Errorf("failed to get resource: %v", err)
	}
	if !has {
		return fmt.Errorf("resource not found: id=%d", binding.ResourceId)
	}

	// 构造事件数据
	eventData := &events.AccountBindEventData{
		AccountID:   binding.AccountId,
		ResourceID:  binding.ResourceId,
		BindingType: "manual", // 默认为手动绑定
		IsActive:    binding.IsActive,
		Creator:     "system", // 系统操作
		CreatedAt:   binding.CreatedAt.Unix(),
	}

	logger.Infof("Publishing account bind event: account_id=%d, resource_id=%d, binding_id=%d",
		binding.AccountId, binding.ResourceId, binding.Id)

	// 使用JumpServer同步事件生产者发布事件
	producer := rdbEvents.GetJSEventProducer()
	if producer != nil {
		return producer.PublishAccountBindEvent(events.EventAccountBind, eventData)
	} else {
		logger.Errorf("JumpServer event producer not available")
		return fmt.Errorf("event producer not available")
	}
}
