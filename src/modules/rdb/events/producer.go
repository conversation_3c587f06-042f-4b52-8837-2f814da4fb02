package events

import (
	"sync"

	"github.com/toolkits/pkg/logger"

	jsEvents "arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/rdb/config"
)

var (
	eventEnabled bool
	streamName   string
	once         sync.Once
)

// EventType 事件类型
type EventType string

const (
	EventNodeCreate     EventType = "node.create"
	EventNodeUpdate     EventType = "node.update"
	EventNodeDelete     EventType = "node.delete"
	EventResourceBind   EventType = "resource.bind"
	EventResourceUnbind EventType = "resource.unbind"
)

// NodeEventData 节点事件数据
type NodeEventData struct {
	ID           int64  `json:"id"`
	PID          int64  `json:"pid"`
	Ident        string `json:"ident"`
	Name         string `json:"name"`
	OriginalName string `json:"original_name"` // 原始名称（更新前的名称）
	Note         string `json:"note"`
	Path         string `json:"path"`
	Leaf         int    `json:"leaf"`
	Cate         string `json:"cate"`
	IconColor    string `json:"icon_color"`
	IconChar     string `json:"icon_char"`
	Proxy        int    `json:"proxy"`
	Creator      string `json:"creator"`
	LastUpdated  int64  `json:"last_updated"`
}

// ResourceBindEventData 资源绑定事件数据
type ResourceBindEventData struct {
	NodeID       int64  `json:"node_id"`
	ResourceID   int64  `json:"resource_id"`
	NodePath     string `json:"node_path"`
	ResourceUUID string `json:"resource_uuid"`
}

// InitProducer 初始化事件生产者
func InitProducer(redisAddr, password string, db int, stream string) error {
	once.Do(func() {
		eventEnabled = true
		streamName = stream
		logger.Infof("Event producer initialized: stream=%s", stream)
	})
	return nil
}

// PublishNodeEvent 发布节点事件
func PublishNodeEvent(eventType EventType, nodeData *NodeEventData) {
	if !eventEnabled {
		return
	}

	// 转换为jumpserver-sync事件格式
	jsNodeData := &jsEvents.NodeEventData{
		ID:           nodeData.ID,
		PID:          nodeData.PID,
		Ident:        nodeData.Ident,
		Name:         nodeData.Name,
		OriginalName: nodeData.OriginalName,
		Note:         nodeData.Note,
		Path:         nodeData.Path,
		Leaf:         nodeData.Leaf,
		Cate:         nodeData.Cate,
		IconColor:    nodeData.IconColor,
		IconChar:     nodeData.IconChar,
		Proxy:        nodeData.Proxy,
		Creator:      nodeData.Creator,
		LastUpdated:  nodeData.LastUpdated,
	}

	// 发布到JumpServer同步事件流
	if IsJSEnabled() {
		go func() {
			var jsEventType jsEvents.EventType
			switch eventType {
			case EventNodeCreate:
				jsEventType = jsEvents.EventNodeCreate
			case EventNodeUpdate:
				jsEventType = jsEvents.EventNodeUpdate
			case EventNodeDelete:
				jsEventType = jsEvents.EventNodeDelete
			default:
				logger.Errorf("Unknown node event type: %s", eventType)
				return
			}

			if err := jsEventProducer.PublishNodeEvent(jsEventType, jsNodeData); err != nil {
				logger.Errorf("Failed to publish node event: %v", err)
			}
		}()
	}

	logger.Infof("Node event published: type=%s, id=%v, path=%s", eventType, nodeData.ID, nodeData.Path)
}

// PublishResourceBindEvent 发布资源绑定事件
func PublishResourceBindEvent(eventType EventType, bindData *ResourceBindEventData) {
	if !eventEnabled {
		return
	}

	// 转换为jumpserver-sync事件格式
	jsBindData := &jsEvents.ResourceBindEventData{
		NodeID:       bindData.NodeID,
		ResourceID:   bindData.ResourceID,
		NodePath:     bindData.NodePath,
		ResourceUUID: bindData.ResourceUUID,
	}

	// 发布到JumpServer同步事件流
	if IsJSEnabled() {
		go func() {
			var jsEventType jsEvents.EventType
			switch eventType {
			case EventResourceBind:
				jsEventType = jsEvents.EventResourceBind
			case EventResourceUnbind:
				jsEventType = jsEvents.EventResourceUnbind
			default:
				logger.Errorf("Unknown resource bind event type: %s", eventType)
				return
			}

			if err := jsEventProducer.PublishResourceBindEvent(jsEventType, jsBindData); err != nil {
				logger.Errorf("Failed to publish resource bind event: %v", err)
			}
		}()
	}

	logger.Infof("Resource bind event published: type=%s, node_id=%v, resource_id=%v",
		eventType, bindData.NodeID, bindData.ResourceID)
}

// Close 关闭事件生产者
func Close() {
	if eventEnabled {
		logger.Info("Event producer closed")
	}
	CloseJSEventProducer()
}

// ==================== JumpServer同步事件发布 ====================

var (
	jsEventProducer *jsEvents.Producer
	jsOnce          sync.Once
	jsInitError     error
)

// InitJSEventProducer 初始化JumpServer同步事件生产者
func InitJSEventProducer() error {
	jsOnce.Do(func() {
		if config.Config.Redis.Addr == "" {
			logger.Info("Redis not configured, JumpServer sync event publishing disabled")
			return
		}

		producer, err := jsEvents.NewProducer(
			config.Config.Redis.Addr,
			config.Config.Redis.Password,
			config.Config.Redis.DB,
			"arboris:sync:events",
		)
		if err != nil {
			jsInitError = err
			logger.Errorf("Failed to create JumpServer sync event producer: %v", err)
			return
		}

		jsEventProducer = producer
		logger.Info("JumpServer sync event producer initialized successfully")
	})

	return jsInitError
}

// InitJSEventProducerWithConfig 使用指定配置初始化JumpServer同步事件生产者
func InitJSEventProducerWithConfig(redisAddr, redisPassword string, redisDB int) error {
	jsOnce.Do(func() {
		if redisAddr == "" {
			logger.Info("Redis not configured, JumpServer sync event publishing disabled")
			return
		}

		producer, err := jsEvents.NewProducer(
			redisAddr,
			redisPassword,
			redisDB,
			"arboris:sync:events",
		)
		if err != nil {
			jsInitError = err
			logger.Errorf("Failed to create JumpServer sync event producer: %v", err)
			return
		}

		jsEventProducer = producer
		logger.Info("JumpServer sync event producer initialized successfully")
	})

	return jsInitError
}

// GetJSProducer 获取JumpServer同步事件生产者
func GetJSProducer() *jsEvents.Producer {
	return jsEventProducer
}

// IsJSEnabled 检查JumpServer同步事件发布是否启用
func IsJSEnabled() bool {
	return jsEventProducer != nil
}

// CloseJSEventProducer 关闭JumpServer同步事件生产者
func CloseJSEventProducer() error {
	if jsEventProducer != nil {
		return jsEventProducer.Close()
	}
	return nil
}

// GetJSEventProducer 获取JumpServer同步事件生产者
func GetJSEventProducer() *jsEvents.Producer {
	return jsEventProducer
}

// ==================== 用户事件发布 ====================

// PublishUserCreateEvent 发布用户创建事件
func PublishUserCreateEvent(userData *jsEvents.UserEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishUserEvent(jsEvents.EventUserCreate, userData); err != nil {
			logger.Errorf("Failed to publish user create event: %v", err)
		}
	}()
}

// PublishUserUpdateEvent 发布用户更新事件
func PublishUserUpdateEvent(userData *jsEvents.UserEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishUserEvent(jsEvents.EventUserUpdate, userData); err != nil {
			logger.Errorf("Failed to publish user update event: %v", err)
		}
	}()
}

// PublishUserDeleteEvent 发布用户删除事件
func PublishUserDeleteEvent(userData *jsEvents.UserEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishUserEvent(jsEvents.EventUserDelete, userData); err != nil {
			logger.Errorf("Failed to publish user delete event: %v", err)
		}
	}()
}

// PublishUserEnableEvent 发布用户启用事件
func PublishUserEnableEvent(userData *jsEvents.UserEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishUserEvent(jsEvents.EventUserEnable, userData); err != nil {
			logger.Errorf("Failed to publish user enable event: %v", err)
		}
	}()
}

// PublishUserDisableEvent 发布用户禁用事件
func PublishUserDisableEvent(userData *jsEvents.UserEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishUserEvent(jsEvents.EventUserDisable, userData); err != nil {
			logger.Errorf("Failed to publish user disable event: %v", err)
		}
	}()
}

// PublishPasswordChangeEvent 发布密码变更事件
func PublishPasswordChangeEvent(passwordData *jsEvents.PasswordChangeEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishPasswordChangeEvent(passwordData); err != nil {
			logger.Errorf("Failed to publish password change event: %v", err)
		}
	}()
}

// ==================== 团队事件发布 ====================

// PublishTeamCreateEvent 发布团队创建事件
func PublishTeamCreateEvent(teamData *jsEvents.TeamEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishTeamEvent(jsEvents.EventTeamCreate, teamData); err != nil {
			logger.Errorf("Failed to publish team create event: %v", err)
		}
	}()
}

// PublishTeamUpdateEvent 发布团队更新事件
func PublishTeamUpdateEvent(teamData *jsEvents.TeamEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishTeamEvent(jsEvents.EventTeamUpdate, teamData); err != nil {
			logger.Errorf("Failed to publish team update event: %v", err)
		}
	}()
}

// PublishTeamDeleteEvent 发布团队删除事件
func PublishTeamDeleteEvent(teamData *jsEvents.TeamEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishTeamEvent(jsEvents.EventTeamDelete, teamData); err != nil {
			logger.Errorf("Failed to publish team delete event: %v", err)
		}
	}()
}

// PublishTeamAddMemberEvent 发布团队添加成员事件
func PublishTeamAddMemberEvent(teamData *jsEvents.TeamEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishTeamAddMemberEvent(teamData); err != nil {
			logger.Errorf("Failed to publish team add member event: %v", err)
		}
	}()
}

// PublishTeamRemoveMemberEvent 发布团队移除成员事件
func PublishTeamRemoveMemberEvent(teamData *jsEvents.TeamEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishTeamRemoveMemberEvent(teamData); err != nil {
			logger.Errorf("Failed to publish team remove member event: %v", err)
		}
	}()
}

// ==================== 权限事件发布 ====================

// PublishPermissionGrantEvent 发布权限授予事件
func PublishPermissionGrantEvent(permissionData *jsEvents.PermissionEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishPermissionEvent(jsEvents.EventPermissionGrant, permissionData); err != nil {
			logger.Errorf("Failed to publish permission grant event: %v", err)
		}
	}()
}

// PublishPermissionRevokeEvent 发布权限撤销事件
func PublishPermissionRevokeEvent(permissionData *jsEvents.PermissionEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishPermissionEvent(jsEvents.EventPermissionRevoke, permissionData); err != nil {
			logger.Errorf("Failed to publish permission revoke event: %v", err)
		}
	}()
}

// ==================== 节点事件发布 ====================

// PublishNodeCreateEvent 发布节点创建事件
func PublishNodeCreateEvent(nodeData *jsEvents.NodeEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishNodeEvent(jsEvents.EventNodeCreate, nodeData); err != nil {
			logger.Errorf("Failed to publish node create event: %v", err)
		}
	}()
}

// PublishNodeUpdateEvent 发布节点更新事件
func PublishNodeUpdateEvent(nodeData *jsEvents.NodeEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishNodeEvent(jsEvents.EventNodeUpdate, nodeData); err != nil {
			logger.Errorf("Failed to publish node update event: %v", err)
		}
	}()
}

// PublishNodeDeleteEvent 发布节点删除事件
func PublishNodeDeleteEvent(nodeData *jsEvents.NodeEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishNodeEvent(jsEvents.EventNodeDelete, nodeData); err != nil {
			logger.Errorf("Failed to publish node delete event: %v", err)
		}
	}()
}

// ==================== 资源绑定事件发布 ====================

// PublishResourceBindCreateEvent 发布资源绑定事件
func PublishResourceBindCreateEvent(bindData *jsEvents.ResourceBindEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishResourceBindEvent(jsEvents.EventResourceBind, bindData); err != nil {
			logger.Errorf("Failed to publish resource bind event: %v", err)
		}
	}()
}

// PublishResourceUnbindEvent 发布资源解绑事件
func PublishResourceUnbindEvent(bindData *jsEvents.ResourceBindEventData) {
	if !IsJSEnabled() {
		return
	}

	go func() {
		if err := jsEventProducer.PublishResourceBindEvent(jsEvents.EventResourceUnbind, bindData); err != nil {
			logger.Errorf("Failed to publish resource unbind event: %v", err)
		}
	}()
}
