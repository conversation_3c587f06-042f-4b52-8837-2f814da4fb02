#!/bin/bash

# 测试资源解绑功能的脚本

echo "=== 测试资源解绑功能 ==="

# 测试参数
RESOURCE_UUID="*******"
NODE_PATH="jump-test.jump-test1.jump-test2"  # 要解绑的节点路径

echo "测试参数:"
echo "  资源UUID: $RESOURCE_UUID"
echo "  解绑节点路径: $NODE_PATH"
echo ""

# 首先查看当前资产的绑定状态
echo "1. 查看当前资产绑定状态..."
curl -s "http://**********:8090/api/v1/assets/hosts/?address=$RESOURCE_UUID" \
  -H 'Accept: application/json' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  --insecure | jq '.results[0].nodes' 2>/dev/null || echo "无法查询当前状态（可能需要认证）"

echo ""

# 模拟资源解绑事件
echo "2. 发送资源解绑事件..."

# 创建测试事件数据
EVENT_ID="test-unbind-$(date +%s)"
TIMESTAMP=$(date +%s)

cat > /tmp/test_unbind_event.json << EOF
{
  "id": "$EVENT_ID",
  "type": "resource.unbind",
  "source": "test",
  "timestamp": $TIMESTAMP,
  "data": {
    "node_path": "$NODE_PATH",
    "resource_uuid": "$RESOURCE_UUID"
  }
}
EOF

echo "解绑事件数据:"
cat /tmp/test_unbind_event.json | jq .
echo ""

# 发送到Redis Stream进行测试（如果redis-cli可用）
if command -v redis-cli &> /dev/null; then
    echo "3. 发送测试事件到Redis Stream..."
    redis-cli -h 127.0.0.1 -p 6379 -n 0 XADD "arboris:sync:events" "*" \
      "id" "$EVENT_ID" \
      "type" "resource.unbind" \
      "source" "test" \
      "timestamp" "$TIMESTAMP" \
      "data.node_path" "$NODE_PATH" \
      "data.resource_uuid" "$RESOURCE_UUID"
    
    echo "事件已发送到Redis Stream"
else
    echo "3. Redis CLI不可用，跳过事件发送"
fi

echo ""
echo "4. 请查看jumpserver-sync日志以验证解绑逻辑:"
echo "   tail -f logs/jumpserver-sync/INFO.log"
echo ""

echo "期望看到的日志:"
echo "1. Resource unbind event: node_path=$NODE_PATH, resource_uuid=$RESOURCE_UUID"
echo "2. Asset found in JumpServer: id=xxx, name=xxx"
echo "3. Found node to remove: node_id=xxx, node_name=xxx"
echo "4. Node removal analysis: asset_id=xxx, target_node=xxx, node_removed=true"
echo "5. Asset updated, node binding removed: asset_id=xxx, removed_node=xxx, remaining_nodes=[...]"
echo "   或者如果没有剩余节点："
echo "   Asset has no more node bindings, deleting asset: asset_id=xxx"

echo ""
echo "测试完成后，可以通过以下方式验证结果:"
echo "1. 在JumpServer页面查看资产是否从指定节点移除"
echo "2. 如果资产还绑定到其他节点，应该仍然存在"
echo "3. 如果资产没有绑定到任何节点，应该被删除"

# 清理临时文件
rm -f /tmp/test_unbind_event.json
