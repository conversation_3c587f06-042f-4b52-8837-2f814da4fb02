# AMS主机更新API测试指南

## API接口信息

- **URL**: `PUT /api/ams-ce/host/{id}`
- **方法**: `PUT`
- **认证**: 需要 (<PERSON><PERSON>或<PERSON>)
- **权限**: `ams_host_modify`

## 请求示例

### 1. 更新主机基本信息

```bash
curl -X PUT "http://localhost:8002/api/ams-ce/host/1" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=your-session-id" \
  -d '{
    "name": "Updated Web Server 01",
    "note": "Updated production web server",
    "os_version": "Ubuntu 22.04",
    "cpu": "32",
    "mem": "128GB",
    "disk": "2TB"
  }'
```

### 2. 更新主机硬件信息

```bash
curl -X PUT "http://localhost:8002/api/ams-ce/host/1" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=your-session-id" \
  -d '{
    "model": "Dell PowerEdge R750",
    "manufacturer": "Dell",
    "gpu": "4",
    "gpu_model": "NVIDIA A100",
    "idc": "shanghai",
    "zone": "us-west-1b",
    "rack": "rack-02"
  }'
```

## 响应示例

### 成功响应 (200)

```json
{
  "code": 0,
  "dat": {
    "id": 1,
    "sn": "SN001",
    "ip": "*************",
    "ident": "web-server-01",
    "name": "Updated Web Server 01",
    "os_version": "Ubuntu 22.04",
    "kernel_version": "5.15.0-25-generic",
    "cpu_model": "Intel Xeon Gold 6248R",
    "cpu": "32",
    "mem": "128GB",
    "disk": "2TB",
    "note": "Updated production web server",
    "cate": "host",
    "tenant": "team-a",
    "clock": 1703123456,
    "gpu": "4",
    "gpu_model": "NVIDIA A100",
    "model": "Dell PowerEdge R750",
    "manufacturer": "Dell",
    "idc": "shanghai",
    "zone": "us-west-1b",
    "rack": "rack-02"
  },
  "err": ""
}
```

### 错误响应

#### 主机不存在 (404)
```json
{
  "code": 404,
  "dat": null,
  "err": "host not found"
}
```

#### 权限不足 (403)
```json
{
  "code": 403,
  "dat": null,
  "err": "permission denied"
}
```

#### 参数错误 (400)
```json
{
  "code": 400,
  "dat": null,
  "err": "no fields to update"
}
```

## 字段约束

### ❌ 不可修改字段
- `id` - 主机ID
- `sn` - 序列号
- `ip` - IP地址
- `ident` - 标识符
- `tenant` - 租户
- `clock` - 心跳时间戳

### ✅ 可修改字段
- `name` - 主机名
- `note` - 备注
- `os_version` - 操作系统版本
- `kernel_version` - 内核版本
- `cpu_model` - CPU型号
- `cpu` - CPU信息
- `mem` - 内存信息
- `disk` - 磁盘信息
- `gpu` - GPU信息
- `gpu_model` - GPU型号
- `model` - 主机型号
- `manufacturer` - 制造商
- `idc` - 数据中心
- `zone` - 可用区
- `rack` - 机架
- `cate` - 类别

## 同步行为

### 同步条件
API会检查主机的同步状态：

1. **已同步到RDB且已挂载到节点**: 
   - ✅ 同步更新到RDB resource表
   - ✅ 发布JumpServer更新事件

2. **未同步到RDB或未挂载到节点**:
   - ❌ 不进行同步
   - ❌ 不发布JumpServer事件

### JumpServer事件
当满足同步条件时，会发布包含以下信息的主机更新事件：
- 主机完整信息（JumpServer主要关注name和note字段）
- 事件类型：`host.update`
- 事件源：`ams`

## 测试场景

### 1. 正常更新测试
- 修改允许的字段
- 验证响应数据正确性
- 检查数据库更新

### 2. 权限测试
- 无权限用户访问
- 验证权限检查

### 3. 字段约束测试
- 尝试修改不可变字段
- 验证字段过滤

### 4. 同步测试
- 更新已挂载主机，验证事件发布
- 更新未挂载主机，验证无事件发布

### 5. 边界测试
- 空字段更新
- 不存在的主机ID
- 无效的字段值
