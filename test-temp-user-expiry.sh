#!/bin/bash

# 测试临时用户有效期检查功能的脚本

echo "=== 测试临时用户有效期检查功能 ==="

# 计算时间戳
CURRENT_TIME=$(date +%s)
PAST_TIME=$((CURRENT_TIME - 3600))      # 1小时前
FUTURE_TIME=$((CURRENT_TIME + 3600))    # 1小时后
NEAR_FUTURE=$((CURRENT_TIME + 60))      # 1分钟后

echo "测试参数:"
echo "  当前时间: $(date -r $CURRENT_TIME)"
echo "  过去时间: $(date -r $PAST_TIME)"
echo "  未来时间: $(date -r $FUTURE_TIME)"
echo "  近期时间: $(date -r $NEAR_FUTURE)"
echo ""

# 1. 创建一个已过期的临时用户
echo "1. 创建已过期的临时用户..."
EXPIRED_USERNAME="expired_temp_user_$(date +%s)"

cat > /tmp/expired_user.json << EOF
{
  "username": "$EXPIRED_USERNAME",
  "password": "Test123456!",
  "dispname": "已过期临时用户",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "typ": 1,
  "status": 0,
  "organization": "测试组织",
  "active_begin": $PAST_TIME,
  "active_end": $((PAST_TIME + 1800))
}
EOF

# 注意：这个创建请求应该会失败，因为结束时间早于当前时间
EXPIRED_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/expired_user.json 2>/dev/null || echo "创建失败")

echo "已过期临时用户创建结果（应该失败）:"
echo "$EXPIRED_USER_RESULT"
echo ""

# 2. 创建一个尚未生效的临时用户
echo "2. 创建尚未生效的临时用户..."
FUTURE_USERNAME="future_temp_user_$(date +%s)"

cat > /tmp/future_user.json << EOF
{
  "username": "$FUTURE_USERNAME",
  "password": "Test123456!",
  "dispname": "尚未生效临时用户",
  "email": "<EMAIL>",
  "phone": "13800138002",
  "typ": 1,
  "status": 0,
  "organization": "测试组织",
  "active_begin": $NEAR_FUTURE,
  "active_end": $FUTURE_TIME
}
EOF

FUTURE_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/future_user.json 2>/dev/null || echo "创建失败")

echo "尚未生效临时用户创建结果:"
echo "$FUTURE_USER_RESULT"
echo ""

# 3. 创建一个有效的临时用户
echo "3. 创建有效的临时用户..."
VALID_USERNAME="valid_temp_user_$(date +%s)"

cat > /tmp/valid_user.json << EOF
{
  "username": "$VALID_USERNAME",
  "password": "Test123456!",
  "dispname": "有效临时用户",
  "email": "<EMAIL>",
  "phone": "13800138003",
  "typ": 1,
  "status": 0,
  "organization": "测试组织",
  "active_begin": $((CURRENT_TIME - 60)),
  "active_end": $FUTURE_TIME
}
EOF

VALID_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/valid_user.json 2>/dev/null || echo "创建失败")

echo "有效临时用户创建结果:"
echo "$VALID_USER_RESULT"
echo ""

# 4. 测试登录功能
echo "4. 测试临时用户登录功能..."

# 如果有效用户创建成功，测试登录
if echo "$VALID_USER_RESULT" | grep -q '"err":""' 2>/dev/null; then
    echo "测试有效临时用户登录..."
    LOGIN_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/auth/login" \
      -H "Content-Type: application/json" \
      -d "{\"username\":\"$VALID_USERNAME\",\"password\":\"Test123456!\"}" 2>/dev/null || echo "登录失败")
    
    echo "有效临时用户登录结果:"
    echo "$LOGIN_RESULT"
    echo ""
else
    echo "有效临时用户创建失败，跳过登录测试"
fi

# 如果尚未生效用户创建成功，测试登录
if echo "$FUTURE_USER_RESULT" | grep -q '"err":""' 2>/dev/null; then
    echo "测试尚未生效临时用户登录..."
    FUTURE_LOGIN_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/auth/login" \
      -H "Content-Type: application/json" \
      -d "{\"username\":\"$FUTURE_USERNAME\",\"password\":\"Test123456!\"}" 2>/dev/null || echo "登录失败")
    
    echo "尚未生效临时用户登录结果（应该失败）:"
    echo "$FUTURE_LOGIN_RESULT"
    echo ""
else
    echo "尚未生效临时用户创建失败，跳过登录测试"
fi

echo "5. 检查日志..."
echo "最新的用户相关日志:"
tail -10 logs/rdb/INFO.log | grep -E "(user|temp|expire)" || echo "没有找到相关日志"

echo ""
echo "期望看到的结果:"
echo "1. 已过期临时用户创建失败"
echo "2. 尚未生效临时用户创建成功"
echo "3. 有效临时用户创建成功"
echo "4. 有效临时用户可以正常登录"
echo "5. 尚未生效临时用户登录失败，提示'临时账号尚未生效'"

# 清理临时文件
rm -f /tmp/expired_user.json /tmp/future_user.json /tmp/valid_user.json
