#!/bin/bash

# 测试节点路径转换的脚本

echo "=== 测试节点路径转换 ==="

# 测试节点路径
NODE_PATH="jump-test.jump-test1.jump-test2"

echo "测试节点路径: $NODE_PATH"
echo "期望结果: 应该找到正确的子节点，而不是父节点"
echo ""

# 模拟资源绑定事件来测试节点查找
echo "模拟资源绑定事件..."

# 创建测试事件数据
cat > /tmp/test_event.json << EOF
{
  "id": "test-$(date +%s)",
  "type": "resource.bind",
  "source": "test",
  "timestamp": $(date +%s),
  "data": {
    "node_path": "$NODE_PATH",
    "resource_uuid": "test-resource-$(date +%s)"
  }
}
EOF

echo "测试事件数据:"
cat /tmp/test_event.json | jq .
echo ""

# 发送到Redis Stream进行测试
echo "发送测试事件到Redis Stream..."
redis-cli -h 127.0.0.1 -p 6379 -n 0 XADD "arboris:sync:events" "*" \
  "id" "test-$(date +%s)" \
  "type" "resource.bind" \
  "source" "test" \
  "timestamp" "$(date +%s)" \
  "data.node_path" "$NODE_PATH" \
  "data.resource_uuid" "test-resource-$(date +%s)"

echo ""
echo "事件已发送，请查看jumpserver-sync日志以验证节点查找逻辑:"
echo "tail -f logs/jumpserver-sync/INFO.log"
echo ""
echo "期望看到的日志:"
echo "1. Found RDB node: id=59, ident=jump-test2, name=jumpserver测试节点2, path=jump-test.jump-test1.jump-test2"
echo "2. Found parent node: id=56, ident=jump-test1, name=jumpserver测试节点2, path=jump-test.jump-test1"
echo "3. 最终找到正确的JumpServer节点: 897c62e2-5027-4161-ae80-a63b86634be5 (子节点)"
echo "   而不是错误的节点: ce17a45b-edc0-47c6-94f6-b31e5ba612e3 (父节点)"

# 清理临时文件
rm -f /tmp/test_event.json
