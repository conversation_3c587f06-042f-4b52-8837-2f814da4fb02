-- 调试node_resource表的SQL查询

-- 1. 查看资源2的基本信息
SELECT 'Resource Info' as query_type, id, uuid, ident, name, source_id, source_type, tenant 
FROM resource 
WHERE id = 2 OR uuid = '*******' OR source_id = 2;

-- 2. 查看节点59的基本信息  
SELECT 'Node Info' as query_type, id, pid, ident, name, path, leaf, cate, tenant
FROM node 
WHERE id = 59 OR path LIKE '%jump-test2%';

-- 3. 查看node_resource表中资源2的绑定关系
SELECT 'Resource 2 Bindings' as query_type, node_id, res_id
FROM node_resource 
WHERE res_id = 2;

-- 4. 查看node_resource表中节点59的绑定关系
SELECT 'Node 59 Bindings' as query_type, node_id, res_id
FROM node_resource 
WHERE node_id = 59;

-- 5. 查看所有与host-2相关的资源
SELECT 'Host-2 Resources' as query_type, id, uuid, ident, name, source_id, source_type, tenant
FROM resource 
WHERE uuid LIKE '%host-2%' OR uuid = 'host-2' OR source_id = 2;

-- 6. 查看所有与*******相关的资源
SELECT '******* Resources' as query_type, id, uuid, ident, name, source_id, source_type, tenant
FROM resource 
WHERE uuid = '*******' OR ident = '*******' OR name LIKE '%*******%';

-- 7. 查看node_resource表的所有记录（如果记录不多的话）
SELECT 'All Node Resource Bindings' as query_type, COUNT(*) as total_count
FROM node_resource;

-- 8. 查看最近的node_resource绑定记录（假设有时间戳字段）
SELECT 'Recent Bindings' as query_type, node_id, res_id
FROM node_resource 
ORDER BY node_id DESC, res_id DESC 
LIMIT 10;

-- 9. 检查是否有重复的资源UUID
SELECT 'Duplicate UUIDs' as query_type, uuid, COUNT(*) as count
FROM resource 
GROUP BY uuid 
HAVING COUNT(*) > 1;

-- 10. 检查资源2是否存在于其他相关表中
SELECT 'Resource 2 Existence Check' as query_type, 
       (SELECT COUNT(*) FROM resource WHERE id = 2) as in_resource_table,
       (SELECT COUNT(*) FROM node_resource WHERE res_id = 2) as in_node_resource_table;
