{"zh": {"cannot convert %s to int64": "%s 无法转为 int64 类型", "cannot convert %s to int": "%s 无法转为 int 类型", "args invalid": "参数无效", "arg[%s] not found": "参数[%s]没找到", "[%s] is blank": "参数[%s]值不能为空", "no such field": "扩展字段未找到", "field_type cannot modify": "字段类型不能被修改", "%s invalid": "%s 不符合规范", "ident is blank": "唯一标识不能为空", "url param[%s] is blank": "url参数[%s]不能为空", "query param[%s] is necessary": "query参数[%s]不能为空", "Cannot find the user by %s": "无法用 %s 找到相关用户", "Invalid arguments %s": "不合法的参数 %s", "IP %s already exists": "IP %s 已存在", "ident %s already exists": "唯一标识已存在", "required field %s(%s) is empty": "%s 字段不能为空", "field %s not defined": "%s 字段未定义", "%s is empty": "%s 为空", "failed to write example row: %v": "写入示例行失败: %v", "failed to open file: %v": "处理文件失败：%v", "failed to parse CSV: %v": "解析文件失败：%v", "CSV file must contain at least one data row": "导入文件至少包含一行数据", "Minimum password length %d": "密码长度最少 %d 位", "Upper char": "大写字母", "Lower char": "小写字母", "Number": "数字", "Special char": "特殊字符", "Must include %s": "必须包含 %s", "character: %s not supported": "不支持的字符 %s", "no file uploaded": "未选择上传文件", "unsupported file format '%s'. Only CSV (.csv), Excel (.xls, .xlsx) files are supported": "不支持的文件格式 '%s'，仅支持 CSV (.csv) 和 Excel (.xls, .xlsx) 文件", "file too large, maximum size is 10MB": "文件大小超过限制，最大支持10MB", "Too many rows to import. Maximum allowed: 1000 rows, but got: %d rows": "导入数据行数超过限制，最大支持1000行，但实际有 %d 行", "Failed to get host fields: %v": "获取主机字段失败：%v", "import_id is required": "导入批次ID不能为空", "import batch not found or expired": "导入批次不存在或已过期", "no failed data found": "未找到失败数据", "failed to write CSV headers: %v": "写入CSV头部失败：%v", "failed to write failed row: %v": "写入失败数据行失败：%v", "cannot retrieve user[%d]: %v": "获取不到用户[%d],原因:%v", "no such user[%d]": "用户[%d]不存在", "no such user: %s": "用户[%s]不存在", "cannot retrieve team[%d]: %v": "获取不到团队[%d],原因:%v", "no such team[%d]": "团队[%d]不存在", "cannot retrieve role[%d]: %v": "获取不到角色[%d],原因:%v", "no such role[%d]": "角色[%d]不存在", "no such NodeCate[id:%d]": "节点类型[%d]没找到", "arg[endpoints] empty": "参数不能[endpoints]为空", "arg[cur_nid_paths] empty": "参数不能[cur_nid_paths]为空", "arg[tags] empty": "参数不能[tags]为空", "arg[hosts] empty": "参数不能[hosts]为空", "arg[btime,etime] empty": "参数[btime,etime]不合规范", "arg[name] empty": "参数[name]不合规范", "arg[name] is blank": "参数[名称]不能为空", "arg[ids] is empty": "参数[ids]不能为空", "%s too long > 64": "%s 超过64长度限制", "arg[%s] too long > %d": "参数 %s 长度不能超过 %d", "cate is blank": "节点分类不能为空", "uuid is blank": "uuid不能为空", "tenant is blank": "租户不能为空", "ids is blank": "ids不能为空", "items empty": "提交内容不能为空", "ident legal characters: [a-z0-9_-]": "唯一标识英文只能字母开头，包括数字、中划线、下划线", "ident length should be less than 32": "唯一标识长度需小于32", "cannot modify tenant's node-category": "租户分类不允许修改", "cannot modify node-category to tenant": "节点分类不允许修改为租户", "node is managed by other system": "租户正在被系统系统使用", "resources not found by %s": "通过 %s 没有找到资源", "cannot delete root user": "root用户不能删除", "user not found": "用户未找到", "Unable to get captcha": "无法获得验证码", "Invalid captcha answer": "错误的验证码", "Username %s is invalid": "用户名 %s 不符合规范", "Username %s too long > 64": "用户名 %s 太长(64)", "Unable to get login arguments": "无法获得登陆参数", "Deny Access from %s with whitelist control": "来自 %s 的访问被白名单规则拒绝", "Invalid login type %s": "不支持的登陆类型 %s", "sms/email sender is disabled": "无法发送 短信/邮件 验证码", "Invalid code type %s": "不支持的验证码类型 %s", "Unable to get password": "无法获取密码", "Login fail, check your username and password": "登陆失败，请检查用户名/密码", "User dose not exist": "用户不存在", "Username %s already exists": "用户名 %s 已存在", "Invalid Password, %s": "密码不符合规范, %s", "Incorrect login/password %s times, you still have %s chances": "登陆失败%d次，你还有%d次机会", "The limited sessions %d": "会话数量限制，最多%d个会话", "Password has been expired": "密码已过期，请重置密码", "User is inactive": "用户已禁用", "User is locked": "用户已锁定", "User is frozen": "用户已休眠", "User is writen off": "用户已注销", "Password too short (min:%d) %s": "密码太短 (最小 %d) %s", "%s format error": "%s 所填内容不符合规范", "%s %s format error": "%s %s 所填内容不符合规范", "username too long (max:%d)": "用户名太长 (最长:%d)", "dispname too long (max:%d)": "昵称太长 (最长:%d)", "email %s or phone %s is exists": "邮箱 %s 或者 手机号 %s 已存在", "Password is not set": "密码未设置", "Incorrect old password": "密码错误", "The password is the same as the old password": "密码与历史密码重复", "phone": "手机号", "email": "邮箱", "username": "用户名", "dispname": "昵称", "Temporary user has expired": "临时账号,已过有效期", "Invalid user status %d": "异常的用户状态 %d", "Password expired, please change the password in time": "密码过期，请及时修改密码", "First Login, please change the password in time": "初始登陆，请及时修改密码", "invite url already expired": "邀请链接已过期", "Cannot found white list": "无法找到白名单", "no privilege": "没有权限访问", "EOF": ""}}