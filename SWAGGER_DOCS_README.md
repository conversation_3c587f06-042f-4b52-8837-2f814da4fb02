# Arboris Swagger API 文档

本项目为各个模块提供了完整的 Swagger API 文档，方便开发者了解和使用各个服务的接口。

## 📚 可用的文档

### 1. AMS 模块 (资产管理系统)
- **文档位置**: `docs-ams/`
- **生成脚本**: `./generate-ams-swagger.sh`
- **在线访问**: http://localhost:8002/swagger/index.html
- **服务端口**: 8002

**主要功能**:
- 资产管理 (主机、网络设备等)
- 用户认证和授权
- 系统配置管理
- 数据导入导出

### 2. JumpServer同步模块
- **文档位置**: `docs-jumpserver-sync/`
- **生成脚本**: `./generate-jumpserver-sync-swagger.sh`
- **在线访问**: http://localhost:8003/swagger/index.html
- **服务端口**: 8003

**主要功能**:
- 死信队列管理
- 消息重新处理
- 系统状态监控
- 健康检查

### 3. RDB 模块 (关系数据库)
- **文档位置**: `docs-rdb/` (如果已生成)
- **生成脚本**: `./generate-rdb-swagger.sh` (待创建)
- **在线访问**: http://localhost:8001/swagger/index.html
- **服务端口**: 8001

## 🚀 快速开始

### 生成文档

```bash
# 生成 AMS 模块文档
./generate-ams-swagger.sh

# 生成 JumpServer同步模块文档
./generate-jumpserver-sync-swagger.sh

# 生成所有模块文档
./generate-ams-swagger.sh && ./generate-jumpserver-sync-swagger.sh
```

### 查看文档

#### 方式1: 在线查看 (推荐)
1. 启动对应的服务
2. 在浏览器中访问对应的 swagger 地址

```bash
# 启动 AMS 服务
cd src/modules/ams && go run ams.go

# 启动 JumpServer同步服务
cd src/modules/jumpserver-sync && go run jumpserver-sync.go
```

#### 方式2: 本地文件查看
直接查看生成的 `swagger.yaml` 或 `swagger.json` 文件

#### 方式3: 在线编辑器
1. 访问 https://editor.swagger.io/
2. 导入本地的 `swagger.yaml` 文件

## 📖 API 文档结构

### AMS 模块 API
```
/api/ams-ce/
├── /auth/          # 认证相关
├── /hosts/         # 主机管理
├── /networks/      # 网络设备管理
├── /users/         # 用户管理
├── /configs/       # 配置管理
└── /import/        # 数据导入
```

### JumpServer同步模块 API
```
/
├── /health                                    # 基础健康检查
├── /info                                     # 服务信息
└── /api/admin/
    ├── /health                               # 管理接口健康检查
    ├── /status                               # 系统状态
    └── /dead-letter/
        ├── /messages                         # 死信消息列表
        ├── /messages/{id}                    # 单个死信消息
        ├── /messages/{id}/reprocess          # 重新处理消息
        ├── /messages/batch-reprocess         # 批量重新处理
        └── /stats                            # 统计信息
```

## 🔧 开发指南

### 添加新的 API 接口

1. **添加 Swagger 注释**
```go
// CreateHost 创建主机
//
//	@Summary		创建主机
//	@Description	创建一个新的主机记录
//	@Tags			Hosts
//	@Accept			json
//	@Produce		json
//	@Param			host	body		CreateHostRequest	true	"主机信息"
//	@Success		200		{object}	CreateHostResponse	"创建成功"
//	@Failure		400		{object}	ErrorResponse		"请求参数错误"
//	@Router			/api/ams-ce/hosts [post]
//	@Security		ApiKeyAuth
func CreateHost(c *gin.Context) {
    // 实现代码
}
```

2. **定义响应结构体**
```go
// CreateHostRequest 创建主机请求
type CreateHostRequest struct {
    Name string `json:"name" example:"web-server-01"`
    IP   string `json:"ip" example:"*************"`
}

// CreateHostResponse 创建主机响应
type CreateHostResponse struct {
    Success bool `json:"success" example:"true"`
    Data    Host `json:"data"`
}
```

3. **重新生成文档**
```bash
./generate-ams-swagger.sh
```

### Swagger 注释规范

- `@Summary`: 接口简短描述
- `@Description`: 接口详细描述
- `@Tags`: 接口分组标签
- `@Accept`: 接受的内容类型
- `@Produce`: 返回的内容类型
- `@Param`: 参数定义
- `@Success`: 成功响应
- `@Failure`: 错误响应
- `@Router`: 路由定义
- `@Security`: 安全认证

## 🛠️ 故障排除

### 常见问题

1. **swag 工具未找到**
```bash
go install github.com/swaggo/swag/cmd/swag@latest
```

2. **文档生成失败**
- 检查 Go 语法错误
- 检查 Swagger 注释格式
- 确保所有依赖包已安装

3. **在线文档无法访问**
- 确认服务已启动
- 检查端口是否被占用
- 确认防火墙设置

### 调试技巧

1. **查看详细错误信息**
```bash
./generate-ams-swagger.sh 2>&1 | tee swagger-generation.log
```

2. **验证生成的文档**
```bash
# 检查文档文件是否存在
ls -la docs-ams/
ls -la docs-jumpserver-sync/

# 验证 JSON 格式
cat docs-ams/swagger.json | jq .
```

## 📝 更新日志

- **2025-08-19**: 
  - ✅ 完成 AMS 模块 Swagger 文档
  - ✅ 完成 JumpServer同步模块 Swagger 文档
  - ✅ 创建文档生成脚本
  - ✅ 添加 CORS 支持
  - ✅ 配置 Swagger UI

## 🤝 贡献指南

1. 为新接口添加完整的 Swagger 注释
2. 定义清晰的请求/响应结构体
3. 使用有意义的示例数据
4. 更新相应的生成脚本
5. 测试文档生成和在线访问

## 📞 支持

如有问题，请联系开发团队或提交 Issue。
