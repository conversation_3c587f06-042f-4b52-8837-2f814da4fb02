basePath: /
definitions:
  http.ApiResponse:
    properties:
      dat: {}
      err:
        type: string
    type: object
  http.HostBindingInfo:
    properties:
      host_id:
        type: integer
      host_ident:
        type: string
      host_ip:
        type: string
      host_name:
        type: string
      node_paths:
        items:
          type: string
        type: array
    type: object
  http.HostFieldValueListResponse:
    properties:
      list:
        items: {}
        type: array
      total:
        type: integer
    type: object
  http.HostFilterItem:
    properties:
      cate:
        type: string
      cpu:
        type: string
      cpu_model:
        type: string
      disk:
        type: string
      field_values:
        additionalProperties:
          type: string
        type: object
      gpu:
        type: string
      gpu_model:
        type: string
      id:
        type: integer
      idc:
        type: string
      ident:
        type: string
      ip:
        type: string
      kernel_version:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      node_paths:
        items:
          type: string
        type: array
      note:
        type: string
      os_version:
        type: string
      rack:
        type: string
      sn:
        type: string
      tenant:
        type: string
      zone:
        type: string
    type: object
  http.HostFilterResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/http.HostFilterItem'
        type: array
      total:
        type: integer
    type: object
  http.HostListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.Host'
        type: array
      total:
        type: integer
    type: object
  http.ImportResult:
    properties:
      failed_count:
        type: integer
      import_id:
        type: string
      success_count:
        type: integer
      total_rows:
        type: integer
    type: object
  http.hostAddForm:
    properties:
      cate:
        type: string
      cpu:
        type: string
      cpu_model:
        type: string
      disk:
        type: string
      fields:
        additionalProperties:
          type: string
        description: 自定义字段
        type: object
      gpu:
        type: string
      gpu_model:
        type: string
      idc:
        type: string
      ident:
        type: string
      ip:
        type: string
      kernel_version:
        type: string
      manufacturer:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      note:
        type: string
      os_version:
        type: string
      rack:
        type: string
      sn:
        type: string
      tenant:
        type: string
      zone:
        type: string
    required:
    - ident
    - ip
    type: object
  http.hostCateForm:
    properties:
      cate:
        type: string
      ids:
        items:
          type: integer
        type: array
    type: object
  http.hostFieldValueForm:
    properties:
      field_idents:
        items:
          type: string
        type: array
      host_ids:
        items:
          type: integer
        type: array
    required:
    - field_idents
    - host_ids
    type: object
  http.hostFilterForm:
    properties:
      cate:
        description: 类别精确匹配，支持多选
        items:
          type: string
        type: array
      cpu_model:
        description: CPU型号精确匹配，支持多选
        items:
          type: string
        type: array
      fields:
        description: 自定义字段过滤
        items:
          properties:
            field_ident:
              description: 字段标识符
              type: string
            is_like:
              description: 是否使用模糊匹配
              type: boolean
            values:
              description: 字段值，支持多选
              items:
                type: string
              type: array
          type: object
        type: array
      gpu_model:
        description: GPU型号精确匹配，支持多选
        items:
          type: string
        type: array
      idc:
        description: IDC精确匹配，支持多选
        items:
          type: string
        type: array
      ident:
        description: 标识符模糊匹配
        type: string
      ip:
        description: 基础字段过滤
        items:
          type: string
        type: array
      kernel_version:
        description: 内核版本模糊匹配
        type: string
      model:
        description: 型号精确匹配，支持多选
        items:
          type: string
        type: array
      name:
        description: 名称模糊匹配
        type: string
      note:
        description: 备注模糊匹配
        type: string
      os_version:
        description: 操作系统版本模糊匹配
        type: string
      rack:
        description: 机架精确匹配，支持多选
        items:
          type: string
        type: array
      sn:
        description: SN模糊匹配
        type: string
      tenant:
        description: 租户精确匹配，支持多选
        items:
          type: string
        type: array
      zone:
        description: 可用区精确匹配，支持多选
        items:
          type: string
        type: array
    type: object
  http.hostNodeForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      nodeids:
        items:
          type: integer
        type: array
    type: object
  http.hostNoteForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      note:
        type: string
    type: object
  http.hostTenantForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      tenant:
        type: string
    type: object
  http.hostUpdateForm:
    properties:
      cate:
        type: string
      cpu:
        type: string
      cpu_model:
        type: string
      disk:
        type: string
      gpu:
        type: string
      gpu_model:
        type: string
      idc:
        type: string
      kernel_version:
        type: string
      manufacturer:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      note:
        type: string
      os_version:
        type: string
      rack:
        type: string
      zone:
        type: string
    type: object
  http.idsOrIpsForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      ips:
        items:
          type: string
        type: array
    type: object
  models.Host:
    properties:
      cate:
        type: string
      clock:
        type: integer
      cpu:
        type: string
      cpu_model:
        type: string
      disk:
        type: string
      gpu:
        type: string
      gpu_model:
        type: string
      id:
        type: integer
      idc:
        type: string
      ident:
        type: string
      ip:
        type: string
      kernel_version:
        type: string
      manufacturer:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      note:
        type: string
      os_version:
        type: string
      rack:
        type: string
      sn:
        type: string
      tenant:
        type: string
      zone:
        type: string
    type: object
  models.HostField:
    properties:
      field_cate:
        type: string
      field_extra:
        type: string
      field_ident:
        type: string
      field_name:
        type: string
      field_required:
        type: integer
      field_type:
        type: string
      id:
        type: integer
    type: object
  models.HostFieldValue:
    properties:
      field_ident:
        type: string
      field_value:
        type: string
      host_id:
        type: integer
      id:
        type: integer
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Arboris AMS 模块 API 文档
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: AMS API
  version: "1.0"
paths:
  /api/ams-ce/host:
    post:
      consumes:
      - application/json
      description: 添加单台主机设备，支持基础字段和自定义字段
      parameters:
      - description: 主机添加参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostAddForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建的主机信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Host'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "409":
          description: IP或标识符已存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 添加主机
      tags:
      - 主机管理
  /api/ams-ce/host/{id}:
    get:
      consumes:
      - application/json
      description: 根据主机ID获取主机详细信息
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 主机信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Host'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 主机不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取单个主机信息
      tags:
      - 主机管理
    put:
      consumes:
      - application/json
      description: 更新单台主机的详细信息，支持基础字段。IP、ID和标识符不可修改
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      - description: 主机更新参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostUpdateForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新后的主机信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Host'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 主机不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新主机信息
      tags:
      - 主机管理
  /api/ams-ce/host/{id}/fields:
    get:
      consumes:
      - application/json
      description: 获取指定主机的所有字段值
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 主机字段值列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.HostFieldValue'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 主机不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取主机字段值
      tags:
      - 主机字段管理
    put:
      consumes:
      - application/json
      description: 批量更新指定主机的字段值
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      - description: 主机字段值列表
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/models.HostFieldValue'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 主机不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新主机字段值
      tags:
      - 主机字段管理
  /api/ams-ce/host/filter:
    post:
      consumes:
      - application/json
      description: 根据基础字段和自定义字段多维度过滤主机列表，支持分页，返回结果包含节点路径信息和自定义字段值
      parameters:
      - description: 过滤条件
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostFilterForm'
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 过滤后的主机列表，包含节点路径和自定义字段值
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.HostFilterResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 多维度过滤主机
      tags:
      - 主机管理
  /api/ams-ce/hosts:
    delete:
      consumes:
      - application/json
      description: 批量删除主机设备，支持通过ID或IP删除。如果主机已绑定到节点，将阻止删除并返回绑定信息
      parameters:
      - description: 主机删除参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.idsOrIpsForm'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 部分主机已绑定到节点，无法删除
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/http.HostBindingInfo'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除主机
      tags:
      - 主机管理
    get:
      consumes:
      - application/json
      description: 获取主机列表，支持分页、搜索和批量筛选
      parameters:
      - description: 租户
        in: query
        name: tenant
        type: string
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - description: 批量IP列表，逗号分隔
        in: query
        name: batch
        type: string
      - default: ip
        description: 搜索字段
        in: query
        name: field
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 主机列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.HostListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取主机列表
      tags:
      - 主机管理
  /api/ams-ce/hosts/cate:
    put:
      consumes:
      - application/json
      description: 批量修改主机设备的类别信息
      parameters:
      - description: 主机类别修改参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostCateForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改主机类别
      tags:
      - 主机管理
  /api/ams-ce/hosts/csv/failed/{import_id}:
    get:
      consumes:
      - application/json
      description: 根据导入批次ID下载导入失败的CSV数据，包含原始数据和错误信息
      parameters:
      - description: 导入批次ID
        in: path
        name: import_id
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 失败数据CSV文件
          schema:
            type: file
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 导入批次不存在或已过期
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 下载导入失败的数据
      tags:
      - 主机管理
  /api/ams-ce/hosts/csv/import:
    post:
      consumes:
      - multipart/form-data
      description: 通过CSV或Excel文件批量导入主机数据，支持基础字段和自定义字段。支持.csv、.xls、.xlsx格式。最大支持1000行数据导入。可选择将导入的主机挂载到指定节点。返回导入结果统计信息，详细错误信息可通过失败数据下载接口获取
      parameters:
      - description: CSV或Excel文件（支持.csv、.xls、.xlsx格式，最大1000行数据，不包括标题行）
        in: formData
        name: file
        required: true
        type: file
      - description: 节点ID列表，用逗号分隔。如果指定，导入的主机将自动挂载到这些节点下
        in: formData
        name: node_ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 导入完成，返回统计信息。如果有失败记录，err字段会包含错误提示，import_id用于下载失败数据
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.ImportResult'
              type: object
        "400":
          description: 请求参数错误（如文件格式错误、超过1000行限制等）
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 批量导入主机
      tags:
      - 主机管理
  /api/ams-ce/hosts/csv/template:
    get:
      consumes:
      - application/json
      description: 动态根据自定义字段生成主机导入模板文件，支持CSV和Excel格式
      parameters:
      - default: csv
        description: 文件格式
        enum:
        - csv
        - xlsx
        in: query
        name: format
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 模板文件
          schema:
            type: file
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 下载主机导入模板
      tags:
      - 主机管理
  /api/ams-ce/hosts/field/{id}:
    delete:
      consumes:
      - application/json
      description: 删除主机字段定义
      parameters:
      - description: 字段ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 字段不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除主机字段
      tags:
      - 主机字段管理
    get:
      consumes:
      - application/json
      description: 根据字段ID获取主机字段定义详情
      parameters:
      - description: 字段ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 主机字段信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.HostField'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 字段不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取单个主机字段
      tags:
      - 主机字段管理
    put:
      consumes:
      - application/json
      description: 更新主机字段定义信息（字段类型不可修改）
      parameters:
      - description: 字段ID
        in: path
        name: id
        required: true
        type: integer
      - description: 主机字段更新参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.HostField'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 字段不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新主机字段
      tags:
      - 主机字段管理
  /api/ams-ce/hosts/fields:
    get:
      consumes:
      - application/json
      description: 获取所有主机自定义字段定义列表
      produces:
      - application/json
      responses:
        "200":
          description: 主机字段列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.HostField'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取主机字段列表
      tags:
      - 主机字段管理
    post:
      consumes:
      - application/json
      description: 创建新的主机自定义字段定义
      parameters:
      - description: 主机字段参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.HostField'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建主机字段
      tags:
      - 主机字段管理
  /api/ams-ce/hosts/fields/values:
    post:
      consumes:
      - application/json
      description: 获取多个主机的指定自定义字段值，支持分页
      parameters:
      - description: 查询参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostFieldValueForm'
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 1
        description: 页码
        in: query
        name: p
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 主机字段值列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.HostFieldValueListResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 批量获取主机字段值
      tags:
      - 主机字段管理
  /api/ams-ce/hosts/node:
    put:
      consumes:
      - application/json
      description: 将主机设备挂载到多个节点，支持一台主机同时挂载到多个节点
      parameters:
      - description: 主机节点挂载参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostNodeForm'
      produces:
      - application/json
      responses:
        "200":
          description: 挂载成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 主机挂载到节点
      tags:
      - 主机管理
  /api/ams-ce/hosts/note:
    put:
      consumes:
      - application/json
      description: 批量修改主机设备的备注信息
      parameters:
      - description: 主机备注修改参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostNoteForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改主机备注
      tags:
      - 主机管理
  /api/ams-ce/hosts/tenant:
    put:
      consumes:
      - application/json
      description: 管理员修改主机设备的租户，相当于分配设备到指定租户
      parameters:
      - description: 主机租户修改参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostTenantForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改主机租户
      tags:
      - 主机管理
  /api/ams-ce/ping:
    get:
      consumes:
      - application/json
      description: 检查 AMS 服务是否正常运行
      produces:
      - text/plain
      responses:
        "200":
          description: pong
          schema:
            type: string
      summary: 健康检查
      tags:
      - 系统管理
securityDefinitions:
  ApiKeyAuth:
    description: API Token 认证
    in: header
    name: X-User-Token
    type: apiKey
  CookieAuth:
    description: Cookie 认证 (ecmc-sid)
    in: header
    name: Cookie
    type: apiKey
swagger: "2.0"
