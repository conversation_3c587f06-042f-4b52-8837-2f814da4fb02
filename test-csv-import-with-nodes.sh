#!/bin/bash

# 测试CSV导入机器到指定节点的脚本

echo "=== 测试CSV导入机器到指定节点功能 ==="

# 创建测试CSV文件
echo "1. 创建测试CSV文件..."
cat > /tmp/test_hosts.csv << 'EOF'
序列号,IP地址,标识符,主机名,操作系统版本,内核版本,CPU,频率,内存,磁盘,类型,租户,核心数,GPU,型号,IDC,可用区,机架,厂商,备注
SN001,*************,test-host-001,test-server-01,CentOS 7,3.10.0-1160,Intel Xeon,2.4GHz,32GB,500GB SSD,host,inner,8,NVIDIA GTX 1080,Dell R740,北京机房,Zone-A,A01,Dell,测试主机1
SN002,*************,test-host-002,test-server-02,Ubuntu 20.04,5.4.0-74,AMD EPYC,2.2GHz,64GB,1TB SSD,host,inner,16,NVIDIA RTX 3080,HP DL380,上海机房,Zone-B,B02,HP,测试主机2
SN003,*************,test-host-003,test-server-03,CentOS 8,4.18.0-348,Intel Core i7,3.0GHz,16GB,256GB SSD,host,inner,4,集成显卡,Lenovo ThinkServer,深圳机房,Zone-C,C03,Lenovo,测试主机3
EOF

echo "CSV文件内容:"
head -2 /tmp/test_hosts.csv
echo ""

# 测试场景1：导入到有效的叶子节点
echo "2. 测试场景1：导入到有效的叶子节点..."
echo "假设节点ID 1 是一个有效的叶子节点"

RESULT1=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts.csv" \
  -F "node_ids=1")

echo "导入结果1:"
echo "$RESULT1" | jq . 2>/dev/null || echo "$RESULT1"
echo ""

# 测试场景2：导入到无效节点（不存在的节点）
echo "3. 测试场景2：导入到不存在的节点..."

RESULT2=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts.csv" \
  -F "node_ids=99999")

echo "导入结果2（应该有节点绑定错误）:"
echo "$RESULT2" | jq . 2>/dev/null || echo "$RESULT2"
echo ""

# 测试场景3：导入到多个节点（包含有效和无效的）
echo "4. 测试场景3：导入到多个节点..."

RESULT3=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts.csv" \
  -F "node_ids=1,99999,2")

echo "导入结果3（混合场景）:"
echo "$RESULT3" | jq . 2>/dev/null || echo "$RESULT3"
echo ""

# 测试场景4：不指定节点的导入
echo "5. 测试场景4：不指定节点的导入..."

RESULT4=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts.csv")

echo "导入结果4（无节点绑定）:"
echo "$RESULT4" | jq . 2>/dev/null || echo "$RESULT4"
echo ""

# 检查AMS日志
echo "6. 检查AMS日志..."
echo "最新的CSV导入相关日志:"
tail -20 logs/ams/INFO.log | grep -E "(CSV|import|bind|node)" || echo "没有找到相关日志"

echo ""
echo "7. 检查数据库中的主机记录..."
echo "最新创建的主机:"
mysql -h ********** -u root -pzTbMNuT9nbjDvEt2 -e "
USE arboris_ams; 
SELECT id, ip, ident, name, created_at 
FROM host 
WHERE ident LIKE 'test-host-%' 
ORDER BY created_at DESC 
LIMIT 5;" 2>/dev/null || echo "无法查询数据库"

echo ""
echo "8. 检查资源绑定关系..."
echo "最新的资源绑定:"
mysql -h ********** -u root -pzTbMNuT9nbjDvEt2 -e "
USE arboris_rdb;
SELECT nr.node_id, nr.res_id, r.ident, r.name 
FROM node_resource nr 
JOIN resource r ON nr.res_id = r.id 
WHERE r.ident LIKE 'test-host-%' 
ORDER BY r.id DESC 
LIMIT 10;" 2>/dev/null || echo "无法查询绑定关系"

echo ""
echo "期望看到的结果:"
echo "1. 场景1：成功导入，如果节点1是叶子节点且权限正确"
echo "2. 场景2：主机创建成功，但有node_binding_errors提示节点不存在"
echo "3. 场景3：主机创建成功，部分节点绑定成功，部分失败"
echo "4. 场景4：主机创建成功，无节点绑定"
echo "5. 日志中应该看到详细的节点验证和绑定过程"

# 清理测试文件
rm -f /tmp/test_hosts.csv

echo ""
echo "测试完成！"
