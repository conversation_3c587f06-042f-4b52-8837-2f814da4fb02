#!/bin/bash

# 生成所有模块的Swagger文档
# 确保各模块文档独立，不互相包含

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${CYAN}Arboris Swagger文档生成工具${NC}"
    echo ""
    echo -e "${BLUE}用法:${NC}"
    echo "  $0 [选项]"
    echo ""
    echo -e "${BLUE}选项:${NC}"
    echo "  -h, --help     显示此帮助信息"
    echo "  -a, --ams      只生成AMS模块文档"
    echo "  -r, --rdb      只生成RDB模块文档"
    echo "  -j, --jumpserver-sync  只生成JumpServer同步模块文档"
    echo "  -c, --clean    清理所有文档后退出"
    echo "  -v, --verbose  显示详细输出"
    echo ""
    echo -e "${BLUE}示例:${NC}"
    echo "  $0              # 生成所有模块文档"
    echo "  $0 --ams        # 只生成AMS文档"
    echo "  $0 --rdb        # 只生成RDB文档"
    echo "  $0 --jumpserver-sync  # 只生成JumpServer同步文档"
    echo "  $0 --clean      # 清理所有文档"
}

# 解析命令行参数
parse_args() {
    GENERATE_AMS=true
    GENERATE_RDB=true
    GENERATE_JUMPSERVER_SYNC=true
    CLEAN_ONLY=false
    VERBOSE=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--ams)
                GENERATE_AMS=true
                GENERATE_RDB=false
                GENERATE_JUMPSERVER_SYNC=false
                shift
                ;;
            -r|--rdb)
                GENERATE_AMS=false
                GENERATE_RDB=true
                GENERATE_JUMPSERVER_SYNC=false
                shift
                ;;
            -j|--jumpserver-sync)
                GENERATE_AMS=false
                GENERATE_RDB=false
                GENERATE_JUMPSERVER_SYNC=true
                shift
                ;;
            -c|--clean)
                CLEAN_ONLY=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            *)
                echo -e "${RED}❌ 未知选项: $1${NC}"
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 清理所有文档
clean_all_docs() {
    echo -e "${BLUE}清理所有Swagger文档...${NC}"
    
    local cleaned=false
    
    if [[ -d "docs-ams" ]]; then
        rm -rf docs-ams
        echo -e "${GREEN}✅ AMS文档已清理${NC}"
        cleaned=true
    fi

    if [[ -d "docs-rdb" ]]; then
        rm -rf docs-rdb
        echo -e "${GREEN}✅ RDB文档已清理${NC}"
        cleaned=true
    fi

    if [[ -d "docs-jumpserver-sync" ]]; then
        rm -rf docs-jumpserver-sync
        echo -e "${GREEN}✅ JumpServer同步文档已清理${NC}"
        cleaned=true
    fi
    
    if [[ "$cleaned" == "false" ]]; then
        echo -e "${YELLOW}⚠️  没有找到需要清理的文档${NC}"
    fi
}

# 检查脚本是否存在
check_scripts() {
    local missing_scripts=()

    if [[ "$GENERATE_AMS" == "true" && ! -f "generate-ams-swagger.sh" ]]; then
        missing_scripts+=("generate-ams-swagger.sh")
    fi

    if [[ "$GENERATE_RDB" == "true" && ! -f "generate-rdb-swagger.sh" ]]; then
        missing_scripts+=("generate-rdb-swagger.sh")
    fi

    if [[ "$GENERATE_JUMPSERVER_SYNC" == "true" && ! -f "generate-jumpserver-sync-swagger.sh" ]]; then
        missing_scripts+=("generate-jumpserver-sync-swagger.sh")
    fi

    if [[ ${#missing_scripts[@]} -gt 0 ]]; then
        echo -e "${RED}❌ 缺少生成脚本: ${missing_scripts[*]}${NC}"
        exit 1
    fi
}

# 生成AMS文档
generate_ams_docs() {
    echo -e "${CYAN}=== 生成AMS模块文档 ===${NC}"
    
    if [[ "$VERBOSE" == "true" ]]; then
        ./generate-ams-swagger.sh
    else
        ./generate-ams-swagger.sh > /tmp/ams-swagger.log 2>&1
    fi
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ AMS文档生成成功${NC}"
        return 0
    else
        echo -e "${RED}❌ AMS文档生成失败${NC}"
        if [[ "$VERBOSE" == "false" ]]; then
            echo -e "${YELLOW}详细错误信息请查看: /tmp/ams-swagger.log${NC}"
        fi
        return 1
    fi
}

# 生成RDB文档
generate_rdb_docs() {
    echo -e "${CYAN}=== 生成RDB模块文档 ===${NC}"

    if [[ "$VERBOSE" == "true" ]]; then
        ./generate-rdb-swagger.sh
    else
        ./generate-rdb-swagger.sh > /tmp/rdb-swagger.log 2>&1
    fi

    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ RDB文档生成成功${NC}"
        return 0
    else
        echo -e "${RED}❌ RDB文档生成失败${NC}"
        if [[ "$VERBOSE" == "false" ]]; then
            echo -e "${YELLOW}详细错误信息请查看: /tmp/rdb-swagger.log${NC}"
        fi
        return 1
    fi
}

# 生成JumpServer同步文档
generate_jumpserver_sync_docs() {
    echo -e "${CYAN}=== 生成JumpServer同步模块文档 ===${NC}"

    if [[ "$VERBOSE" == "true" ]]; then
        ./generate-jumpserver-sync-swagger.sh
    else
        ./generate-jumpserver-sync-swagger.sh > /tmp/jumpserver-sync-swagger.log 2>&1
    fi

    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ JumpServer同步文档生成成功${NC}"
        return 0
    else
        echo -e "${RED}❌ JumpServer同步文档生成失败${NC}"
        if [[ "$VERBOSE" == "false" ]]; then
            echo -e "${YELLOW}详细错误信息请查看: /tmp/jumpserver-sync-swagger.log${NC}"
        fi
        return 1
    fi
}

# 显示最终结果
show_final_results() {
    echo ""
    echo -e "${GREEN}🎉 Swagger文档生成完成！${NC}"
    echo ""
    
    if [[ "$GENERATE_AMS" == "true" && -d "docs-ams" ]]; then
        echo -e "${BLUE}📁 AMS文档:${NC}"
        echo "   位置: docs-ams/"
        if [[ -f "docs-ams/swagger.json" ]]; then
            local ams_size=$(du -h docs-ams/swagger.json | cut -f1)
            echo "   大小: $ams_size"
        fi
        echo "   访问: http://localhost:8002/swagger/index.html"
        echo ""
    fi
    
    if [[ "$GENERATE_RDB" == "true" && -d "docs-rdb" ]]; then
        echo -e "${BLUE}📁 RDB文档:${NC}"
        echo "   位置: docs-rdb/"
        if [[ -f "docs-rdb/swagger.json" ]]; then
            local rdb_size=$(du -h docs-rdb/swagger.json | cut -f1)
            echo "   大小: $rdb_size"
        fi
        echo "   访问: http://localhost:8001/swagger/index.html"
        echo ""
    fi

    if [[ "$GENERATE_JUMPSERVER_SYNC" == "true" && -d "docs-jumpserver-sync" ]]; then
        echo -e "${BLUE}📁 JumpServer同步文档:${NC}"
        echo "   位置: docs-jumpserver-sync/"
        if [[ -f "docs-jumpserver-sync/swagger.json" ]]; then
            local js_size=$(du -h docs-jumpserver-sync/swagger.json | cut -f1)
            echo "   大小: $js_size"
        fi
        echo "   访问: http://localhost:8003/swagger/index.html"
        echo ""
    fi

    echo -e "${BLUE}🔧 常用命令:${NC}"
    echo "   重新生成所有: ./generate-all-swagger.sh"
    echo "   只生成AMS:   ./generate-all-swagger.sh --ams"
    echo "   只生成RDB:   ./generate-all-swagger.sh --rdb"
    echo "   只生成JumpServer同步: ./generate-all-swagger.sh --jumpserver-sync"
    echo "   清理文档:    ./generate-all-swagger.sh --clean"
}

# 主函数
main() {
    echo -e "${CYAN}Arboris Swagger文档生成工具${NC}"
    echo ""
    
    parse_args "$@"
    
    # 如果只是清理，执行清理后退出
    if [[ "$CLEAN_ONLY" == "true" ]]; then
        clean_all_docs
        echo -e "${GREEN}✅ 清理完成${NC}"
        exit 0
    fi
    
    # 检查必要的脚本
    check_scripts
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    local success_count=0
    local total_count=0
    
    # 生成AMS文档
    if [[ "$GENERATE_AMS" == "true" ]]; then
        total_count=$((total_count + 1))
        if generate_ams_docs; then
            success_count=$((success_count + 1))
        fi
        echo ""
    fi
    
    # 生成RDB文档
    if [[ "$GENERATE_RDB" == "true" ]]; then
        total_count=$((total_count + 1))
        if generate_rdb_docs; then
            success_count=$((success_count + 1))
        fi
        echo ""
    fi

    # 生成JumpServer同步文档
    if [[ "$GENERATE_JUMPSERVER_SYNC" == "true" ]]; then
        total_count=$((total_count + 1))
        if generate_jumpserver_sync_docs; then
            success_count=$((success_count + 1))
        fi
        echo ""
    fi
    
    # 计算耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 显示结果
    if [[ $success_count -eq $total_count ]]; then
        show_final_results
        echo -e "${GREEN}⏱️  总耗时: ${duration}秒${NC}"
    else
        echo -e "${RED}❌ 部分文档生成失败 ($success_count/$total_count)${NC}"
        echo -e "${YELLOW}💡 请检查错误信息并重试${NC}"
        exit 1
    fi
}

# 设置脚本权限并执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 确保脚本有执行权限
    chmod +x generate-ams-swagger.sh 2>/dev/null || true
    chmod +x generate-rdb-swagger.sh 2>/dev/null || true
    chmod +x generate-jumpserver-sync-swagger.sh 2>/dev/null || true

    main "$@"
fi
