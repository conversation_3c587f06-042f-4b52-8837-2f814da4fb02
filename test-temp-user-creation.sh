#!/bin/bash

# 测试临时用户创建功能的脚本

echo "=== 测试临时用户创建功能 ==="

# 计算时间戳
CURRENT_TIME=$(date +%s)
START_TIME=$((CURRENT_TIME + 60))      # 1分钟后开始
END_TIME=$((CURRENT_TIME + 3600))      # 1小时后结束

echo "测试参数:"
echo "  当前时间: $(date -r $CURRENT_TIME)"
echo "  开始时间: $(date -r $START_TIME)"
echo "  结束时间: $(date -r $END_TIME)"
echo ""

# 1. 测试创建长期用户
echo "1. 测试创建长期用户..."
cat > /tmp/long_term_user.json << EOF
{
  "username": "test_long_user_$(date +%s)",
  "password": "Test123456!",
  "dispname": "测试长期用户",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "typ": 0,
  "status": 0,
  "organization": "测试组织"
}
EOF

echo "长期用户数据:"
cat /tmp/long_term_user.json | jq .
echo ""

LONG_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/long_term_user.json)

echo "长期用户创建结果:"
echo "$LONG_USER_RESULT" | jq . 2>/dev/null || echo "$LONG_USER_RESULT"
echo ""

# 2. 测试创建临时用户
echo "2. 测试创建临时用户..."
cat > /tmp/temp_user.json << EOF
{
  "username": "test_temp_user_$(date +%s)",
  "password": "Test123456!",
  "dispname": "测试临时用户",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "typ": 1,
  "status": 0,
  "organization": "测试组织",
  "active_begin": $START_TIME,
  "active_end": $END_TIME
}
EOF

echo "临时用户数据:"
cat /tmp/temp_user.json | jq .
echo ""

TEMP_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/temp_user.json)

echo "临时用户创建结果:"
echo "$TEMP_USER_RESULT" | jq . 2>/dev/null || echo "$TEMP_USER_RESULT"
echo ""

# 3. 测试创建无效的临时用户（结束时间早于开始时间）
echo "3. 测试创建无效的临时用户（结束时间早于开始时间）..."
cat > /tmp/invalid_temp_user.json << EOF
{
  "username": "test_invalid_user_$(date +%s)",
  "password": "Test123456!",
  "dispname": "测试无效临时用户",
  "email": "<EMAIL>",
  "phone": "13800138002",
  "typ": 1,
  "status": 0,
  "organization": "测试组织",
  "active_begin": $END_TIME,
  "active_end": $START_TIME
}
EOF

INVALID_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/invalid_temp_user.json)

echo "无效临时用户创建结果（应该失败）:"
echo "$INVALID_USER_RESULT" | jq . 2>/dev/null || echo "$INVALID_USER_RESULT"
echo ""

# 4. 测试创建过期的临时用户
echo "4. 测试创建过期的临时用户（结束时间早于当前时间）..."
PAST_TIME=$((CURRENT_TIME - 3600))  # 1小时前

cat > /tmp/expired_temp_user.json << EOF
{
  "username": "test_expired_user_$(date +%s)",
  "password": "Test123456!",
  "dispname": "测试过期临时用户",
  "email": "<EMAIL>",
  "phone": "13800138003",
  "typ": 1,
  "status": 0,
  "organization": "测试组织",
  "active_begin": $PAST_TIME,
  "active_end": $((PAST_TIME + 1800))
}
EOF

EXPIRED_USER_RESULT=$(curl -s -X POST "http://localhost:8002/api/rdb/users" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/expired_temp_user.json)

echo "过期临时用户创建结果（应该失败）:"
echo "$EXPIRED_USER_RESULT" | jq . 2>/dev/null || echo "$EXPIRED_USER_RESULT"
echo ""

# 5. 检查RDB日志
echo "5. 检查RDB日志中的用户创建记录..."
echo "最新的用户创建日志:"
tail -10 logs/rdb/INFO.log | grep -E "(UserCreate|user.*create)" || echo "没有找到用户创建日志"

echo ""
echo "期望看到的结果:"
echo "1. 长期用户创建成功"
echo "2. 临时用户创建成功，日志中包含有效期信息"
echo "3. 无效临时用户创建失败，提示时间设置错误"
echo "4. 过期临时用户创建失败，提示结束时间必须晚于当前时间"

# 清理临时文件
rm -f /tmp/long_term_user.json /tmp/temp_user.json /tmp/invalid_temp_user.json /tmp/expired_temp_user.json
