# JumpServer账号管理和账号模板管理使用指南

## 📋 **目录**
1. [快速开始](#快速开始)
2. [账号模板管理](#账号模板管理)
3. [账号管理](#账号管理)
4. [绑定关系管理](#绑定关系管理)
5. [查询和统计](#查询和统计)
6. [完整使用流程](#完整使用流程)

## 🚀 **快速开始**

### 前置条件
- 已有RDB资源数据（通过AMS同步的主机资源）
- 已获取API访问权限和认证信息

### 基础概念
- **Resource**: RDB中的资源，对应AMS中的主机
- **Account**: JumpServer账号，可以绑定到多个资源
- **Template**: 账号模板，用于批量创建标准化账号
- **Binding**: 账号与资源的绑定关系

---

## 📝 **账号模板管理**

### 1. 创建账号模板

**API**: `POST /api/rdb/jumpserver/account-templates`

```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/account-templates" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "Linux Root模板",
    "username": "root",
    "secret_type": "password",
    "secret": "DefaultPassword123",
    "privileged": true,
    "is_active": true,
    "comment": "Linux服务器root账号模板"
  }'
```

**响应示例**:
```json
{
  "err": "",
  "dat": {
    "id": 1,
    "uuid": "template-uuid-001",
    "name": "Linux Root模板",
    "username": "root",
    "secret_type": "password",
    "privileged": true,
    "is_active": true,
    "comment": "Linux服务器root账号模板",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

### 2. 查询账号模板列表

**API**: `GET /api/rdb/jumpserver/account-templates`

```bash
curl "http://your-rdb-server/api/rdb/jumpserver/account-templates?limit=20&offset=0" \
  -H "Authorization: Bearer your-token"
```

### 3. 更新账号模板

**API**: `PUT /api/rdb/jumpserver/account-templates/{id}`

```bash
curl -X PUT "http://your-rdb-server/api/rdb/jumpserver/account-templates/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "Linux Root模板（更新）",
    "secret": "NewPassword123",
    "comment": "更新后的Linux服务器root账号模板"
  }'
```

### 4. 应用账号模板到资源

**API**: `POST /api/rdb/jumpserver/account-templates/apply`

```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/account-templates/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "template_id": 1,
    "resource_ids": [1001, 1002, 1003],
    "username": "admin",
    "comment": "应用模板创建的管理员账号"
  }'
```

**响应示例**:
```json
{
  "err": "",
  "dat": {
    "account": {
      "id": 10,
      "uuid": "account-uuid-010",
      "name": "Linux Root模板",
      "username": "admin",
      "source": "template"
    },
    "results": [
      {"resource_id": 1001, "state": "success"},
      {"resource_id": 1002, "state": "success"},
      {"resource_id": 1003, "state": "error", "error": "该资源上已存在相同用户名的账号"}
    ]
  }
}
```

---

## 👤 **账号管理**

### 1. 创建账号（支持单个和批量）

**API**: `POST /api/rdb/jumpserver/accounts`

#### 单个资源创建
```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "数据库管理员账号",
    "username": "dbadmin",
    "secret_type": "password",
    "secret": "DbAdmin123",
    "resource_ids": [1001],
    "privileged": false,
    "is_active": true,
    "comment": "数据库服务器管理员账号"
  }'
```

#### 批量资源创建
```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "Web服务账号",
    "username": "webuser",
    "secret_type": "password",
    "secret": "WebUser123",
    "resource_ids": [1001, 1002, 1003, 1004],
    "privileged": false,
    "is_active": true,
    "comment": "Web服务器应用账号"
  }'
```

**响应示例**:
```json
{
  "err": "",
  "dat": {
    "account": {
      "id": 20,
      "uuid": "account-uuid-020",
      "name": "Web服务账号",
      "username": "webuser",
      "secret_type": "password",
      "is_active": true
    },
    "results": [
      {"resource_id": 1001, "state": "success"},
      {"resource_id": 1002, "state": "success"},
      {"resource_id": 1003, "state": "success"},
      {"resource_id": 1004, "state": "error", "error": "该资源上已存在相同用户名的账号"}
    ]
  }
}
```

### 2. 查询账号列表

**API**: `GET /api/rdb/jumpserver/accounts`

#### 查询所有账号
```bash
curl "http://your-rdb-server/api/rdb/jumpserver/accounts?limit=20&offset=0" \
  -H "Authorization: Bearer your-token"
```

#### 按资源查询账号
```bash
curl "http://your-rdb-server/api/rdb/jumpserver/accounts?resource_id=1001&limit=20" \
  -H "Authorization: Bearer your-token"
```

#### 搜索账号
```bash
curl "http://your-rdb-server/api/rdb/jumpserver/accounts?search=root&limit=20" \
  -H "Authorization: Bearer your-token"
```

### 3. 获取单个账号详情

**API**: `GET /api/rdb/jumpserver/accounts/{id}`

```bash
curl "http://your-rdb-server/api/rdb/jumpserver/accounts/20" \
  -H "Authorization: Bearer your-token"
```

### 4. 更新账号

**API**: `PUT /api/rdb/jumpserver/accounts/{id}`

```bash
curl -X PUT "http://your-rdb-server/api/rdb/jumpserver/accounts/20" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "Web服务账号（更新）",
    "secret": "NewWebUser123",
    "comment": "更新后的Web服务器应用账号",
    "is_active": true
  }'
```

### 5. 删除账号

**API**: `DELETE /api/rdb/jumpserver/accounts/{id}`

```bash
curl -X DELETE "http://your-rdb-server/api/rdb/jumpserver/accounts/20" \
  -H "Authorization: Bearer your-token"
```

---

## 🔗 **绑定关系管理**

### 1. 绑定账号到资源

**API**: `POST /api/rdb/jumpserver/accounts/bind`

```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts/bind" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "account_ids": [10, 20],
    "resource_ids": [1005, 1006]
  }'
```

### 2. 解绑账号与资源

**API**: `POST /api/rdb/jumpserver/accounts/unbind`

```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts/unbind" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "account_ids": [10],
    "resource_ids": [1005]
  }'
```

### 3. 查询账号绑定关系

**API**: `GET /api/rdb/jumpserver/accounts/{id}/bindings`

```bash
curl "http://your-rdb-server/api/rdb/jumpserver/accounts/20/bindings" \
  -H "Authorization: Bearer your-token"
```

**响应示例**:
```json
{
  "err": "",
  "dat": [
    {
      "id": 100,
      "account_id": 20,
      "resource_id": 1001,
      "asset_uuid": "host-1001",
      "binding_type": "manual",
      "is_active": true,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## 📊 **查询和统计**

### 1. 查询资源的账号列表

**API**: `GET /api/rdb/jumpserver/resources/accounts`

```bash
curl "http://your-rdb-server/api/rdb/jumpserver/resources/accounts?resource_id=1001" \
  -H "Authorization: Bearer your-token"
```

### 2. 获取账号统计信息

**API**: `GET /api/rdb/jumpserver/accounts/statistics`

```bash
curl "http://your-rdb-server/api/rdb/jumpserver/accounts/statistics" \
  -H "Authorization: Bearer your-token"
```

**响应示例**:
```json
{
  "err": "",
  "dat": {
    "total_accounts": 150,
    "active_accounts": 120,
    "inactive_accounts": 30,
    "template_accounts": 80,
    "manual_accounts": 70,
    "total_assets": 50,
    "total_bindings": 300
  }
}
```

### 3. 同步账号到JumpServer

**API**: `POST /api/rdb/jumpserver/accounts/{id}/sync`

```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts/20/sync" \
  -H "Authorization: Bearer your-token"
```

---

## 🔄 **完整使用流程**

### 场景1：为新项目批量创建标准账号

#### 步骤1：创建账号模板
```bash
# 创建Linux服务器标准模板
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/account-templates" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "项目标准账号模板",
    "username": "appuser",
    "secret_type": "password",
    "secret": "ProjectApp123",
    "privileged": false,
    "is_active": true,
    "comment": "项目应用标准账号模板"
  }'
```

#### 步骤2：应用模板到项目资源
```bash
# 假设项目有5台服务器，resource_ids为 [2001, 2002, 2003, 2004, 2005]
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/account-templates/apply" \
  -H "Content-Type: application/json" \
  -d '{
    "template_id": 1,
    "resource_ids": [2001, 2002, 2003, 2004, 2005],
    "comment": "项目标准账号批量创建"
  }'
```

#### 步骤3：验证创建结果
```bash
# 查询统计信息
curl "http://your-rdb-server/api/rdb/jumpserver/accounts/statistics"

# 查询特定资源的账号
curl "http://your-rdb-server/api/rdb/jumpserver/resources/accounts?resource_id=2001"
```

### 场景2：为特殊需求创建自定义账号

#### 步骤1：直接创建账号
```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "数据库DBA账号",
    "username": "dba",
    "secret_type": "password",
    "secret": "DbaSecure123",
    "resource_ids": [2001, 2003],
    "privileged": true,
    "is_active": true,
    "comment": "数据库管理员专用账号"
  }'
```

#### 步骤2：后续添加更多资源绑定
```bash
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts/bind" \
  -H "Content-Type: application/json" \
  -d '{
    "account_ids": [30],
    "resource_ids": [2004, 2005]
  }'
```

### 场景3：账号维护和管理

#### 定期查询和监控
```bash
# 查询所有活跃账号
curl "http://your-rdb-server/api/rdb/jumpserver/accounts?limit=100"

# 查询统计信息
curl "http://your-rdb-server/api/rdb/jumpserver/accounts/statistics"

# 查询特定账号的绑定关系
curl "http://your-rdb-server/api/rdb/jumpserver/accounts/30/bindings"
```

#### 账号更新和维护
```bash
# 更新账号密码
curl -X PUT "http://your-rdb-server/api/rdb/jumpserver/accounts/30" \
  -H "Content-Type: application/json" \
  -d '{
    "secret": "NewDbaSecure456",
    "comment": "密码已更新"
  }'

# 同步到JumpServer
curl -X POST "http://your-rdb-server/api/rdb/jumpserver/accounts/30/sync"
```

## 📚 **最佳实践**

1. **使用模板**: 对于标准化账号，优先使用模板批量创建
2. **合理命名**: 账号名称要清晰表达用途和权限级别
3. **定期维护**: 定期检查账号状态和绑定关系
4. **权限控制**: 根据实际需要设置privileged权限
5. **密码管理**: 使用强密码，定期更新
6. **监控统计**: 定期查看统计信息，了解账号使用情况

## ❗ **注意事项**

1. **资源ID**: 确保resource_id存在于RDB中
2. **用户名唯一性**: 同一资源上不能有重复用户名的账号
3. **权限验证**: 确保有足够的API访问权限
4. **错误处理**: 注意检查API响应中的错误信息
5. **同步状态**: 创建后可能需要手动触发同步到JumpServer

---

## 📖 **API参考**

### 账号模板管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/rdb/jumpserver/account-templates` | 获取模板列表 |
| POST | `/api/rdb/jumpserver/account-templates` | 创建模板 |
| GET | `/api/rdb/jumpserver/account-templates/{id}` | 获取模板详情 |
| PUT | `/api/rdb/jumpserver/account-templates/{id}` | 更新模板 |
| DELETE | `/api/rdb/jumpserver/account-templates/{id}` | 删除模板 |
| POST | `/api/rdb/jumpserver/account-templates/apply` | 应用模板 |

### 账号管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/rdb/jumpserver/accounts` | 获取账号列表 |
| POST | `/api/rdb/jumpserver/accounts` | 创建账号（支持批量） |
| GET | `/api/rdb/jumpserver/accounts/{id}` | 获取账号详情 |
| PUT | `/api/rdb/jumpserver/accounts/{id}` | 更新账号 |
| DELETE | `/api/rdb/jumpserver/accounts/{id}` | 删除账号 |
| POST | `/api/rdb/jumpserver/accounts/{id}/sync` | 同步账号 |

### 绑定关系管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/rdb/jumpserver/accounts/bind` | 绑定账号到资源 |
| POST | `/api/rdb/jumpserver/accounts/unbind` | 解绑账号与资源 |
| GET | `/api/rdb/jumpserver/accounts/{id}/bindings` | 获取账号绑定关系 |

### 查询统计API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/rdb/jumpserver/resources/accounts` | 获取资源的账号列表 |
| GET | `/api/rdb/jumpserver/accounts/statistics` | 获取统计信息 |

## 🔧 **高级用法**

### 1. 批量操作脚本示例

#### Bash脚本：批量创建项目账号
```bash
#!/bin/bash

# 配置
RDB_SERVER="http://your-rdb-server"
TOKEN="your-api-token"
PROJECT_RESOURCES=(2001 2002 2003 2004 2005)

# 创建项目标准模板
echo "创建项目标准模板..."
TEMPLATE_RESPONSE=$(curl -s -X POST "$RDB_SERVER/api/rdb/jumpserver/account-templates" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "项目标准模板",
    "username": "appuser",
    "secret_type": "password",
    "secret": "ProjectApp123",
    "privileged": false,
    "is_active": true,
    "comment": "项目应用标准账号模板"
  }')

TEMPLATE_ID=$(echo $TEMPLATE_RESPONSE | jq -r '.dat.id')
echo "模板创建成功，ID: $TEMPLATE_ID"

# 应用模板到所有项目资源
echo "应用模板到项目资源..."
RESOURCE_IDS_JSON=$(printf '%s\n' "${PROJECT_RESOURCES[@]}" | jq -R . | jq -s .)

curl -s -X POST "$RDB_SERVER/api/rdb/jumpserver/account-templates/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{
    \"template_id\": $TEMPLATE_ID,
    \"resource_ids\": $RESOURCE_IDS_JSON,
    \"comment\": \"项目标准账号批量创建\"
  }" | jq '.'

echo "批量创建完成！"
```

#### Python脚本：账号状态监控
```python
#!/usr/bin/env python3
import requests
import json
from datetime import datetime

class AccountManager:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def get_statistics(self):
        """获取账号统计信息"""
        response = requests.get(
            f'{self.base_url}/api/rdb/jumpserver/accounts/statistics',
            headers=self.headers
        )
        return response.json()

    def get_accounts(self, limit=100, offset=0, search=None, resource_id=None):
        """获取账号列表"""
        params = {'limit': limit, 'offset': offset}
        if search:
            params['search'] = search
        if resource_id:
            params['resource_id'] = resource_id

        response = requests.get(
            f'{self.base_url}/api/rdb/jumpserver/accounts',
            headers=self.headers,
            params=params
        )
        return response.json()

    def create_account(self, name, username, resource_ids, **kwargs):
        """创建账号"""
        data = {
            'name': name,
            'username': username,
            'resource_ids': resource_ids,
            **kwargs
        }

        response = requests.post(
            f'{self.base_url}/api/rdb/jumpserver/accounts',
            headers=self.headers,
            json=data
        )
        return response.json()

    def monitor_accounts(self):
        """监控账号状态"""
        print(f"=== 账号监控报告 - {datetime.now()} ===")

        # 获取统计信息
        stats = self.get_statistics()
        if stats['err'] == '':
            data = stats['dat']
            print(f"总账号数: {data['total_accounts']}")
            print(f"活跃账号: {data['active_accounts']}")
            print(f"非活跃账号: {data['inactive_accounts']}")
            print(f"模板账号: {data['template_accounts']}")
            print(f"手动账号: {data['manual_accounts']}")
            print(f"总资源数: {data['total_assets']}")
            print(f"总绑定关系: {data['total_bindings']}")

        # 检查非活跃账号
        accounts = self.get_accounts(limit=1000)
        if accounts['err'] == '':
            inactive_accounts = [
                acc for acc in accounts['dat']['list']
                if not acc['is_active']
            ]

            if inactive_accounts:
                print(f"\n⚠️  发现 {len(inactive_accounts)} 个非活跃账号:")
                for acc in inactive_accounts:
                    print(f"  - {acc['name']} ({acc['username']}) - ID: {acc['id']}")

# 使用示例
if __name__ == "__main__":
    manager = AccountManager("http://your-rdb-server", "your-api-token")

    # 监控账号状态
    manager.monitor_accounts()

    # 创建测试账号
    result = manager.create_account(
        name="测试账号",
        username="testuser",
        resource_ids=[1001, 1002],
        secret_type="password",
        secret="TestPass123",
        is_active=True,
        comment="Python脚本创建的测试账号"
    )

    print(f"\n账号创建结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
```

### 2. 错误处理和重试机制

#### 带重试的账号创建函数
```python
import time
import random

def create_account_with_retry(manager, account_data, max_retries=3):
    """带重试机制的账号创建"""
    for attempt in range(max_retries):
        try:
            result = manager.create_account(**account_data)

            if result['err'] == '':
                print(f"✅ 账号创建成功: {account_data['name']}")
                return result
            else:
                print(f"❌ 账号创建失败: {result['err']}")
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    print(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)

        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                print(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

    print(f"❌ 账号创建最终失败: {account_data['name']}")
    return None
```

### 3. 配置文件管理

#### 账号配置文件示例 (accounts_config.yaml)
```yaml
# 账号模板配置
templates:
  - name: "Linux标准用户模板"
    username: "appuser"
    secret_type: "password"
    secret: "StandardApp123"
    privileged: false
    is_active: true
    comment: "Linux服务器标准应用用户模板"

  - name: "Linux管理员模板"
    username: "admin"
    secret_type: "password"
    secret: "AdminSecure456"
    privileged: true
    is_active: true
    comment: "Linux服务器管理员模板"

# 项目账号配置
projects:
  - name: "电商项目"
    resources: [3001, 3002, 3003, 3004]
    accounts:
      - template: "Linux标准用户模板"
        username: "ecommerce"
        comment: "电商项目应用账号"
      - template: "Linux管理员模板"
        username: "ecommerce_admin"
        comment: "电商项目管理员账号"

  - name: "数据分析项目"
    resources: [4001, 4002]
    accounts:
      - name: "数据分析账号"
        username: "analyst"
        secret_type: "password"
        secret: "DataAnalyst789"
        privileged: false
        comment: "数据分析专用账号"
```

## 🚨 **故障排除**

### 常见错误及解决方案

#### 1. "该资源上已存在相同用户名的账号"
**原因**: 尝试在同一资源上创建重复用户名的账号
**解决**:
- 检查现有账号：`GET /api/rdb/jumpserver/resources/accounts?resource_id=xxx`
- 使用不同的用户名或删除冲突的账号

#### 2. "资源不存在"
**原因**: 指定的resource_id在RDB中不存在
**解决**:
- 检查资源列表：`GET /api/rdb/resources`
- 确认resource_id正确

#### 3. "账号模板不存在"
**原因**: 指定的template_id不存在
**解决**:
- 检查模板列表：`GET /api/rdb/jumpserver/account-templates`
- 先创建模板再使用

#### 4. 权限不足
**原因**: API token权限不够
**解决**:
- 检查token是否有效
- 确认用户有相应的操作权限

### 调试技巧

1. **启用详细日志**: 在请求中添加调试参数
2. **检查响应状态**: 注意HTTP状态码和错误信息
3. **分步验证**: 先测试单个操作，再进行批量操作
4. **使用统计API**: 定期检查数据一致性

## 📈 **性能优化建议**

1. **批量操作**: 优先使用批量API而不是循环调用单个API
2. **分页查询**: 大量数据查询时使用limit和offset参数
3. **缓存结果**: 对不经常变化的数据（如模板列表）进行缓存
4. **异步处理**: 大批量操作时考虑异步处理
5. **错误重试**: 实现指数退避的重试机制

这个完整的指南涵盖了从基础使用到高级自动化的所有场景，帮助你高效管理JumpServer账号！
