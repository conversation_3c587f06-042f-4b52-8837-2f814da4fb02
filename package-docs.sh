#!/bin/bash

# 打包API文档脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 打包Arboris API文档 ===${NC}"

# 创建临时目录
TEMP_DIR="arboris-api-docs-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$TEMP_DIR"

# 检查文档是否存在
if [[ ! -f "docs-ams/swagger.yaml" ]]; then
    echo "❌ AMS文档不存在，请先运行: ./generate-ams-swagger.sh"
    exit 1
fi

if [[ ! -f "docs-rdb/swagger.yaml" ]]; then
    echo "❌ RDB文档不存在，请先运行: ./generate-rdb-swagger.sh"
    exit 1
fi

# 复制文档文件
echo "📄 复制文档文件..."
cp docs-ams/swagger.yaml "$TEMP_DIR/ams-api.yaml"
cp docs-ams/swagger.json "$TEMP_DIR/ams-api.json"
cp docs-rdb/swagger.yaml "$TEMP_DIR/rdb-api.yaml"
cp docs-rdb/swagger.json "$TEMP_DIR/rdb-api.json"

# 创建README
echo "📝 创建说明文档..."
cat > "$TEMP_DIR/README.md" << 'EOF'
# Arboris API 文档包

## 📖 文档说明

本包包含 Arboris 系统的完整 API 文档：

### AMS模块（资产管理系统）
- `ams-api.yaml` - YAML格式文档
- `ams-api.json` - JSON格式文档
- **服务地址**: http://localhost:8002
- **API前缀**: `/api/ams-ce`

### RDB模块（权限管理系统）  
- `rdb-api.yaml` - YAML格式文档
- `rdb-api.json` - JSON格式文档
- **服务地址**: http://localhost:8000
- **API前缀**: `/api/rdb`

## 🔍 查看方式

### 方式1：在线Swagger编辑器（推荐）
1. 访问 https://editor.swagger.io/
2. 点击 `File` → `Import file`
3. 选择对应的 `.yaml` 文件
4. 右侧即可查看美观的API文档

### 方式2：本地Docker运行
```bash
# 查看AMS文档
docker run -p 8080:8080 -v $(pwd):/docs \
  -e SWAGGER_JSON=/docs/ams-api.yaml \
  swaggerapi/swagger-ui

# 查看RDB文档  
docker run -p 8081:8080 -v $(pwd):/docs \
  -e SWAGGER_JSON=/docs/rdb-api.yaml \
  swaggerapi/swagger-ui
```

### 方式3：VSCode插件
1. 安装 `Swagger Viewer` 插件
2. 打开 `.yaml` 文件
3. 右键选择 `Preview Swagger`

### 方式4：Postman导入
1. 打开Postman
2. 点击 `Import`
3. 选择 `.json` 文件
4. 自动生成接口集合

## 🔐 认证说明

所有接口都需要认证，支持两种方式：

### Cookie认证
```
Cookie: ecmc-sid=your-session-id
```

### Token认证  
```
X-User-Token: your-api-token
```

## 📋 接口概览

### AMS模块主要功能
- 主机管理（增删改查）
- 主机字段管理
- 主机导入导出
- 主机分类管理

### RDB模块主要功能
- 用户管理
- 角色权限管理
- 团队管理
- 资源管理
- 账号管理
- 操作日志

## 🚀 快速开始

1. **查看接口文档**: 使用上述任一方式查看
2. **获取认证**: 通过登录接口获取session或token
3. **调用接口**: 在请求头中携带认证信息
4. **处理响应**: 所有接口返回统一格式的JSON响应

## 📞 技术支持

如有问题，请联系开发团队。
EOF

# 创建快速启动脚本
echo "🚀 创建快速启动脚本..."
cat > "$TEMP_DIR/view-docs.sh" << 'EOF'
#!/bin/bash

echo "选择要查看的文档："
echo "1. AMS模块文档"
echo "2. RDB模块文档"
echo "3. 同时查看两个模块"

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "启动AMS文档查看器..."
        docker run -p 8080:8080 -v $(pwd):/docs \
          -e SWAGGER_JSON=/docs/ams-api.yaml \
          swaggerapi/swagger-ui
        ;;
    2)
        echo "启动RDB文档查看器..."
        docker run -p 8080:8080 -v $(pwd):/docs \
          -e SWAGGER_JSON=/docs/rdb-api.yaml \
          swaggerapi/swagger-ui
        ;;
    3)
        echo "启动AMS文档查看器 (端口8080)..."
        docker run -d -p 8080:8080 -v $(pwd):/docs \
          -e SWAGGER_JSON=/docs/ams-api.yaml \
          swaggerapi/swagger-ui
        
        echo "启动RDB文档查看器 (端口8081)..."
        docker run -d -p 8081:8080 -v $(pwd):/docs \
          -e SWAGGER_JSON=/docs/rdb-api.yaml \
          swaggerapi/swagger-ui
        
        echo "文档已启动："
        echo "AMS: http://localhost:8080"
        echo "RDB: http://localhost:8081"
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
EOF

chmod +x "$TEMP_DIR/view-docs.sh"

# 显示文件信息
echo ""
echo -e "${BLUE}📊 文档包内容:${NC}"
ls -la "$TEMP_DIR/"

echo ""
echo -e "${BLUE}📦 文件大小:${NC}"
du -h "$TEMP_DIR"/*

# 打包
echo ""
echo "📦 打包文档..."
tar -czf "${TEMP_DIR}.tar.gz" -C "$TEMP_DIR" .

echo ""
echo -e "${GREEN}✅ 文档包创建成功！${NC}"
echo ""
echo -e "${BLUE}📁 文档包位置:${NC} ${TEMP_DIR}.tar.gz"
echo -e "${BLUE}📄 包含文件:${NC}"
echo "  - ams-api.yaml/json (AMS模块文档)"
echo "  - rdb-api.yaml/json (RDB模块文档)"  
echo "  - README.md (使用说明)"
echo "  - view-docs.sh (快速查看脚本)"

echo ""
echo -e "${BLUE}🚀 用户使用方式:${NC}"
echo "1. 解压文档包: tar -xzf ${TEMP_DIR}.tar.gz"
echo "2. 查看README.md了解详细说明"
echo "3. 访问 https://editor.swagger.io/ 导入yaml文件"
echo "4. 或运行 ./view-docs.sh 本地查看"

# 清理临时目录
rm -rf "$TEMP_DIR"

echo ""
echo -e "${GREEN}🎉 完成！${NC}"
