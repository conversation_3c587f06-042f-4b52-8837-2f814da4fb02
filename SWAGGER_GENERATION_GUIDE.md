# Swagger文档生成指南

本项目提供了独立的Swagger文档生成脚本，确保AMS和RDB模块的API文档互不包含，保持模块间的清晰边界。

## 📁 **文件结构**

```
arboris/
├── generate-ams-swagger.sh     # AMS模块文档生成脚本
├── generate-rdb-swagger.sh     # RDB模块文档生成脚本
├── generate-all-swagger.sh     # 通用文档生成脚本
├── docs-ams/                   # AMS模块文档输出目录
│   ├── docs.go
│   ├── swagger.json
│   └── swagger.yaml
└── docs-rdb/                   # RDB模块文档输出目录
    ├── docs.go
    ├── swagger.json
    └── swagger.yaml
```

## 🚀 **快速开始**

### 1. 生成所有模块文档
```bash
./generate-all-swagger.sh
```

### 2. 只生成AMS模块文档
```bash
./generate-ams-swagger.sh
# 或者
./generate-all-swagger.sh --ams
```

### 3. 只生成RDB模块文档
```bash
./generate-rdb-swagger.sh
# 或者
./generate-all-swagger.sh --rdb
```

### 4. 清理所有文档
```bash
./generate-all-swagger.sh --clean
```

## 📋 **脚本功能特性**

### ✅ **模块隔离**
- **AMS文档**: 只包含 `/api/ams-ce/*` 接口，排除RDB模块
- **RDB文档**: 只包含 `/api/rdb/*` 接口，排除AMS模块
- **独立生成**: 各模块文档完全独立，互不影响

### ✅ **智能检测**
- 自动检测并安装 `swag` 工具
- 验证项目结构和必要文件
- 检查生成的文档完整性
- 验证接口路径正确性

### ✅ **用户友好**
- 彩色输出，清晰易读
- 详细的错误信息和解决建议
- 文件大小和生成时间统计
- 多种查看方式指引

## 🔧 **详细用法**

### generate-all-swagger.sh 选项

```bash
# 显示帮助信息
./generate-all-swagger.sh --help

# 详细输出模式
./generate-all-swagger.sh --verbose

# 组合使用
./generate-all-swagger.sh --ams --verbose
```

### 单独脚本使用

```bash
# AMS模块
./generate-ams-swagger.sh

# RDB模块  
./generate-rdb-swagger.sh
```

## 📖 **查看文档的方式**

### 1. 在线查看（推荐）
启动对应服务后访问：
- **AMS**: http://localhost:8002/swagger/index.html
- **RDB**: http://localhost:8000/swagger/index.html

### 2. 本地文件
- **JSON格式**: `docs-ams/swagger.json`, `docs-rdb/swagger.json`
- **YAML格式**: `docs-ams/swagger.yaml`, `docs-rdb/swagger.yaml`

### 3. 在线编辑器
访问 https://editor.swagger.io/ 并导入本地生成的文件

## ⚙️ **配置说明**

### AMS模块配置
- **主文件**: `src/modules/ams/ams.go`
- **扫描目录**: `src/modules/ams/`
- **输出目录**: `docs-ams/`
- **服务端口**: 8002
- **API前缀**: `/api/ams-ce`

### RDB模块配置
- **主文件**: `src/modules/rdb/rdb.go`
- **扫描目录**: `src/modules/rdb/`
- **输出目录**: `docs-rdb/`
- **服务端口**: 8000
- **API前缀**: `/api/rdb`

## 🛠️ **故障排除**

### 常见问题

#### 1. swag工具未找到
```bash
# 手动安装
go install github.com/swaggo/swag/cmd/swag@latest

# 检查安装
which swag
```

#### 2. 文档生成失败
```bash
# 检查Go语法
cd src/modules/ams && go build .
cd src/modules/rdb && go build .

# 查看详细错误
./generate-all-swagger.sh --verbose
```

#### 3. 文档包含错误的接口
检查Swagger注释中的路径是否正确：
```go
// @Router /api/ams-ce/hosts [get]  // AMS接口
// @Router /api/rdb/users [get]     // RDB接口
```

#### 4. 权限问题
```bash
# 设置执行权限
chmod +x generate-*.sh
```

### 错误日志位置
- AMS生成日志: `/tmp/ams-swagger.log`
- RDB生成日志: `/tmp/rdb-swagger.log`

## 📊 **文档质量检查**

生成的文档会自动进行以下检查：

### ✅ **文件完整性**
- `docs.go` - Go包文件
- `swagger.json` - JSON格式文档
- `swagger.yaml` - YAML格式文档

### ✅ **内容正确性**
- AMS文档不包含 `/api/rdb` 路径
- RDB文档不包含 `/api/ams-ce` 路径
- 包含正确的模块接口路径

### ✅ **格式规范**
- 符合OpenAPI 2.0规范
- 包含完整的接口定义
- 正确的认证配置

## 🔄 **CI/CD集成**

### GitHub Actions示例
```yaml
name: Generate Swagger Docs
on: [push, pull_request]

jobs:
  swagger:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      
      - name: Generate Swagger Docs
        run: |
          ./generate-all-swagger.sh --verbose
      
      - name: Upload Docs
        uses: actions/upload-artifact@v3
        with:
          name: swagger-docs
          path: docs-*/
```

## 📝 **开发建议**

### 1. 接口注释规范
```go
// @Summary 接口简要描述
// @Description 详细描述
// @Tags 标签分组
// @Accept json
// @Produce json
// @Param id path int true "参数描述"
// @Success 200 {object} ResponseType "成功响应"
// @Failure 400 {object} ErrorResponse "错误响应"
// @Router /api/module/path [method]
```

### 2. 定期更新文档
```bash
# 开发完成后更新文档
./generate-all-swagger.sh

# 提交前检查
git add docs-ams/ docs-rdb/
git commit -m "docs: update swagger documentation"
```

### 3. 版本管理
- 文档版本与代码版本保持同步
- 重大API变更时更新版本号
- 保留历史版本文档供参考

## 🎯 **最佳实践**

1. **模块独立**: 确保各模块文档完全独立
2. **定期生成**: 代码变更后及时更新文档
3. **质量检查**: 生成后验证文档完整性
4. **版本控制**: 将生成的文档纳入版本控制
5. **团队协作**: 统一使用提供的脚本工具
