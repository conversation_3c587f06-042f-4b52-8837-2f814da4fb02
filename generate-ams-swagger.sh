#!/bin/bash

# 生成AMS模块的Swagger文档
# 确保不包含RDB模块的内容

set -e  # 遇到错误立即退出

echo "=== 生成AMS模块Swagger文档 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查swag工具是否存在
check_swag_tool() {
    echo -e "${BLUE}检查swag工具...${NC}"

    # 检查多个可能的路径
    SWAG_PATHS=(
        "$(go env GOPATH)/bin/swag"
        "$HOME/go/bin/swag"
    )

    # 添加which命令的结果（如果存在）
    local which_result=$(which swag 2>/dev/null || echo "")
    if [[ -n "$which_result" ]]; then
        SWAG_PATHS+=("$which_result")
    fi

    for path in "${SWAG_PATHS[@]}"; do
        if [[ -n "$path" && -x "$path" ]]; then
            SWAG_CMD="$path"
            echo -e "${GREEN}✅ 找到swag工具: $SWAG_CMD${NC}"
            return 0
        fi
    done
    
    echo -e "${YELLOW}⚠️  swag工具未找到，正在安装...${NC}"
    go install github.com/swaggo/swag/cmd/swag@latest
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 安装swag工具失败${NC}"
        exit 1
    fi
    
    SWAG_CMD="$(go env GOPATH)/bin/swag"
    if [[ ! -x "$SWAG_CMD" ]]; then
        SWAG_CMD="$HOME/go/bin/swag"
    fi
    
    echo -e "${GREEN}✅ swag工具安装成功: $SWAG_CMD${NC}"
}

# 检查项目结构
check_project_structure() {
    echo -e "${BLUE}检查项目结构...${NC}"
    
    if [[ ! -d "src/modules/ams" ]]; then
        echo -e "${RED}❌ AMS模块目录不存在: src/modules/ams${NC}"
        exit 1
    fi
    
    if [[ ! -f "src/modules/ams/ams.go" ]]; then
        echo -e "${RED}❌ AMS主文件不存在: src/modules/ams/ams.go${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目结构检查通过${NC}"
}

# 清理旧文档
clean_old_docs() {
    echo -e "${BLUE}清理旧的AMS文档...${NC}"
    
    if [[ -d "docs-ams" ]]; then
        rm -rf docs-ams/*
        echo -e "${GREEN}✅ 旧文档已清理${NC}"
    else
        mkdir -p docs-ams
        echo -e "${GREEN}✅ 创建文档目录${NC}"
    fi
}

# 生成Swagger文档
generate_swagger() {
    echo -e "${BLUE}正在生成AMS Swagger文档...${NC}"
    
    # 进入AMS模块目录
    cd src/modules/ams
    
    # 生成swagger文档，只扫描AMS模块
    $SWAG_CMD init \
        --generalInfo ams.go \
        --dir . \
        --output ../../../docs-ams \
        --parseDependency \
        --parseInternal \
        --exclude ../rdb \
        --exclude ../../modules/rdb \
        --exclude ../../../modules/rdb
    
    local exit_code=$?
    
    # 返回原目录
    cd ../../..
    
    return $exit_code
}

# 验证生成的文档
validate_docs() {
    echo -e "${BLUE}验证生成的文档...${NC}"
    
    local required_files=("docs.go" "swagger.json" "swagger.yaml")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "docs-ams/$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo -e "${RED}❌ 缺少文件: ${missing_files[*]}${NC}"
        return 1
    fi
    
    # 检查文档内容是否包含RDB接口（不应该包含）
    if grep -q "/api/rdb" docs-ams/swagger.json 2>/dev/null; then
        echo -e "${YELLOW}⚠️  警告: 文档中包含RDB接口路径${NC}"
    fi
    
    # 检查是否包含AMS接口（应该包含）
    if ! grep -q "/api/ams-ce" docs-ams/swagger.json 2>/dev/null; then
        echo -e "${RED}❌ 错误: 文档中未找到AMS接口路径${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 文档验证通过${NC}"
    return 0
}

# 显示结果
show_results() {
    echo ""
    echo -e "${GREEN}🎉 AMS Swagger文档生成成功！${NC}"
    echo ""
    echo -e "${BLUE}📁 文档位置:${NC}"
    echo "   docs-ams/"
    echo ""
    echo -e "${BLUE}📄 生成的文件:${NC}"
    
    if [[ -d "docs-ams" ]]; then
        ls -la docs-ams/ | while read line; do
            echo "   $line"
        done
    fi
    
    echo ""
    echo -e "${BLUE}📊 文件大小:${NC}"
    if [[ -f "docs-ams/swagger.json" ]]; then
        local json_size=$(du -h docs-ams/swagger.json | cut -f1)
        echo "   swagger.json: $json_size"
    fi
    if [[ -f "docs-ams/swagger.yaml" ]]; then
        local yaml_size=$(du -h docs-ams/swagger.yaml | cut -f1)
        echo "   swagger.yaml: $yaml_size"
    fi
    
    echo ""
    echo -e "${BLUE}🚀 查看文档的方式:${NC}"
    echo "   1. 在线查看: http://localhost:8002/swagger/index.html (需要启动AMS服务)"
    echo "   2. 本地文件: docs-ams/swagger.yaml 或 docs-ams/swagger.json"
    echo "   3. 在线编辑器: https://editor.swagger.io/ (导入本地文件)"
    echo ""
    echo -e "${BLUE}🔧 重新生成命令:${NC}"
    echo "   ./generate-ams-swagger.sh"
}

# 主函数
main() {
    echo -e "${BLUE}开始生成AMS模块Swagger文档...${NC}"
    echo ""
    
    check_swag_tool
    check_project_structure
    clean_old_docs
    
    if generate_swagger; then
        if validate_docs; then
            show_results
        else
            echo -e "${RED}❌ 文档验证失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ Swagger文档生成失败${NC}"
        echo ""
        echo -e "${YELLOW}💡 可能的解决方案:${NC}"
        echo "   1. 检查Go语法错误"
        echo "   2. 检查Swagger注释格式"
        echo "   3. 确保所有依赖包已安装"
        echo "   4. 查看详细错误信息"
        exit 1
    fi
}

# 执行主函数
main "$@"
