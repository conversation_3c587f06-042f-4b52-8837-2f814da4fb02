#!/bin/bash

# 测试简化后的CSV导入机器功能（不支持节点绑定）

echo "=== 测试简化的CSV导入机器功能 ==="

# 创建测试CSV文件
echo "1. 创建测试CSV文件..."
cat > /tmp/test_hosts_simple.csv << 'EOF'
序列号,IP地址,标识符,主机名,操作系统版本,内核版本,CPU,频率,内存,磁盘,类型,租户,核心数,GPU,型号,IDC,可用区,机架,厂商,备注
SN101,*************,simple-host-001,simple-server-01,CentOS 7,3.10.0-1160,Intel Xeon,2.4GHz,32GB,500GB SSD,host,inner,8,NVIDIA GTX 1080,Dell R740,北京机房,Zone-A,A01,Dell,简化测试主机1
SN102,*************,simple-host-002,simple-server-02,Ubuntu 20.04,5.4.0-74,AMD EPYC,2.2GHz,64GB,1TB SSD,host,inner,16,NVIDIA RTX 3080,HP DL380,上海机房,Zone-B,B02,HP,简化测试主机2
SN103,*************,simple-host-003,simple-server-03,CentOS 8,4.18.0-348,Intel Core i7,3.0GHz,16GB,256GB SSD,host,inner,4,集成显卡,Lenovo ThinkServer,深圳机房,Zone-C,C03,Lenovo,简化测试主机3
EOF

echo "CSV文件内容:"
head -2 /tmp/test_hosts_simple.csv
echo ""

# 测试场景1：正常导入（不指定节点）
echo "2. 测试场景1：正常导入主机..."

RESULT1=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts_simple.csv")

echo "导入结果1:"
echo "$RESULT1" | jq . 2>/dev/null || echo "$RESULT1"
echo ""

# 测试场景2：尝试传递node_ids参数（应该被忽略）
echo "3. 测试场景2：传递node_ids参数（应该被忽略）..."

RESULT2=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts_simple.csv" \
  -F "node_ids=1,2,3")

echo "导入结果2（node_ids参数应该被忽略）:"
echo "$RESULT2" | jq . 2>/dev/null || echo "$RESULT2"
echo ""

# 测试场景3：导入包含错误数据的CSV
echo "4. 测试场景3：导入包含错误数据的CSV..."

cat > /tmp/test_hosts_error.csv << 'EOF'
序列号,IP地址,标识符,主机名,操作系统版本,内核版本,CPU,频率,内存,磁盘,类型,租户,核心数,GPU,型号,IDC,可用区,机架,厂商,备注
SN201,,error-host-001,error-server-01,CentOS 7,3.10.0-1160,Intel Xeon,2.4GHz,32GB,500GB SSD,host,inner,8,NVIDIA GTX 1080,Dell R740,北京机房,Zone-A,A01,Dell,错误主机1-缺少IP
SN202,*************,,error-server-02,Ubuntu 20.04,5.4.0-74,AMD EPYC,2.2GHz,64GB,1TB SSD,host,inner,16,NVIDIA RTX 3080,HP DL380,上海机房,Zone-B,B02,HP,错误主机2-缺少标识符
SN203,*************,error-host-003,error-server-03,CentOS 8,4.18.0-348,Intel Core i7,3.0GHz,16GB,256GB SSD,host,inner,4,集成显卡,Lenovo ThinkServer,深圳机房,Zone-C,C03,Lenovo,正常主机3
EOF

RESULT3=$(curl -s -X POST "http://localhost:8001/api/ams-ce/hosts/csv/import" \
  -H "Cookie: ecmc-sid=test-session" \
  -F "file=@/tmp/test_hosts_error.csv")

echo "导入结果3（包含错误数据）:"
echo "$RESULT3" | jq . 2>/dev/null || echo "$RESULT3"
echo ""

# 检查AMS日志
echo "5. 检查AMS日志..."
echo "最新的CSV导入相关日志:"
tail -15 logs/ams/INFO.log | grep -E "(CSV|import|host)" || echo "没有找到相关日志"

echo ""
echo "6. 检查数据库中的主机记录..."
echo "最新创建的主机:"
mysql -h ********** -u root -pzTbMNuT9nbjDvEt2 -e "
USE arboris_ams; 
SELECT id, ip, ident, name, created_at 
FROM host 
WHERE ident LIKE 'simple-host-%' OR ident LIKE 'error-host-%'
ORDER BY created_at DESC 
LIMIT 10;" 2>/dev/null || echo "无法查询数据库"

echo ""
echo "7. 验证没有自动创建资源绑定..."
echo "检查是否有相关资源记录:"
mysql -h ********** -u root -pzTbMNuT9nbjDvEt2 -e "
USE arboris_rdb;
SELECT COUNT(*) as resource_count
FROM resource 
WHERE ident LIKE 'simple-host-%' OR ident LIKE 'error-host-%';" 2>/dev/null || echo "无法查询资源表"

echo ""
echo "期望看到的结果:"
echo "1. 场景1和2：主机成功导入，返回success_count=3, failed_count=0"
echo "2. 场景2：node_ids参数被忽略，不影响导入结果"
echo "3. 场景3：部分成功，success_count=1, failed_count=2，有import_id用于下载失败数据"
echo "4. 日志中应该看到主机导入过程，但没有节点绑定相关日志"
echo "5. 数据库中应该有新的主机记录"
echo "6. 不应该自动创建资源记录（因为没有节点绑定）"

# 清理测试文件
rm -f /tmp/test_hosts_simple.csv /tmp/test_hosts_error.csv

echo ""
echo "测试完成！"
