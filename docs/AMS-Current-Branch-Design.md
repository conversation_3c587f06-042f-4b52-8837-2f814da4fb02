# AMS模块详细设计文档 (当前分支)

## 1. 模块概述

### 1.1 模块定位
AMS (Asset Management System) 是Arboris系统的资产管理模块，专注于主机设备的全生命周期管理。作为系统的基础数据层，AMS为上层业务模块提供标准化的主机资产数据服务，并与JumpServer堡垒机系统进行深度集成。

### 1.2 核心职责
- **主机资产管理**：主机设备的增删改查、批量操作、多维度查询
- **自定义字段扩展**：支持灵活的字段定义和值管理
- **流式批量导入**：CSV/Excel文件流式解析，支持大规模数据迁移，内存优化
- **JumpServer集成**：主机数据自动同步到JumpServer堡垒机
- **用户权限管理**：用户创建、授权、主机访问权限控制
- **账号模板管理**：批量账号绑定和管理
- **数据验证**：IP地址、标识符唯一性等业务规则验证

### 1.3 技术架构
```
┌─────────────────┐    HTTP API    ┌─────────────────┐    Sync API    ┌─────────────────┐
│   前端界面      │ ──────────────→ │   AMS Module    │ ──────────────→ │   JumpServer    │
│   - 主机管理    │                │   - HTTP Layer  │                │   - 资产管理    │
│   - 批量导入    │                │   - Business    │                │   - 用户管理    │
│   - 用户管理    │                │   - Sync Layer  │                │   - 账号管理    │
│   - 权限管理    │                │   - Data Layer  │                └─────────────────┘
└─────────────────┘                └─────────────────┘
                                           │
                                           ▼
                                   ┌─────────────────┐
                                   │   MySQL DB      │
                                   │   - host        │
                                   │   - host_field  │
                                   │   - field_value │
                                   │   - failed_data │
                                   └─────────────────┘
```

## 2. 数据模型设计

### 2.1 主机表 (host)
```sql
CREATE TABLE `host` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主机ID',
  `sn` varchar(255) DEFAULT NULL COMMENT '序列号',
  `ip` varchar(64) NOT NULL COMMENT 'IP地址',
  `ident` varchar(255) NOT NULL COMMENT '标识符',
  `name` varchar(255) NOT NULL COMMENT '主机名',
  `os_version` varchar(255) DEFAULT NULL COMMENT '操作系统版本',
  `kernel_version` varchar(255) DEFAULT NULL COMMENT '内核版本',
  `cpu_model` varchar(255) DEFAULT NULL COMMENT 'CPU型号',
  `gpu_model` varchar(255) DEFAULT NULL COMMENT 'GPU型号',
  `zone` varchar(255) DEFAULT NULL COMMENT '可用区',
  `rack` varchar(255) DEFAULT NULL COMMENT '机架',
  `note` varchar(255) DEFAULT NULL COMMENT '备注',
  `model` varchar(255) DEFAULT NULL COMMENT '设备型号',
  `manufacturer` varchar(255) DEFAULT NULL COMMENT '制造商',
  `idc` varchar(255) DEFAULT NULL COMMENT '数据中心',
  `gpu` varchar(255) DEFAULT NULL COMMENT 'GPU信息',
  `cpu` varchar(255) DEFAULT NULL COMMENT 'CPU信息',
  `mem` varchar(255) DEFAULT NULL COMMENT '内存信息',
  `disk` varchar(255) DEFAULT NULL COMMENT '磁盘信息',
  `cate` varchar(255) DEFAULT NULL COMMENT '分类',
  `clock` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  `tenant` varchar(255) DEFAULT NULL COMMENT '租户',
  `jumpserver_asset_id` varchar(255) DEFAULT NULL COMMENT 'JumpServer资产ID',
  `jumpserver_node_id` varchar(255) DEFAULT NULL COMMENT 'JumpServer节点ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ip` (`ip`),
  UNIQUE KEY `idx_ident` (`ident`),
  KEY `idx_tenant` (`tenant`),
  KEY `idx_cate` (`cate`),
  KEY `idx_jumpserver_asset_id` (`jumpserver_asset_id`)
);
```

### 2.2 主机查询参数结构
```go
type HostQueryParams struct {
    ID            []string // 主机ID，精确匹配
    SN            []string // 序列号，模糊匹配
    IP            []string // IP地址，精确匹配
    Ident         []string // 标识符，模糊匹配
    Name          []string // 主机名，模糊匹配
    OSVersion     []string // 操作系统版本，模糊匹配
    KernelVersion []string // 内核版本，模糊匹配
    CPUModel      []string // CPU型号，模糊匹配
    GPUModel      []string // GPU型号，模糊匹配
    Zone          []string // 可用区，精确匹配
    Rack          []string // 机架，模糊匹配
    Note          []string // 备注，模糊匹配
    Model         []string // 型号，模糊匹配
    Manufacturer  []string // 厂商，模糊匹配
    IDC           []string // IDC机房，精确匹配
    GPU           []string // GPU卡数，精确匹配
    CPU           []string // CPU核数，精确匹配
    Mem           []string // 内存，精确匹配
    Disk          []string // 磁盘，精确匹配
    Cate          []string // 类别，精确匹配
    Tenant        []string // 租户，精确匹配
}
```

## 3. API接口设计

### 3.1 主机管理接口

#### 3.1.1 获取主机列表
**接口路径**: `GET /api/ams-ce/hosts`

**特色功能**:
- 支持所有字段的批量查询 (逗号分隔)
- 支持未同步主机查询 (`unsynced=true`)
- 精确匹配 vs 模糊匹配的智能选择

**请求参数**:
```
limit: 分页大小 (默认20)
p: 页码 (默认1)
id: 主机ID查询，支持批量 "1,2,3"
sn: 序列号查询，支持批量 "SN001,SN002"
ip: IP地址查询，支持批量 "***********,***********"
ident: 标识符查询，支持批量 "host-001,host-002"
name: 主机名查询，支持批量 "web-server,db-server"
zone: 可用区查询，支持批量 "us-west-1a,us-west-1b"
unsynced: 只查询未同步到JumpServer的主机 "true"
... (支持所有主机字段的批量查询)
```

**响应格式**:
```json
{
  "err": null,
  "dat": {
    "list": [
      {
        "id": 1,
        "sn": "SN001",
        "ip": "*************",
        "ident": "web-server-01",
        "name": "Web服务器01",
        "jumpserver_asset_id": "asset-uuid-123",
        "jumpserver_node_id": "node-uuid-456"
      }
    ],
    "total": 100
  }
}
```

#### 3.1.2 添加主机
**接口路径**: `POST /api/ams-ce/host`

**请求体**:
```json
{
  "sn": "SN123456",
  "ip": "*************",
  "ident": "web-server-01",
  "name": "Web服务器01",
  "os_version": "Ubuntu 20.04",
  "cpu_model": "Intel Xeon",
  "zone": "us-west-1a",
  "cate": "server",
  "tenant": "production"
}
```

#### 3.1.3 主机过滤查询
**接口路径**: `POST /api/ams-ce/host/filter`

**特色功能**:
- 支持自定义字段的复合查询
- 支持模糊匹配和精确匹配
- 高性能的子查询优化

### 3.2 CSV批量导入接口

#### 3.2.1 流式批量导入
**接口路径**: `POST /api/ams-ce/hosts/csv/import`

**核心特性**:
- **流式解析**: 使用`ImportHostsFromCSVStream`实现内存优化
- **多格式支持**: CSV, XLSX, XLS
- **智能列映射**: 自动识别中英文列名
- **批量处理**: 分批插入数据库，提高性能
- **错误处理**: 详细的错误记录和失败数据下载

**支持的列映射**:
```go
var columnMappings = map[string]string{
    "序列号": "sn", "Serial Number": "sn",
    "IP地址": "ip", "IP Address": "ip",
    "标识符": "ident", "Identifier": "ident",
    "主机名": "name", "Hostname": "name",
    "操作系统版本": "os_version", "OS Version": "os_version",
    "内核版本": "kernel_version", "Kernel Version": "kernel_version",
    "CPU型号": "cpu_model", "CPU Model": "cpu_model",
    "GPU型号": "gpu_model", "GPU Model": "gpu_model",
    "可用区": "zone", "Zone": "zone",
    "机架": "rack", "Rack": "rack",
    "备注": "note", "Note": "note",
    "型号": "model", "Model": "model",
    "制造商": "manufacturer", "Manufacturer": "manufacturer",
    "IDC": "idc", "IDC": "idc",
    "GPU": "gpu", "GPU": "gpu",
    "CPU": "cpu", "CPU": "cpu",
    "内存": "mem", "Memory": "mem",
    "磁盘": "disk", "Disk": "disk",
    "分类": "cate", "Category": "cate",
    "租户": "tenant", "Tenant": "tenant",
}
```

#### 3.2.2 获取CSV模板
**接口路径**: `GET /api/ams-ce/hosts/csv/template`

#### 3.2.3 下载失败数据
**接口路径**: `GET /api/ams-ce/hosts/csv/failed/{import_id}`

### 3.3 JumpServer集成接口

#### 3.3.1 主机同步
**接口路径**: `POST /api/ams-ce/hosts/sync`

#### 3.3.2 获取同步状态
**接口路径**: `GET /api/ams-ce/sync/status`

#### 3.3.3 重置同步管理器
**接口路径**: `POST /api/ams-ce/sync/reset`

### 3.4 用户管理接口

#### 3.4.1 创建用户
**接口路径**: `POST /api/ams-ce/users`

#### 3.4.2 用户权限授权
**接口路径**: `POST /api/ams-ce/permissions/grant-user`

#### 3.4.3 用户权限撤销
**接口路径**: `POST /api/ams-ce/permissions/revoke-user`

### 3.5 账号管理接口

#### 3.5.1 绑定账号模板
**接口路径**: `POST /api/ams-ce/accounts/bind`

#### 3.5.2 获取账号绑定列表
**接口路径**: `GET /api/ams-ce/accounts/bindings`

#### 3.5.3 解绑账号
**接口路径**: `DELETE /api/ams-ce/accounts/bindings`

## 4. 核心业务流程

### 4.1 主机查询流程
```
用户请求 → 参数解析 → 批量参数处理 → 查询条件构建 → 数据库查询 → 结果返回
```

**关键特性**:
- 支持单个和批量查询 (逗号分隔)
- 智能匹配策略 (精确 vs 模糊)
- 未同步主机过滤

### 4.2 CSV流式导入流程
```
文件上传 → 格式检测 → 流式解析 → 列映射 → 数据验证 → 批量插入 → 错误处理 → 结果返回
```

**核心优化**:
- **内存优化**: 流式读取，避免大文件内存溢出
- **性能优化**: 分批处理，减少数据库压力
- **错误处理**: 详细记录失败原因，支持失败数据下载

### 4.3 JumpServer同步流程
```
主机变更 → 同步检测 → 数据转换 → JumpServer API调用 → 状态更新 → 结果记录
```

## 5. 技术实现细节

### 5.1 流式CSV解析实现
```go
func ImportHostsFromCSVStream(c *gin.Context) {
    // 1. 获取上传文件
    file, header, err := c.Request.FormFile("file")
    
    // 2. 检测文件格式
    ext := strings.ToLower(filepath.Ext(header.Filename))
    
    // 3. 流式解析
    var records [][]string
    switch ext {
    case ".csv":
        records = parseCSVStream(file)
    case ".xlsx", ".xls":
        records = parseExcelStream(file)
    }
    
    // 4. 批量处理
    processBatch(records)
}
```

### 5.2 智能列映射
```go
func mapColumns(headers []string) map[int]string {
    columnMap := make(map[int]string)
    for i, header := range headers {
        if field, exists := columnMappings[strings.TrimSpace(header)]; exists {
            columnMap[i] = field
        }
    }
    return columnMap
}
```

### 5.3 批量查询参数处理
```go
func parseStringSlice(value string) []string {
    if value == "" {
        return nil
    }
    parts := strings.Split(value, ",")
    result := make([]string, 0, len(parts))
    for _, part := range parts {
        if trimmed := strings.TrimSpace(part); trimmed != "" {
            result = append(result, trimmed)
        }
    }
    return result
}
```

## 6. 详细流程图

### 6.1 主机列表查询流程图

```mermaid
flowchart TD
    A[开始] --> B[解析查询参数]
    B --> C[构建HostQueryParams结构]
    C --> D{检查unsynced参数}
    D -->|true| E[查询未同步主机]
    D -->|false| F[查询所有主机]

    E --> G[调用HostTotalUnsynced]
    E --> H[调用HostGetsUnsynced]

    F --> I[调用HostTotalByAllFields]
    F --> J[调用HostGetsByAllFields]

    G --> K[构建响应数据]
    H --> K
    I --> K
    J --> K

    K --> L[返回JSON响应]
    L --> M[结束]
```

### 6.2 CSV流式导入详细流程图

```mermaid
flowchart TD
    A[开始] --> B[获取上传文件]
    B --> C[检测文件格式]
    C --> D{文件格式判断}

    D -->|.csv| E[CSV流式解析]
    D -->|.xlsx/.xls| F[Excel流式解析]
    D -->|其他| G[返回格式错误]

    E --> H[解析标题行]
    F --> H
    H --> I[智能列映射]
    I --> J[逐行数据解析]

    J --> K[数据验证]
    K --> L{验证通过?}
    L -->|否| M[记录失败数据]
    L -->|是| N[加入成功列表]

    M --> O{还有更多行?}
    N --> O
    O -->|是| J
    O -->|否| P[批量数据处理]

    P --> Q[分批插入数据库]
    Q --> R[处理失败数据存储]
    R --> S[生成导入报告]
    S --> T[返回导入结果]
    T --> U[结束]
```

### 6.3 主机添加流程图

```mermaid
flowchart TD
    A[开始] --> B[参数绑定]
    B --> C[基础验证]
    C --> D{IP和Ident非空?}
    D -->|否| E[返回参数错误]
    D -->|是| F[检查IP唯一性]

    F --> G{IP已存在?}
    G -->|是| H[返回IP重复错误]
    G -->|否| I[检查Ident唯一性]

    I --> J{Ident已存在?}
    J -->|是| K[返回标识符重复错误]
    J -->|否| L[创建主机对象]

    L --> M[设置时间戳]
    M --> N[开启数据库事务]
    N --> O[插入主机记录]
    O --> P{插入成功?}
    P -->|否| Q[事务回滚]
    P -->|是| R[更新扩展字段]

    R --> S{更新成功?}
    S -->|否| Q
    S -->|是| T[事务提交]

    Q --> U[返回插入失败错误]
    T --> V[返回主机信息]
    U --> W[结束]
    V --> W
```

### 6.4 JumpServer同步流程图

```mermaid
flowchart TD
    A[主机数据变更] --> B[检测同步需求]
    B --> C{需要同步?}
    C -->|否| D[跳过同步]
    C -->|是| E[获取主机信息]

    E --> F[构建JumpServer资产数据]
    F --> G[调用JumpServer API]
    G --> H{API调用成功?}
    H -->|否| I[记录同步失败]
    H -->|是| J[更新jumpserver_asset_id]

    J --> K[记录同步成功]
    I --> L[结束]
    K --> L
    D --> L
```

### 6.5 用户权限授权流程图

```mermaid
flowchart TD
    A[开始] --> B[解析授权请求]
    B --> C[验证用户存在性]
    C --> D{用户存在?}
    D -->|否| E[返回用户不存在错误]
    D -->|是| F[验证主机存在性]

    F --> G{主机存在?}
    G -->|否| H[返回主机不存在错误]
    G -->|是| I[检查现有权限]

    I --> J{权限已存在?}
    J -->|是| K[返回权限已存在]
    J -->|否| L[创建权限记录]

    L --> M[同步到JumpServer]
    M --> N{同步成功?}
    N -->|否| O[记录同步失败]
    N -->|是| P[记录授权成功]

    E --> Q[结束]
    H --> Q
    K --> Q
    O --> Q
    P --> Q
```

## 7. 性能优化策略

### 7.1 查询优化
- **批量查询**: 支持逗号分隔的多值查询，减少请求次数
- **索引优化**: 为常用查询字段建立合适的索引
- **分页查询**: 大数据量查询使用LIMIT/OFFSET分页
- **查询条件优化**: 精确匹配 vs 模糊匹配的智能选择

### 7.2 导入性能优化
- **流式处理**: 使用流式读取避免大文件内存溢出
- **分批处理**: 大量数据分批插入数据库
- **事务优化**: 合理使用事务减少锁定时间
- **并发控制**: 限制同时处理的导入请求数量

### 7.3 内存管理
- **及时释放**: 处理完成后及时释放文件句柄和内存
- **缓冲区管理**: 合理设置读取缓冲区大小
- **垃圾回收**: 主动触发垃圾回收释放内存

## 8. 错误处理机制

### 8.1 导入错误分类
- **格式错误**: 文件格式不支持、列缺失
- **数据错误**: IP重复、标识符重复、格式不正确
- **系统错误**: 数据库连接失败、磁盘空间不足

### 8.2 失败数据管理
```go
type FailedRowData struct {
    RowNumber int      `json:"row_number"`
    Data      []string `json:"data"`
    Error     string   `json:"error"`
    Timestamp int64    `json:"timestamp"`
}
```

### 8.3 错误恢复策略
- **部分成功**: 成功的数据正常入库，失败的数据提供下载
- **事务回滚**: 批量操作失败时自动回滚
- **重试机制**: 网络错误等临时性错误支持重试

## 9. 安全性设计

### 9.1 输入验证
- **文件类型验证**: 限制上传文件类型和大小
- **数据格式验证**: IP地址、标识符格式验证
- **SQL注入防护**: 使用参数化查询和ORM框架

### 9.2 权限控制
- **认证检查**: 所有接口需要用户登录认证
- **操作权限**: 基于用户角色的操作权限控制
- **数据隔离**: 支持租户级别的数据隔离

### 9.3 数据安全
- **敏感信息保护**: 密码等敏感信息加密存储
- **审计日志**: 记录关键操作的审计日志
- **备份恢复**: 定期数据备份和恢复机制
