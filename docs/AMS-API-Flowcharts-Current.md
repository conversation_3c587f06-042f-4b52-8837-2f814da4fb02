# AMS模块API接口流程图详解 (当前分支)

## 1. 主机管理接口流程图

### 1.1 获取主机列表 (GET /api/ams-ce/hosts)

```mermaid
flowchart TD
    A[开始] --> B[解析查询参数]
    B --> C[构建HostQueryParams结构体]
    C --> D[处理批量参数<br/>parseStringSlice]
    
    D --> E{检查unsynced参数}
    E -->|unsynced=true| F[查询未同步主机路径]
    E -->|正常查询| G[查询所有主机路径]
    
    F --> H[调用HostTotalUnsynced<br/>获取未同步主机总数]
    F --> I[调用HostGetsUnsynced<br/>获取未同步主机列表]
    
    G --> J[调用HostTotalByAllFields<br/>获取主机总数]
    G --> K[调用HostGetsByAllFields<br/>获取主机列表]
    
    H --> L[构建响应数据]
    I --> L
    J --> L
    K --> L
    
    L --> M[返回JSON响应<br/>{list, total}]
    M --> N[结束]
```

**关键特性**:
- 支持所有字段的批量查询 (逗号分隔)
- 智能匹配: 精确匹配 vs 模糊匹配
- 未同步主机过滤功能

### 1.2 添加主机 (POST /api/ams-ce/host)

```mermaid
flowchart TD
    A[开始] --> B[参数绑定hostForm]
    B --> C[基础参数验证]
    C --> D{IP和Ident非空?}
    D -->|否| E[返回参数错误]
    D -->|是| F[检查IP唯一性<br/>HostGet by IP]
    
    F --> G{IP已存在?}
    G -->|是| H[返回IP重复错误]
    G -->|否| I[检查Ident唯一性<br/>HostGet by Ident]
    
    I --> J{Ident已存在?}
    J -->|是| K[返回标识符重复错误]
    J -->|否| L[调用HostNew创建主机]
    
    L --> M[开启数据库事务]
    M --> N[插入主机基础信息]
    N --> O{有扩展字段?}
    O -->|是| P[更新扩展字段]
    O -->|否| Q[提交事务]
    
    P --> R{更新成功?}
    R -->|否| S[事务回滚]
    R -->|是| Q
    
    S --> T[返回插入失败错误]
    Q --> U[返回创建的主机信息]
    
    E --> V[结束]
    H --> V
    K --> V
    T --> V
    U --> V
```

### 1.3 主机过滤查询 (POST /api/ams-ce/host/filter)

```mermaid
flowchart TD
    A[开始] --> B[参数绑定hostFilterForm]
    B --> C[获取分页参数]
    C --> D[构建基础查询Session]
    
    D --> E[处理基础字段过滤]
    E --> F{有IP过滤条件?}
    F -->|是| G[添加IP精确匹配<br/>In查询]
    F -->|否| H{有名称过滤条件?}
    
    G --> H
    H -->|是| I[添加名称模糊匹配<br/>Like查询]
    H -->|否| J{有其他基础字段?}
    
    I --> J
    J -->|是| K[添加对应字段条件]
    J -->|否| L[处理自定义字段过滤]
    
    K --> L
    L --> M{有自定义字段过滤?}
    M -->|否| N[执行总数查询]
    M -->|是| O[循环处理每个字段过滤]
    
    O --> P[验证字段定义存在性]
    P --> Q{字段定义存在?}
    Q -->|否| R[返回字段不存在错误]
    Q -->|是| S[构建字段值子查询]
    
    S --> T{是模糊匹配?}
    T -->|是| U[构建LIKE条件<br/>支持多值OR]
    T -->|否| V[构建精确匹配<br/>In条件]
    
    U --> W[执行子查询获取host_ids]
    V --> W
    W --> X[将host_ids添加到主查询]
    X --> Y{还有更多字段过滤?}
    Y -->|是| O
    Y -->|否| N
    
    N --> Z{总数 > 0?}
    Z -->|否| AA[返回空结果]
    Z -->|是| BB[执行分页数据查询]
    
    BB --> CC[获取主机基础信息]
    CC --> DD[循环获取每个主机的自定义字段值]
    DD --> EE[构建完整的主机信息对象]
    EE --> FF[返回过滤结果{list, total}]
    
    R --> GG[结束]
    AA --> GG
    FF --> GG
```

## 2. CSV批量导入流程图

### 2.1 流式CSV导入主流程 (POST /api/ams-ce/hosts/csv/import)

```mermaid
flowchart TD
    A[开始] --> B[获取上传文件]
    B --> C[文件基础验证]
    C --> D{文件存在且大小合法?}
    D -->|否| E[返回文件错误]
    D -->|是| F[检测文件格式]
    
    F --> G{文件格式判断}
    G -->|.csv| H[CSV流式解析器]
    G -->|.xlsx/.xls| I[Excel流式解析器]
    G -->|其他| J[返回格式不支持错误]
    
    H --> K[解析标题行]
    I --> K
    K --> L[智能列映射<br/>columnMappings]
    L --> M[验证必需列存在]
    M --> N{必需列完整?}
    N -->|否| O[返回列缺失错误]
    N -->|是| P[初始化处理变量]
    
    P --> Q[逐行流式读取]
    Q --> R[数据行解析]
    R --> S[字段映射和转换]
    S --> T[数据验证]
    
    T --> U{验证通过?}
    U -->|否| V[记录到失败列表<br/>FailedRowData]
    U -->|是| W[构建Host对象]
    
    W --> X[加入成功列表]
    V --> Y{还有更多行?}
    X --> Y
    Y -->|是| Q
    Y -->|否| Z[批量数据处理]
    
    Z --> AA[分批插入数据库<br/>事务处理]
    AA --> BB{插入成功?}
    BB -->|否| CC[记录插入失败]
    BB -->|是| DD[更新成功计数]
    
    CC --> EE[处理失败数据存储]
    DD --> EE
    EE --> FF{有失败数据?}
    FF -->|是| GG[生成导入ID<br/>存储失败数据]
    FF -->|否| HH[构建成功响应]
    
    GG --> II[构建部分成功响应<br/>包含import_id]
    HH --> JJ[返回导入结果]
    II --> JJ
    
    E --> KK[结束]
    J --> KK
    O --> KK
    JJ --> KK
```

### 2.2 智能列映射流程

```mermaid
flowchart TD
    A[解析标题行] --> B[遍历每个列标题]
    B --> C[去除首尾空格]
    C --> D[查找columnMappings映射]
    D --> E{找到映射?}
    E -->|是| F[记录列索引和字段名]
    E -->|否| G[跳过该列]
    
    F --> H{还有更多列?}
    G --> H
    H -->|是| B
    H -->|否| I[验证必需字段映射]
    
    I --> J{IP和Ident列存在?}
    J -->|否| K[返回必需列缺失错误]
    J -->|是| L[返回列映射结果]
    
    K --> M[结束]
    L --> M
```

### 2.3 数据验证流程

```mermaid
flowchart TD
    A[单行数据验证] --> B[IP地址验证]
    B --> C{IP格式正确?}
    C -->|否| D[记录IP格式错误]
    C -->|是| E[IP唯一性检查]
    
    E --> F{IP已存在?}
    F -->|是| G[记录IP重复错误]
    F -->|否| H[标识符验证]
    
    H --> I{Ident非空?}
    I -->|否| J[记录标识符为空错误]
    I -->|是| K[标识符唯一性检查]
    
    K --> L{Ident已存在?}
    L -->|是| M[记录标识符重复错误]
    L -->|否| N[其他字段验证]
    
    N --> O[主机名验证]
    O --> P[可选字段验证]
    P --> Q[验证结果汇总]
    
    D --> R[验证失败]
    G --> R
    J --> R
    M --> R
    Q --> S{有验证错误?}
    S -->|是| R
    S -->|否| T[验证通过]
    
    R --> U[结束]
    T --> U
```

## 3. JumpServer集成流程图

### 3.1 主机同步流程 (POST /api/ams-ce/hosts/sync)

```mermaid
flowchart TD
    A[开始] --> B[获取同步参数]
    B --> C[验证主机ID列表]
    C --> D[循环处理每个主机]
    
    D --> E[获取主机详细信息]
    E --> F{主机存在?}
    F -->|否| G[跳过，记录错误]
    F -->|是| H[检查同步状态]
    
    H --> I{已同步到JumpServer?}
    I -->|是| J[更新JumpServer资产]
    I -->|否| K[创建JumpServer资产]
    
    J --> L[调用JumpServer更新API]
    K --> M[调用JumpServer创建API]
    
    L --> N{API调用成功?}
    M --> N
    N -->|否| O[记录同步失败]
    N -->|是| P[更新jumpserver_asset_id]
    
    P --> Q[记录同步成功]
    O --> R{还有更多主机?}
    Q --> R
    G --> R
    
    R -->|是| D
    R -->|否| S[生成同步报告]
    S --> T[返回同步结果]
    T --> U[结束]
```

### 3.2 用户权限授权流程 (POST /api/ams-ce/permissions/grant-user)

```mermaid
flowchart TD
    A[开始] --> B[解析授权请求参数]
    B --> C[验证用户存在性]
    C --> D{用户存在?}
    D -->|否| E[返回用户不存在错误]
    D -->|是| F[验证主机存在性]
    
    F --> G{主机存在?}
    G -->|否| H[返回主机不存在错误]
    G -->|是| I[检查现有权限]
    
    I --> J{权限已存在?}
    J -->|是| K[返回权限已存在提示]
    J -->|否| L[创建本地权限记录]
    
    L --> M[构建JumpServer权限数据]
    M --> N[调用JumpServer权限API]
    N --> O{JumpServer同步成功?}
    O -->|否| P[回滚本地权限<br/>记录同步失败]
    O -->|是| Q[记录授权成功]
    
    E --> R[结束]
    H --> R
    K --> R
    P --> R
    Q --> R
```

## 4. 账号管理流程图

### 4.1 绑定账号模板流程 (POST /api/ams-ce/accounts/bind)

```mermaid
flowchart TD
    A[开始] --> B[解析绑定请求]
    B --> C[验证账号模板存在性]
    C --> D{模板存在?}
    D -->|否| E[返回模板不存在错误]
    D -->|是| F[验证主机列表]
    
    F --> G[循环处理每个主机]
    G --> H[检查主机存在性]
    H --> I{主机存在?}
    I -->|否| J[跳过，记录错误]
    I -->|是| K[检查账号绑定状态]
    
    K --> L{账号已绑定?}
    L -->|是| M[跳过，记录已存在]
    L -->|否| N[创建账号绑定记录]
    
    N --> O[同步到JumpServer]
    O --> P{同步成功?}
    P -->|否| Q[记录同步失败]
    P -->|是| R[记录绑定成功]
    
    J --> S{还有更多主机?}
    M --> S
    Q --> S
    R --> S
    
    S -->|是| G
    S -->|否| T[生成绑定报告]
    T --> U[返回绑定结果]
    
    E --> V[结束]
    U --> V
```

## 5. 错误处理流程图

### 5.1 CSV导入错误处理

```mermaid
flowchart TD
    A[导入过程中发生错误] --> B[错误分类判断]
    B --> C{错误类型}
    C -->|格式错误| D[文件格式不支持<br/>列缺失等]
    C -->|数据错误| E[IP重复<br/>标识符重复<br/>格式不正确]
    C -->|系统错误| F[数据库连接失败<br/>磁盘空间不足]
    
    D --> G[记录错误信息]
    E --> G
    F --> G
    
    G --> H[构建FailedRowData]
    H --> I[添加到失败列表]
    I --> J[继续处理下一行]
    
    J --> K{处理完成?}
    K -->|否| L[继续处理]
    K -->|是| M[生成导入ID]
    
    M --> N[存储失败数据]
    N --> O[返回部分成功结果]
    O --> P[结束]
    
    L --> A
```

### 5.2 JumpServer同步错误处理

```mermaid
flowchart TD
    A[JumpServer API调用失败] --> B[错误类型判断]
    B --> C{错误类型}
    C -->|网络错误| D[连接超时<br/>网络不可达]
    C -->|认证错误| E[Token无效<br/>权限不足]
    C -->|数据错误| F[参数格式错误<br/>资源不存在]
    
    D --> G[标记为可重试错误]
    E --> H[标记为认证错误]
    F --> I[标记为数据错误]
    
    G --> J[执行重试逻辑]
    H --> K[记录认证失败]
    I --> L[记录数据错误]
    
    J --> M{重试成功?}
    M -->|是| N[更新同步状态]
    M -->|否| O[记录最终失败]
    
    K --> P[结束]
    L --> P
    N --> P
    O --> P
```

这些流程图详细展示了AMS模块当前分支中各个API接口的完整处理流程，包括正常流程和异常处理，有助于开发人员理解系统的业务逻辑和错误处理机制。
