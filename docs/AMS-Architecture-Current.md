# AMS模块架构总结 (当前分支)

## 1. 整体架构概览

### 1.1 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web UI    │  │  Mobile App │  │  API Client │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      API Gateway Layer                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              HTTP Router (Gin Framework)                │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │ Auth Middle │  │ CORS Middle │  │ Log Middle  │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Host Manager │  │Field Manager│  │CSV Importer │         │
│  │- CRUD Ops   │  │- Definition │  │- Stream Parse│        │
│  │- Multi Query│  │- Value Mgmt │  │- Batch Proc │         │
│  │- Sync Status│  │- Validation │  │- Error Hand │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │JS Sync Mgr  │  │User Manager │  │Account Mgr  │         │
│  │- Asset Sync │  │- Permission │  │- Template   │         │
│  │- Status Mgmt│  │- Grant/Rev  │  │- Binding    │         │
│  │- Error Hand │  │- User CRUD  │  │- Management │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Integration Layer                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                JumpServer Client                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │Asset API    │  │User API     │  │Permission   │     │ │
│  │  │- Create     │  │- Create     │  │- Grant      │     │ │
│  │  │- Update     │  │- Update     │  │- Revoke     │     │ │
│  │  │- Delete     │  │- Delete     │  │- Query      │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Data Access Layer                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                XORM (ORM Framework)                     │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │Model Mapper │  │Query Builder│  │Transaction  │     │ │
│  │  │- Host Model │  │- Multi Field│  │- Batch Ops  │     │ │
│  │  │- Field Model│  │- Complex Q  │  │- Rollback   │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Data Storage Layer                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    MySQL Database                       │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │    host     │  │ host_field  │  │field_value  │     │ │
│  │  │   (主机表)   │  │ (字段定义表) │  │ (字段值表)   │     │ │
│  │  │+ JS集成字段  │  │             │  │             │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  │  ┌─────────────┐  ┌─────────────┐                      │ │
│  │  │failed_data  │  │sync_status  │                      │ │
│  │  │(失败数据表)  │  │(同步状态表)  │                      │ │
│  │  └─────────────┘  └─────────────┘                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心组件关系
```
┌─────────────────┐    HTTP Request    ┌─────────────────┐
│   HTTP Client   │ ─────────────────→ │   Gin Router    │
└─────────────────┘                    └─────────────────┘
                                               │
                                               ▼
                                       ┌─────────────────┐
                                       │  Middleware     │
                                       │  - Auth Check   │
                                       │  - CORS Handle  │
                                       │  - Logging      │
                                       └─────────────────┘
                                               │
                                               ▼
┌─────────────────┐                   ┌─────────────────┐
│  Business Logic │ ←─────────────────│  Route Handler  │
│  - Host CRUD    │                   │  - Parameter    │
│  - Field Mgmt   │                   │  - Validation   │
│  - CSV Import   │                   │  - Response     │
│  - JS Sync      │                   │  - Error Handle │
│  - User Mgmt    │                   └─────────────────┘
│  - Account Mgmt │
└─────────────────┘
         │                                    │
         ▼                                    ▼
┌─────────────────┐    SQL Query      ┌─────────────────┐
│   XORM Models   │ ─────────────────→│  MySQL Database │
│  - Host Model   │                   │  - host         │
│  - Field Model  │                   │  - host_field   │
│  - Value Model  │                   │  - field_value  │
│  - Failed Model │                   │  - failed_data  │
└─────────────────┘                   └─────────────────┘
         │
         ▼
┌─────────────────┐    HTTP API       ┌─────────────────┐
│ JumpServer Sync │ ─────────────────→│   JumpServer    │
│  - Asset Sync   │                   │  - Asset Mgmt   │
│  - User Sync    │                   │  - User Mgmt    │
│  - Permission   │                   │  - Permission   │
└─────────────────┘                   └─────────────────┘
```

## 2. 核心功能模块

### 2.1 主机管理模块 (Host Management)
**职责**: 主机资产的全生命周期管理
**核心功能**:
- 主机信息的增删改查
- 多字段批量查询 (逗号分隔)
- IP和标识符唯一性验证
- 未同步主机过滤查询
- JumpServer集成字段管理

**关键文件**:
- `router_host.go`: 主机相关路由定义
- `host_add.go`: 主机添加逻辑
- `models/host.go`: 主机数据模型 (包含JS集成字段)

**特色功能**:
- 支持所有字段的批量查询
- 智能匹配策略 (精确 vs 模糊)
- JumpServer同步状态跟踪

### 2.2 自定义字段模块 (Custom Fields)
**职责**: 灵活的字段扩展机制
**核心功能**:
- 字段定义管理
- 字段值存储和查询
- 字段类型验证
- 必填字段检查
- 复合查询支持

**关键文件**:
- `router_host_field.go`: 字段相关路由
- `models/host_field.go`: 字段定义模型
- `models/host_field_value.go`: 字段值模型

### 2.3 流式CSV导入模块 (Stream CSV Import)
**职责**: 大批量数据导入处理
**核心功能**:
- 多格式文件解析 (CSV/Excel)
- 流式处理避免内存溢出
- 智能列映射 (中英文支持)
- 批量数据验证和插入
- 详细的错误处理和失败数据管理

**关键文件**:
- `host_csv.go`: CSV导入核心逻辑
- 支持的格式: `.csv`, `.xlsx`, `.xls`

**技术特色**:
- 流式解析大文件
- 智能列名映射
- 分批数据库操作
- 失败数据可下载

### 2.4 JumpServer集成模块 (JumpServer Integration)
**职责**: 与JumpServer堡垒机的深度集成
**核心功能**:
- 主机资产自动同步
- 用户账号同步
- 权限管理同步
- 同步状态跟踪
- 错误处理和重试

**关键接口**:
- 主机同步: `POST /api/ams-ce/hosts/sync`
- 同步状态: `GET /api/ams-ce/sync/status`
- 同步重置: `POST /api/ams-ce/sync/reset`

### 2.5 用户权限管理模块 (User Permission Management)
**职责**: 用户权限的统一管理
**核心功能**:
- 用户创建和管理
- 主机访问权限授权
- 权限撤销
- 批量权限操作

**关键接口**:
- 用户创建: `POST /api/ams-ce/users`
- 权限授权: `POST /api/ams-ce/permissions/grant-user`
- 权限撤销: `POST /api/ams-ce/permissions/revoke-user`

### 2.6 账号模板管理模块 (Account Template Management)
**职责**: 批量账号管理和绑定
**核心功能**:
- 账号模板定义
- 批量账号绑定
- 账号权限管理
- 绑定状态跟踪

**关键接口**:
- 账号绑定: `POST /api/ams-ce/accounts/bind`
- 绑定查询: `GET /api/ams-ce/accounts/bindings`
- 账号解绑: `DELETE /api/ams-ce/accounts/bindings`

## 3. 数据模型设计

### 3.1 实体关系图 (ERD)
```
┌─────────────────┐         ┌─────────────────┐
│      host       │         │   host_field    │
│─────────────────│         │─────────────────│
│ id (PK)         │         │ id (PK)         │
│ sn              │         │ field_ident     │
│ ip (UNIQUE)     │         │ field_name      │
│ ident (UNIQUE)  │         │ field_type      │
│ name            │         │ field_extra     │
│ os_version      │         │ field_required  │
│ kernel_version  │         │ field_cate      │
│ cpu_model       │         └─────────────────┘
│ gpu_model       │                  │
│ zone            │                  │ 1:N
│ rack            │                  │
│ note            │                  ▼
│ model           │         ┌─────────────────┐
│ manufacturer    │    1:N  │host_field_value │
│ idc             │ ───────→│─────────────────│
│ gpu             │         │ id (PK)         │
│ cpu             │         │ host_id (FK)    │
│ mem             │         │ field_ident (FK)│
│ disk            │         │ field_value     │
│ cate            │         └─────────────────┘
│ clock           │
│ tenant          │         ┌─────────────────┐
│ jumpserver_*    │ ───────→│  failed_data    │
└─────────────────┘         │─────────────────│
                            │ id (PK)         │
                            │ import_id       │
                            │ row_number      │
                            │ row_data        │
                            │ error_message   │
                            │ created_at      │
                            └─────────────────┘
```

### 3.2 JumpServer集成字段
```sql
-- 主机表中的JumpServer集成字段
jumpserver_asset_id VARCHAR(255) -- JumpServer资产ID
jumpserver_node_id VARCHAR(255)  -- JumpServer节点ID

-- 索引
KEY idx_jumpserver_asset_id (jumpserver_asset_id)
KEY idx_jumpserver_node_id (jumpserver_node_id)
```

## 4. API接口设计

### 4.1 RESTful API规范
```
资源类型: hosts (主机)
Base URL: /api/ams-ce

# 主机管理
GET    /hosts              # 获取主机列表 (支持批量查询)
POST   /host               # 创建主机
PUT    /host/{id}          # 更新主机
DELETE /hosts              # 批量删除主机
POST   /host/filter        # 多维度过滤查询

# CSV导入
POST   /hosts/csv/import   # 流式批量导入
GET    /hosts/csv/template # 获取CSV模板
GET    /hosts/csv/failed/{import_id}  # 下载失败数据

# JumpServer集成
POST   /hosts/sync         # 主机同步
GET    /sync/status        # 同步状态
POST   /sync/reset         # 重置同步

# 用户管理
POST   /users              # 创建用户
POST   /permissions/grant-user   # 权限授权
POST   /permissions/revoke-user  # 权限撤销

# 账号管理
POST   /accounts/bind      # 绑定账号模板
GET    /accounts/bindings  # 获取绑定列表
DELETE /accounts/bindings  # 解绑账号
```

### 4.2 批量查询特性
```
# 支持所有字段的批量查询 (逗号分隔)
GET /hosts?ip=***********,***********&name=web,db&zone=us-west-1a,us-west-1b

# 未同步主机查询
GET /hosts?unsynced=true

# 精确匹配字段: id, ip, zone, idc, gpu, cpu, mem, disk, cate, tenant
# 模糊匹配字段: sn, ident, name, os_version, kernel_version, cpu_model, gpu_model, rack, note, model, manufacturer
```

## 5. 性能优化策略

### 5.1 查询优化
- **批量查询**: 支持逗号分隔的多值查询，减少请求次数
- **智能匹配**: 根据字段类型选择精确或模糊匹配
- **索引优化**: 为常用查询字段建立合适的索引
- **分页查询**: 大数据量查询使用LIMIT/OFFSET分页

### 5.2 导入性能优化
- **流式处理**: 使用流式读取避免大文件内存溢出
- **分批处理**: 大量数据分批插入数据库
- **事务优化**: 合理使用事务减少锁定时间
- **智能映射**: 预编译列映射提高解析效率

### 5.3 JumpServer集成优化
- **批量同步**: 支持批量主机同步操作
- **状态缓存**: 缓存同步状态减少重复查询
- **错误重试**: 智能重试机制处理临时性错误
- **异步处理**: 大批量同步使用异步处理

## 6. 监控和运维

### 6.1 关键指标
- **接口响应时间**: 各API接口的性能指标
- **导入成功率**: CSV导入操作的成功率
- **同步成功率**: JumpServer同步的成功率
- **数据库性能**: 查询响应时间和连接数
- **内存使用**: 流式处理的内存使用情况

### 6.2 日志管理
- **结构化日志**: 使用统一的日志格式
- **日志级别**: ERROR/INFO/DEBUG分级记录
- **业务日志**: 记录关键业务操作和状态变更
- **错误日志**: 详细记录错误信息和堆栈

### 6.3 故障处理
- **优雅降级**: 在高负载情况下的降级策略
- **错误恢复**: 自动重试和错误恢复机制
- **数据一致性**: 确保AMS和JumpServer数据一致性
- **监控告警**: 关键指标的监控和告警

这个架构设计确保了AMS模块的高性能、高可用性和良好的可扩展性，特别是在JumpServer集成、流式数据处理和批量查询方面提供了强大的功能支持。
