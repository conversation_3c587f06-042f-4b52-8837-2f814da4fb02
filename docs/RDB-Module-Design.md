# RDB模块技术设计文档

## 1. 模块概述

### 1.1 模块职责
RDB (Resource Database) 是Arboris系统的核心资源管理模块，负责：
- **用户管理**：用户生命周期管理、认证授权
- **节点管理**：树形结构的节点管理、权限控制
- **资源管理**：资源的注册、绑定、生命周期管理
- **权限控制**：基于RBAC的权限模型
- **事件发布**：向JumpServer同步服务发布变更事件
- **会话管理**：用户会话和Token管理

### 1.2 核心功能
- 多租户用户管理（长期/临时账号）
- 树形节点结构管理
- 资源生命周期管理
- 细粒度权限控制
- 账号模板管理
- 操作日志记录

### 1.3 架构定位
```
┌─────────────┐    HTTP API    ┌─────────────┐    Events    ┌─────────────────┐
│   前端UI    │ ──────────────→ │ RDB Module  │ ────────────→ │ JumpServer-Sync │
│   AMS       │                │             │              └─────────────────┘
│   其他模块   │                └─────────────┘
└─────────────┘                       │
                                      ▼
                              ┌─────────────┐
                              │ RDB Database│
                              │ (MySQL)     │
                              └─────────────┘
```

## 2. 技术架构

### 2.1 技术栈
- **Web框架**：Gin (Go HTTP框架)
- **ORM**：XORM (Go ORM框架)
- **数据库**：MySQL 5.7+
- **缓存**：内存缓存 + Redis
- **会话**：基于数据库的Session存储
- **认证**：多种认证方式支持
- **事件**：Redis Streams事件发布

### 2.2 项目结构
```
src/modules/rdb/
├── rdb.go                    # 主程序入口
├── config/                   # 配置管理
│   └── yaml.go              # YAML配置解析
├── http/                     # HTTP服务层
│   ├── router.go            # 路由配置
│   ├── router_auth.go       # 认证接口
│   ├── router_user.go       # 用户管理接口
│   ├── router_node.go       # 节点管理接口
│   ├── router_resource.go   # 资源管理接口
│   ├── router_perm.go       # 权限管理接口
│   └── http_middleware.go   # 中间件
├── auth/                     # 认证模块
├── session/                  # 会话管理
├── cache/                    # 缓存管理
└── events/                   # 事件发布
```

### 2.3 核心组件关系
```
HTTP Layer (Gin Router)
    ├── Authentication Layer
    │   ├── Session Management
    │   ├── Token Authentication
    │   └── Permission Control
    ├── Business Logic Layer
    │   ├── User Management
    │   ├── Node Management
    │   ├── Resource Management
    │   └── Account Template Management
    ├── Event Publishing Layer
    │   └── Redis Streams Producer
    └── Data Access Layer (XORM)
            ↓
    MySQL Database (RDB)
```

## 3. API接口文档

### 3.1 认证管理接口

#### 3.1.1 用户登录
```http
POST /api/rdb/auth/login
```
**请求体**：
```json
{
  "username": "admin",
  "password": "password123"
}
```

#### 3.1.2 用户登出
```http
GET /api/rdb/auth/logout
```

### 3.2 用户管理接口

#### 3.2.1 创建用户
```http
POST /api/rdb/users
```
**请求体**：
```json
{
  "username": "testuser",
  "password": "Test123456!",
  "dispname": "测试用户",
  "email": "<EMAIL>",
  "phone": "***********",
  "typ": 0,
  "status": 0,
  "organization": "测试组织",
  "active_begin": **********,
  "active_end": **********
}
```

#### 3.2.2 更新用户信息
```http
PUT /api/rdb/user/{id}/profile
```

#### 3.2.3 获取用户列表
```http
GET /api/rdb/users
```
**参数**：
- `limit`: 分页大小
- `p`: 页码
- `query`: 搜索关键词

### 3.3 节点管理接口

#### 3.3.1 创建节点
```http
POST /api/rdb/node
```
**请求体**：
```json
{
  "pid": 1,
  "ident": "web-cluster",
  "name": "Web集群",
  "cate": "cluster",
  "leaf": 0,
  "note": "Web服务集群",
  "admin_ids": [1, 2]
}
```

#### 3.3.2 获取节点树
```http
GET /api/rdb/tree
```

#### 3.3.3 绑定资源到节点
```http
POST /api/rdb/node/{id}/resources/bind
```
**请求体**：
```json
{
  "res_ids": [1, 2, 3]
}
```

### 3.4 资源管理接口

#### 3.4.1 注册资源
```http
POST /v1/rdb/resources/register
```
**请求体**：
```json
{
  "uuid": "host-123",
  "ident": "web-server-01",
  "name": "Web服务器01",
  "cate": "host",
  "tenant": "default",
  "source_id": 123,
  "source_type": "host"
}
```

#### 3.4.2 注销资源
```http
POST /v1/rdb/resources/unregister
```

### 3.5 账号模板接口

#### 3.5.1 应用账号模板
```http
POST /api/rdb/account-templates/apply
```
**请求体**：
```json
{
  "template_id": 11,
  "resource_ids": [1, 2, 3]
}
```

### 3.6 权限管理接口

#### 3.6.1 检查全局权限
```http
GET /api/rdb/can-do-global-op?username=admin&op=user_create
```

#### 3.6.2 检查节点权限
```http
GET /api/rdb/can-do-node-op?username=admin&nid=1&op=resource_bind
```

## 4. 数据模型

### 4.1 用户表 (user)
```sql
CREATE TABLE `user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uuid` varchar(128) NOT NULL COMMENT '用户UUID',
  `username` varchar(64) NOT NULL COMMENT '用户名',
  `password` varchar(128) NOT NULL COMMENT '密码',
  `passwords` text COMMENT '历史密码',
  `dispname` varchar(32) NOT NULL COMMENT '显示名',
  `phone` varchar(16) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `im` varchar(64) DEFAULT NULL COMMENT 'IM账号',
  `portrait` varchar(255) DEFAULT NULL COMMENT '头像',
  `intro` varchar(255) DEFAULT NULL COMMENT '简介',
  `organization` varchar(255) DEFAULT NULL COMMENT '组织',
  `typ` tinyint(4) DEFAULT '0' COMMENT '用户类型：0-长期账号，1-临时账号',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-激活，1-未激活，2-锁定，3-冻结，4-注销',
  `is_root` tinyint(4) DEFAULT '0' COMMENT '是否超管',
  `leader_id` bigint(20) DEFAULT '0' COMMENT '上级ID',
  `leader_name` varchar(32) DEFAULT NULL COMMENT '上级姓名',
  `active_begin` bigint(20) DEFAULT '0' COMMENT '临时账号开始时间',
  `active_end` bigint(20) DEFAULT '0' COMMENT '临时账号结束时间',
  `pwd_updated_at` bigint(20) DEFAULT '0' COMMENT '密码更新时间',
  `login_err_num` int(11) DEFAULT '0' COMMENT '登录错误次数',
  `updated_at` bigint(20) DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_uuid` (`uuid`)
);
```

### 4.2 节点表 (node)
```sql
CREATE TABLE `node` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) unsigned NOT NULL COMMENT '父节点ID',
  `ident` varchar(128) NOT NULL COMMENT '节点标识',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `note` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `path` varchar(255) NOT NULL COMMENT '节点路径',
  `leaf` tinyint(1) NOT NULL COMMENT '是否叶子节点',
  `cate` char(128) NOT NULL DEFAULT '' COMMENT '节点分类',
  `icon_color` char(7) NOT NULL DEFAULT '' COMMENT '图标颜色',
  `icon_char` char(1) NOT NULL DEFAULT '' COMMENT '图标字符',
  `proxy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否代理节点',
  `creator` varchar(64) NOT NULL COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_path` (`path`),
  KEY `idx_pid` (`pid`)
);
```

### 4.3 资源表 (resource)
```sql
CREATE TABLE `resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uuid` varchar(128) NOT NULL COMMENT '资源UUID',
  `ident` varchar(255) NOT NULL COMMENT '资源标识',
  `name` varchar(255) NOT NULL COMMENT '资源名称',
  `labels` text COMMENT '标签JSON',
  `note` varchar(255) DEFAULT NULL COMMENT '备注',
  `extend` text COMMENT '扩展字段JSON',
  `cate` varchar(128) NOT NULL COMMENT '资源分类',
  `tenant` varchar(128) NOT NULL COMMENT '租户',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `source_id` bigint(20) DEFAULT NULL COMMENT '源ID',
  `source_type` varchar(64) DEFAULT NULL COMMENT '源类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uuid` (`uuid`),
  UNIQUE KEY `idx_ident` (`ident`),
  KEY `idx_tenant` (`tenant`),
  KEY `idx_cate` (`cate`)
);
```

### 4.4 节点资源绑定表 (node_resource)
```sql
CREATE TABLE `node_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `res_id` bigint(20) NOT NULL COMMENT '资源ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_node_res` (`node_id`, `res_id`)
);
```

## 5. 业务流程

### 5.1 用户登录流程
```
用户提交登录请求
    ↓
用户名密码验证
    ↓
检查用户状态（激活/锁定/冻结）
    ↓
临时用户有效期检查
    ↓
白名单访问控制检查
    ↓
创建用户会话
    ↓
返回登录结果
```

### 5.2 节点创建流程
```
用户提交节点创建请求
    ↓
权限验证（父节点创建权限）
    ↓
参数验证（路径唯一性）
    ↓
创建节点记录
    ↓
设置节点管理员
    ↓
发布节点创建事件
    ↓
返回创建结果
```

### 5.3 资源绑定流程
```
用户提交资源绑定请求
    ↓
权限验证（节点绑定权限）
    ↓
叶子节点检查
    ↓
租户权限验证
    ↓
批量绑定资源
    ↓
发布资源绑定事件
    ↓
返回绑定结果
```

### 5.4 账号模板应用流程
```
用户提交模板应用请求
    ↓
模板存在性验证
    ↓
资源权限验证
    ↓
创建账号记录
    ↓
创建资源绑定关系
    ↓
发布账号创建事件
    ↓
发布账号绑定事件
    ↓
返回应用结果
```

## 6. 事件机制

### 6.1 事件类型
- **节点事件**：`node.create`, `node.update`, `node.delete`
- **用户事件**：`user.create`, `user.update`, `user.delete`
- **资源事件**：`resource.create`, `resource.update`, `resource.delete`
- **绑定事件**：`resource.bind`, `resource.unbind`
- **账号事件**：`account.create`, `account.update`, `account.delete`
- **账号绑定事件**：`account.bind`, `account.unbind`

### 6.2 事件数据格式
```json
{
  "id": "event_20250820_151234_abc123",
  "type": "user.create",
  "source": "rdb",
  "timestamp": **********,
  "data": {
    "id": 123,
    "uuid": "user-uuid-123",
    "username": "testuser",
    "dispname": "测试用户",
    "email": "<EMAIL>",
    "type": 0,
    "status": 0,
    "organization": "测试组织"
  },
  "metadata": {
    "entity_type": "user",
    "entity_id": "123"
  }
}
```

### 6.3 事件发布配置
```yaml
events:
  enable: true
  stream_name: "arboris:sync:events"
```

## 7. 权限模型

### 7.1 认证机制
- **Session认证**：基于Cookie的Web认证
- **Token认证**：基于X-User-Token的API认证
- **多因子认证**：支持MFA扩展

### 7.2 权限层级
```
全局权限 (Global Permissions)
├── 用户管理权限
├── 节点管理权限
├── 资源管理权限
└── 系统管理权限

节点权限 (Node Permissions)
├── 节点查看权限
├── 节点修改权限
├── 资源绑定权限
└── 子节点创建权限
```

### 7.3 权限检查流程
```
请求到达
    ↓
身份认证（Session/Token）
    ↓
临时用户有效期检查
    ↓
全局权限检查
    ↓
节点权限检查（如需要）
    ↓
租户权限检查
    ↓
执行业务逻辑
```

## 8. 配置说明

### 8.1 主配置文件 (etc/rdb.yml)
```yaml
logger:
  dir: logs/rdb
  level: INFO
  keepHours: 168

http:
  mode: debug
  cookieName: ecmc-sid
  cookieDomain: ""

auth:
  extraMode:
    ldap:
      enable: false
    oauth:
      enable: false

redis:
  addr: "10.1.4.213:6379"
  password: "4KyCLw2TEMWsWCz8"
  db: 0

events:
  enable: true
  stream_name: "arboris:sync:events"
```

### 8.2 关键配置项说明
- **auth.extraMode**: 扩展认证方式配置
- **events.enable**: 是否启用事件发布
- **events.stream_name**: Redis Streams流名称
