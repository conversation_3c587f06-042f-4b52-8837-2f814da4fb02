# JumpServer-Sync模块技术设计文档

## 1. 模块概述

### 1.1 模块职责
JumpServer-Sync是Arboris系统与JumpServer堡垒机的同步服务模块，负责：
- **事件消费**：从Redis Streams消费RDB/AMS模块发布的变更事件
- **数据同步**：将用户、资产、账号等数据同步到JumpServer
- **映射转换**：将Arboris数据模型映射为JumpServer数据模型
- **错误处理**：支持重试机制和死信队列
- **监控管理**：提供健康检查和管理接口

### 1.2 核心功能
- 实时事件消费和处理
- 用户生命周期同步（创建、更新、删除）
- 资产生命周期同步（创建、更新、删除）
- 账号管理同步（创建、更新、删除、绑定）
- 临时用户过期时间管理
- 死信队列和重试机制

### 1.3 架构定位
```
┌─────────────┐    Events    ┌─────────────────┐    HTTP API    ┌─────────────┐
│ RDB Module  │ ────────────→ │ Redis Streams   │ ──────────────→ │ JumpServer  │
│ AMS Module  │              │                 │                │             │
└─────────────┘              └─────────────────┘                └─────────────┘
                                      │
                                      ▼
                              ┌─────────────────┐
                              │ JumpServer-Sync │
                              │ - Event Consumer│
                              │ - Data Mapper   │
                              │ - JS Client     │
                              │ - Dead Letter Q │
                              └─────────────────┘
```

## 2. 技术架构

### 2.1 技术栈
- **消息队列**：Redis Streams
- **HTTP客户端**：Go net/http
- **数据库**：MySQL (读取配置和状态)
- **Web框架**：Gin (管理接口)
- **重试机制**：指数退避算法
- **监控**：Swagger API文档

### 2.2 项目结构
```
src/modules/jumpserver-sync/
├── jumpserver-sync.go        # 主程序入口
├── config/                   # 配置管理
│   └── yaml.go              # YAML配置解析
├── events/                   # 事件处理
│   ├── types.go             # 事件类型定义
│   ├── consumer.go          # Redis Streams消费者
│   ├── producer.go          # 事件生产者（测试用）
│   └── retry.go             # 重试机制
├── sync/                     # 同步逻辑
│   ├── handler.go           # 事件处理器
│   ├── mapper.go            # 数据映射器
│   └── filter.go            # 同步过滤器
├── jumpserver/               # JumpServer客户端
│   ├── client.go            # HTTP客户端
│   ├── models.go            # JumpServer数据模型
│   └── auth.go              # 认证管理
├── deadletter/               # 死信队列
│   └── queue.go             # 死信队列实现
└── http/                     # HTTP管理接口
    ├── server.go            # HTTP服务器
    ├── admin.go             # 管理接口
    └── health.go            # 健康检查
```

### 2.3 核心组件关系
```
Redis Streams Consumer
    ├── Event Parser
    ├── Event Handler
    │   ├── Data Mapper
    │   ├── Sync Filter
    │   └── JumpServer Client
    ├── Retry Manager
    │   ├── Exponential Backoff
    │   └── Error Classifier
    └── Dead Letter Queue
            ↓
    JumpServer HTTP API
```

## 3. API接口文档

### 3.1 健康检查接口

#### 3.1.1 服务健康检查
```http
GET /health
```
**响应**：
```json
{
  "status": "healthy",
  "timestamp": "2025-08-20T15:30:00Z",
  "version": "1.0.0",
  "uptime": "2h30m15s"
}
```

#### 3.1.2 详细健康检查
```http
GET /health/detailed
```
**响应**：
```json
{
  "status": "healthy",
  "components": {
    "redis": "healthy",
    "jumpserver": "healthy",
    "database": "healthy"
  },
  "metrics": {
    "events_processed": 1250,
    "events_failed": 5,
    "last_event_time": "2025-08-20T15:29:45Z"
  }
}
```

### 3.2 死信队列管理接口

#### 3.2.1 获取死信消息列表
```http
GET /api/admin/dead-letter/messages
```
**参数**：
- `limit`: 分页大小 (默认20)
- `offset`: 偏移量 (默认0)

#### 3.2.2 重新处理死信消息
```http
POST /api/admin/dead-letter/messages/stream/{stream_id}/reprocess
```

#### 3.2.3 删除死信消息
```http
DELETE /api/admin/dead-letter/messages/stream/{stream_id}
```

### 3.3 系统监控接口

#### 3.3.1 获取系统状态
```http
GET /api/admin/status
```
**响应**：
```json
{
  "consumer": {
    "status": "running",
    "last_poll_time": "2025-08-20T15:29:50Z",
    "processed_count": 1250,
    "error_count": 5
  },
  "jumpserver": {
    "status": "connected",
    "last_sync_time": "2025-08-20T15:29:45Z",
    "api_version": "v1"
  }
}
```

#### 3.3.2 获取处理统计
```http
GET /api/admin/metrics
```

## 4. 数据模型

### 4.1 事件数据结构
```go
type Event struct {
    ID        string                 `json:"id"`        // 事件ID
    Type      EventType              `json:"type"`      // 事件类型
    Source    string                 `json:"source"`    // 事件源
    Timestamp int64                  `json:"timestamp"` // 时间戳
    Data      map[string]interface{} `json:"data"`      // 事件数据
    Metadata  map[string]string      `json:"metadata"`  // 元数据
}
```

### 4.2 JumpServer用户模型
```go
type User struct {
    ID           string      `json:"id,omitempty"`
    Username     string      `json:"username"`
    Name         string      `json:"name"`
    Email        string      `json:"email"`
    Phone        string      `json:"phone"`
    IsActive     bool        `json:"is_active"`
    IsSuperuser  bool        `json:"is_superuser"`
    DateExpired  interface{} `json:"date_expired,omitempty"`
    Comment      string      `json:"comment"`
    Groups       []interface{} `json:"groups"`
    UserGroups   []interface{} `json:"user_groups"`
    SystemRoles  []interface{} `json:"system_roles"`
    OrgRoles     []interface{} `json:"org_roles"`
}
```

### 4.3 JumpServer资产模型
```go
type Asset struct {
    ID       string            `json:"id,omitempty"`
    Hostname string            `json:"hostname"`
    IP       string            `json:"ip"`
    Platform string            `json:"platform"`
    Protocols []AssetProtocol   `json:"protocols"`
    IsActive bool              `json:"is_active"`
    Comment  string            `json:"comment"`
    Labels   map[string]string `json:"labels"`
}
```

### 4.4 JumpServer账号模型
```go
type Account struct {
    ID           string      `json:"id,omitempty"`
    Name         string      `json:"name"`
    Username     string      `json:"username"`
    SecretType   string      `json:"secret_type"`
    Secret       string      `json:"secret"`
    Asset        string      `json:"asset"`
    IsActive     interface{} `json:"is_active"`
    Comment      string      `json:"comment"`
}
```

## 5. 业务流程

### 5.1 事件消费流程
```
Redis Streams轮询
    ↓
解析事件消息
    ↓
事件类型分发
    ↓
数据映射转换
    ↓
同步过滤检查
    ↓
调用JumpServer API
    ↓
处理结果确认
    ↓
错误处理/重试
```

### 5.2 用户同步流程
```
接收用户事件 (create/update/delete)
    ↓
解析用户数据
    ↓
映射为JumpServer用户模型
    ↓
处理临时用户过期时间
    ↓
检查用户是否已存在
    ↓
创建/更新/删除JumpServer用户
    ↓
记录同步结果
```

### 5.3 资产同步流程
```
接收主机事件 (create/update/delete)
    ↓
解析主机数据
    ↓
映射为JumpServer资产模型
    ↓
设置资产协议和平台
    ↓
检查资产是否已存在
    ↓
创建/更新/删除JumpServer资产
    ↓
记录同步结果
```

### 5.4 账号同步流程
```
接收账号事件 (create/update/delete/bind)
    ↓
解析账号数据
    ↓
查找关联的资产
    ↓
映射为JumpServer账号模型
    ↓
检查账号是否已存在
    ↓
创建/更新/删除JumpServer账号
    ↓
处理账号绑定关系
    ↓
记录同步结果
```

### 5.5 错误处理流程
```
事件处理失败
    ↓
错误分类判断
    ↓
├── 可重试错误 → 指数退避重试
├── 死信队列错误 → 发送到死信队列
└── 丢弃错误 → 记录日志并丢弃
    ↓
重试次数检查
    ↓
├── 未达到最大次数 → 继续重试
└── 达到最大次数 → 发送到死信队列
```

## 6. 事件机制

### 6.1 支持的事件类型
```go
const (
    // 节点事件
    EventNodeCreate EventType = "node.create"
    EventNodeUpdate EventType = "node.update"
    EventNodeDelete EventType = "node.delete"
    
    // 主机事件
    EventHostCreate EventType = "host.create"
    EventHostUpdate EventType = "host.update"
    EventHostDelete EventType = "host.delete"
    
    // 用户事件
    EventUserCreate EventType = "user.create"
    EventUserUpdate EventType = "user.update"
    EventUserDelete EventType = "user.delete"
    
    // 账号事件
    EventAccountCreate EventType = "account.create"
    EventAccountUpdate EventType = "account.update"
    EventAccountDelete EventType = "account.delete"
    EventAccountBind   EventType = "account.bind"
    EventAccountUnbind EventType = "account.unbind"
)
```

### 6.2 消费者配置
```yaml
sync:
  consumer:
    stream: "arboris:sync:events"
    group: "jumpserver-sync-group"
    consumer: "jumpserver-sync-consumer-1"
    batch_size: 10
    poll_interval: "1s"
```

### 6.3 重试配置
```yaml
sync:
  retry:
    max_attempts: 3
    initial_interval: "1s"
    max_interval: "30s"
    multiplier: 2.0
    enable_dlq: true
```

## 7. 权限模型

### 7.1 JumpServer认证
- **Token认证**：使用JumpServer API Token
- **组织隔离**：支持多组织环境
- **权限继承**：继承JumpServer的权限模型

### 7.2 同步权限控制
- **数据过滤**：基于租户和分类的数据过滤
- **字段映射**：敏感字段的安全映射
- **访问控制**：限制同步服务的API访问权限

## 8. 配置说明

### 8.1 主配置文件 (etc/jumpserver-sync.yml)
```yaml
logger:
  dir: logs/jumpserver-sync
  level: INFO
  keepHours: 168

http:
  listen: ":8003"
  mode: debug

redis:
  addr: "**********:6379"
  password: "4KyCLw2TEMWsWCz8"
  db: 0

jumpserver:
  base_url: "http://**********:8090"
  api_token: "your-jumpserver-api-token"
  org_id: "********-0000-0000-0000-********0002"
  timeout: "30s"
  retry_attempts: 3

sync:
  consumer:
    stream: "arboris:sync:events"
    group: "jumpserver-sync-group"
    consumer: "jumpserver-sync-consumer-1"
    batch_size: 10
    poll_interval: "1s"
  
  retry:
    max_attempts: 3
    initial_interval: "1s"
    max_interval: "30s"
    multiplier: 2.0
    enable_dlq: true
  
  error_handling:
    retry_patterns:
      - "connection refused"
      - "timeout"
      - "temporary failure"
    dlq_patterns:
      - "authentication failed"
      - "permission denied"
    discard_patterns:
      - "invalid data format"
      - "malformed request"
  
  filters:
    sync_users: true
    sync_assets: true
    sync_accounts: true
    tenant_whitelist: ["inner", "default"]
    category_blacklist: ["test", "temp"]
```

### 8.2 关键配置项说明
- **jumpserver.base_url**: JumpServer服务地址
- **jumpserver.api_token**: JumpServer API访问令牌
- **jumpserver.org_id**: JumpServer组织ID
- **sync.consumer**: Redis Streams消费者配置
- **sync.retry**: 重试机制配置
- **sync.error_handling**: 错误处理策略配置
- **sync.filters**: 同步过滤规则配置

### 8.3 环境变量支持
```bash
# JumpServer配置
JUMPSERVER_BASE_URL=http://**********:8090
JUMPSERVER_API_TOKEN=your-api-token
JUMPSERVER_ORG_ID=********-0000-0000-0000-********0002

# Redis配置
REDIS_ADDR=**********:6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs/jumpserver-sync
```

## 9. 监控和运维

### 9.1 关键指标
- **事件处理速率**：每秒处理的事件数量
- **错误率**：处理失败的事件比例
- **重试次数**：平均重试次数
- **死信队列大小**：待处理的失败消息数量
- **JumpServer连接状态**：API连接健康状态

### 9.2 日志格式
```
2025-08-20 15:30:00.123 INFO Event processed successfully: stream_id=1755660498306-0, event_id=1755660498135-abc123, event_type=user.create
2025-08-20 15:30:01.456 ERROR Failed to sync user to JumpServer: user_id=123, error=authentication failed
2025-08-20 15:30:02.789 WARN Event sent to dead letter queue: event_id=1755660498135-def456, retry_count=3
```

### 9.3 故障排查
- **连接问题**：检查JumpServer服务状态和网络连通性
- **认证问题**：验证API Token的有效性和权限
- **数据问题**：检查事件数据格式和必填字段
- **性能问题**：监控处理延迟和资源使用情况
