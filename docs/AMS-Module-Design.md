# AMS模块技术设计文档

## 1. 模块概述

### 1.1 模块职责
AMS (Asset Management System) 是Arboris系统的资产管理模块，负责：
- **主机设备管理**：主机的增删改查、批量操作
- **CSV批量导入**：支持CSV/Excel文件批量导入主机数据
- **自定义字段管理**：支持主机的自定义字段定义和值管理
- **租户隔离**：基于租户的数据隔离和权限控制
- **事件发布**：向JumpServer同步服务发布主机变更事件

### 1.2 核心功能
- 主机生命周期管理（创建、更新、删除）
- 批量导入导出功能
- 自定义字段扩展
- 租户和分类管理
- 与RDB模块的数据同步

### 1.3 架构定位
```
┌─────────────┐    HTTP API    ┌─────────────┐    Events    ┌─────────────────┐
│   前端UI    │ ──────────────→ │ AMS Module  │ ────────────→ │ JumpServer-Sync │
└─────────────┘                └─────────────┘              └─────────────────┘
                                       │
                                       ▼
                               ┌─────────────┐
                               │ AMS Database│
                               │ (MySQL)     │
                               └─────────────┘
```

## 2. 技术架构

### 2.1 技术栈
- **Web框架**：Gin (Go HTTP框架)
- **ORM**：XORM (Go ORM框架)
- **数据库**：MySQL 5.7+
- **文档**：Swagger/OpenAPI 3.0
- **日志**：自定义日志中间件
- **配置**：YAML配置文件

### 2.2 项目结构
```
src/modules/ams/
├── ams.go                 # 主程序入口
├── config/                # 配置管理
│   └── yaml.go           # YAML配置解析
├── http/                  # HTTP服务层
│   ├── router.go         # 路由配置
│   ├── http_server.go    # HTTP服务器
│   ├── host_*.go         # 主机相关接口
│   ├── host_csv.go       # CSV导入功能
│   └── http_middleware.go # 中间件
└── docs/                  # API文档
```

### 2.3 核心组件关系
```
HTTP Layer (Gin Router)
    ├── Authentication Middleware
    ├── Logging Middleware
    ├── CORS Middleware
    └── Business Handlers
            ├── Host Management
            ├── CSV Import/Export
            ├── Custom Fields
            └── Event Publishing
                    ↓
            Data Access Layer (XORM)
                    ↓
            MySQL Database
```

## 3. API接口文档

### 3.1 主机管理接口

#### 3.1.1 获取主机列表
```http
GET /api/ams-ce/hosts
```
**参数**：
- `limit`: 分页大小 (默认20)
- `p`: 页码 (默认1)
- `query`: 搜索关键词
- `tenant`: 租户过滤
- `batch`: 批次过滤
- `field`: 搜索字段 (默认ip)

**响应**：
```json
{
  "err": null,
  "dat": {
    "list": [
      {
        "id": 1,
        "sn": "SN001",
        "ip": "*************",
        "ident": "web-server-01",
        "name": "Web服务器01",
        "cate": "server",
        "tenant": "default"
      }
    ],
    "total": 100
  }
}
```

#### 3.1.2 创建主机
```http
POST /api/ams-ce/host
```
**请求体**：
```json
{
  "sn": "SN001",
  "ip": "*************",
  "ident": "web-server-01",
  "name": "Web服务器01",
  "cate": "server",
  "tenant": "default",
  "fields": {
    "custom_field1": "value1",
    "custom_field2": "value2"
  }
}
```

#### 3.1.3 更新主机
```http
PUT /api/ams-ce/host/{id}
```

#### 3.1.4 删除主机
```http
DELETE /api/ams-ce/hosts
```
**请求体**：
```json
{
  "ids": [1, 2, 3]
}
```

### 3.2 CSV导入接口

#### 3.2.1 批量导入主机
```http
POST /api/ams-ce/hosts/csv/import
```
**参数**：
- `file`: CSV/Excel文件 (multipart/form-data)

**响应**：
```json
{
  "err": "some rows failed to import",
  "dat": {
    "success_count": 8,
    "failed_count": 2,
    "total_rows": 10,
    "import_id": "import_20250820_151234_abc123"
  }
}
```

#### 3.2.2 下载失败数据
```http
GET /api/ams-ce/hosts/csv/failed/{import_id}
```

### 3.3 自定义字段接口

#### 3.3.1 创建自定义字段
```http
POST /api/ams-ce/hosts/fields
```

#### 3.3.2 获取字段列表
```http
GET /api/ams-ce/hosts/fields
```

#### 3.3.3 更新主机字段值
```http
PUT /api/ams-ce/host/{id}/fields
```

## 4. 数据模型

### 4.1 主机表 (host)
```sql
CREATE TABLE `host` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sn` varchar(255) DEFAULT NULL COMMENT '序列号',
  `ip` varchar(64) NOT NULL COMMENT 'IP地址',
  `ident` varchar(255) NOT NULL COMMENT '标识符',
  `name` varchar(255) NOT NULL COMMENT '主机名',
  `os_version` varchar(255) DEFAULT NULL COMMENT '操作系统版本',
  `kernel_version` varchar(255) DEFAULT NULL COMMENT '内核版本',
  `cpu_model` varchar(255) DEFAULT NULL COMMENT 'CPU型号',
  `cpu` varchar(255) DEFAULT NULL COMMENT 'CPU信息',
  `mem` varchar(255) DEFAULT NULL COMMENT '内存信息',
  `disk` varchar(255) DEFAULT NULL COMMENT '磁盘信息',
  `note` text COMMENT '备注',
  `cate` varchar(255) DEFAULT NULL COMMENT '分类',
  `tenant` varchar(255) DEFAULT NULL COMMENT '租户',
  `clock` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  `gpu` varchar(255) DEFAULT NULL COMMENT 'GPU信息',
  `gpu_model` varchar(255) DEFAULT NULL COMMENT 'GPU型号',
  `model` varchar(255) DEFAULT NULL COMMENT '设备型号',
  `idc` varchar(255) DEFAULT NULL COMMENT '机房',
  `zone` varchar(255) DEFAULT NULL COMMENT '可用区',
  `rack` varchar(255) DEFAULT NULL COMMENT '机架',
  `manufacturer` varchar(255) DEFAULT NULL COMMENT '厂商',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ip` (`ip`),
  UNIQUE KEY `idx_ident` (`ident`),
  KEY `idx_tenant` (`tenant`),
  KEY `idx_cate` (`cate`)
);
```

### 4.2 自定义字段表 (host_field)
```sql
CREATE TABLE `host_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `field_ident` varchar(255) NOT NULL COMMENT '字段标识',
  `field_name` varchar(255) NOT NULL COMMENT '字段名称',
  `field_type` varchar(50) NOT NULL COMMENT '字段类型',
  `field_extra` text COMMENT '字段额外配置',
  `required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_field_ident` (`field_ident`)
);
```

### 4.3 主机字段值表 (host_field_value)
```sql
CREATE TABLE `host_field_value` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `host_id` bigint(20) NOT NULL COMMENT '主机ID',
  `field_ident` varchar(255) NOT NULL COMMENT '字段标识',
  `field_value` text COMMENT '字段值',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_host_field` (`host_id`, `field_ident`),
  KEY `idx_field_ident` (`field_ident`)
);
```

## 5. 业务流程

### 5.1 主机创建流程
```
用户提交创建请求
    ↓
参数验证 (IP/标识符唯一性)
    ↓
创建主机记录
    ↓
处理自定义字段值
    ↓
发布主机创建事件
    ↓
返回创建结果
```

### 5.2 CSV导入流程
```
用户上传CSV文件
    ↓
文件格式验证 (CSV/Excel)
    ↓
解析文件内容
    ↓
数据验证 (必填字段、格式检查)
    ↓
批量插入数据库
    ↓
记录失败数据
    ↓
返回导入统计结果
```

### 5.3 主机更新流程
```
用户提交更新请求
    ↓
权限验证
    ↓
数据验证
    ↓
更新主机记录
    ↓
更新自定义字段值
    ↓
发布主机更新事件
    ↓
返回更新结果
```

## 6. 事件机制

### 6.1 事件类型
- `host.create`: 主机创建事件
- `host.update`: 主机更新事件  
- `host.delete`: 主机删除事件

### 6.2 事件数据格式
```json
{
  "id": "event_20250820_151234_abc123",
  "type": "host.create",
  "source": "ams",
  "timestamp": 1755660498,
  "data": {
    "id": 123,
    "sn": "SN001",
    "ip": "*************",
    "ident": "web-server-01",
    "name": "Web服务器01",
    "cate": "server",
    "tenant": "default"
  },
  "metadata": {
    "entity_type": "host",
    "entity_id": "123"
  }
}
```

### 6.3 事件发布机制
- 使用Redis Streams作为消息队列
- 异步发布，不影响主业务流程
- 支持事件重试和死信队列

## 7. 权限模型

### 7.1 认证机制
- **Cookie认证**：基于Session的Web认证
- **Token认证**：基于X-User-Token的API认证

### 7.2 权限检查
- 所有接口需要登录认证
- 基于用户权限进行操作授权
- 支持租户级别的数据隔离

## 8. 配置说明

### 8.1 主配置文件 (etc/ams.yml)
```yaml
logger:
  dir: logs/ams
  level: INFO
  keepHours: 168

http:
  mode: debug
  cookieName: ecmc-sid
  cookieDomain: ""

i18n:
  dictPath: etc/dict.json
  lang: zh

redis:
  addr: "*********3:6379"
  password: "4KyCLw2TEMWsWCz8"
  db: 0
```

### 8.2 数据库配置 (etc/mysql.yml)
```yaml
ams:
  addr: "root:password@tcp(*********3:3306)/arboris_ams?charset=utf8&parseTime=True&loc=Asia%2FShanghai"
  max: 100
  idle: 10
  debug: false

rdb:
  addr: "root:password@tcp(*********3:3306)/arboris_rdb?charset=utf8&parseTime=True&loc=Asia%2FShanghai"
  max: 100
  idle: 10
  debug: false
```

### 8.3 关键配置项说明
- **logger.keepHours**: 日志保留时间（小时）
- **http.mode**: HTTP服务模式 (debug/release)
- **redis**: Redis连接配置，用于事件发布
- **mysql**: 数据库连接配置，支持多数据库
