#!/bin/bash

# 测试 Swagger 文档的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Swagger 文档测试 ===${NC}"

# 测试文档文件是否存在
test_docs_exist() {
    echo -e "${BLUE}检查文档文件是否存在...${NC}"
    
    local modules=("ams" "jumpserver-sync")
    local missing_docs=()
    
    for module in "${modules[@]}"; do
        local doc_dir="docs-${module}"
        if [[ ! -d "$doc_dir" ]]; then
            missing_docs+=("$module")
            continue
        fi
        
        local required_files=("docs.go" "swagger.json" "swagger.yaml")
        for file in "${required_files[@]}"; do
            if [[ ! -f "$doc_dir/$file" ]]; then
                missing_docs+=("$module ($file)")
            fi
        done
    done
    
    if [[ ${#missing_docs[@]} -gt 0 ]]; then
        echo -e "${RED}❌ 缺少文档: ${missing_docs[*]}${NC}"
        echo -e "${YELLOW}💡 请先运行相应的生成脚本${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 所有文档文件存在${NC}"
    return 0
}

# 验证 JSON 格式
test_json_format() {
    echo -e "${BLUE}验证 JSON 格式...${NC}"
    
    local modules=("ams" "jumpserver-sync")
    local invalid_json=()
    
    for module in "${modules[@]}"; do
        local json_file="docs-${module}/swagger.json"
        if [[ -f "$json_file" ]]; then
            if ! python3 -m json.tool "$json_file" > /dev/null 2>&1; then
                invalid_json+=("$module")
            fi
        fi
    done
    
    if [[ ${#invalid_json[@]} -gt 0 ]]; then
        echo -e "${RED}❌ JSON 格式错误: ${invalid_json[*]}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ JSON 格式验证通过${NC}"
    return 0
}

# 检查 API 路径
test_api_paths() {
    echo -e "${BLUE}检查 API 路径...${NC}"
    
    # 检查 AMS 模块
    if [[ -f "docs-ams/swagger.json" ]]; then
        if grep -q "/api/ams-ce" docs-ams/swagger.json; then
            echo -e "${GREEN}✅ AMS API 路径正确${NC}"
        else
            echo -e "${RED}❌ AMS API 路径缺失${NC}"
        fi
    fi
    
    # 检查 JumpServer同步模块
    if [[ -f "docs-jumpserver-sync/swagger.json" ]]; then
        if grep -q "/api/admin\|/health\|/info" docs-jumpserver-sync/swagger.json; then
            echo -e "${GREEN}✅ JumpServer同步 API 路径正确${NC}"
        else
            echo -e "${RED}❌ JumpServer同步 API 路径缺失${NC}"
        fi
    fi
}

# 检查文档内容
test_doc_content() {
    echo -e "${BLUE}检查文档内容...${NC}"
    
    local modules=("ams" "jumpserver-sync")
    
    for module in "${modules[@]}"; do
        local json_file="docs-${module}/swagger.json"
        if [[ -f "$json_file" ]]; then
            # 检查基本信息
            if grep -q '"title"' "$json_file" && grep -q '"version"' "$json_file"; then
                echo -e "${GREEN}✅ ${module} 文档包含基本信息${NC}"
            else
                echo -e "${YELLOW}⚠️  ${module} 文档缺少基本信息${NC}"
            fi
            
            # 检查路径数量
            local path_count=$(grep -o '"paths"' "$json_file" | wc -l)
            if [[ $path_count -gt 0 ]]; then
                echo -e "${GREEN}✅ ${module} 文档包含 API 路径${NC}"
            else
                echo -e "${RED}❌ ${module} 文档缺少 API 路径${NC}"
            fi
        fi
    done
}

# 显示文档统计信息
show_doc_stats() {
    echo -e "${BLUE}文档统计信息:${NC}"
    
    local modules=("ams" "jumpserver-sync")
    
    for module in "${modules[@]}"; do
        local doc_dir="docs-${module}"
        if [[ -d "$doc_dir" ]]; then
            echo -e "${BLUE}📊 ${module} 模块:${NC}"
            
            if [[ -f "$doc_dir/swagger.json" ]]; then
                local json_size=$(du -h "$doc_dir/swagger.json" | cut -f1)
                echo "   swagger.json: $json_size"
            fi
            
            if [[ -f "$doc_dir/swagger.yaml" ]]; then
                local yaml_size=$(du -h "$doc_dir/swagger.yaml" | cut -f1)
                echo "   swagger.yaml: $yaml_size"
            fi
            
            if [[ -f "$doc_dir/docs.go" ]]; then
                local go_size=$(du -h "$doc_dir/docs.go" | cut -f1)
                echo "   docs.go: $go_size"
            fi
            
            echo ""
        fi
    done
}

# 显示访问信息
show_access_info() {
    echo -e "${BLUE}📖 文档访问方式:${NC}"
    echo ""
    echo -e "${GREEN}在线访问 (需要启动服务):${NC}"
    echo "   AMS 模块: http://localhost:8002/swagger/index.html"
    echo "   JumpServer同步: http://localhost:8003/swagger/index.html"
    echo ""
    echo -e "${GREEN}本地文件:${NC}"
    echo "   AMS: docs-ams/swagger.yaml"
    echo "   JumpServer同步: docs-jumpserver-sync/swagger.yaml"
    echo ""
    echo -e "${GREEN}在线编辑器:${NC}"
    echo "   访问 https://editor.swagger.io/ 并导入本地 YAML 文件"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}开始测试 Swagger 文档...${NC}"
    echo ""
    
    local test_passed=true
    
    if ! test_docs_exist; then
        test_passed=false
    fi
    
    if ! test_json_format; then
        test_passed=false
    fi
    
    test_api_paths
    test_doc_content
    
    echo ""
    show_doc_stats
    show_access_info
    
    if [[ "$test_passed" == "true" ]]; then
        echo -e "${GREEN}🎉 所有测试通过！${NC}"
        return 0
    else
        echo -e "${RED}❌ 部分测试失败${NC}"
        return 1
    fi
}

# 执行主函数
main "$@"
