#!/bin/bash

# 测试账号模板更新功能的脚本

echo "=== 测试账号模板更新功能 ==="

# 测试参数
TEMPLATE_ID="1"  # 假设模板ID为1
ORIGINAL_NAME="rdb测试模板"
NEW_NAME="rdb测试模板1"

echo "测试参数:"
echo "  模板ID: $TEMPLATE_ID"
echo "  原始名称: $ORIGINAL_NAME"
echo "  新名称: $NEW_NAME"
echo ""

# 首先查看当前模板状态
echo "1. 查看当前模板状态..."
curl -s "http://localhost:8002/api/rdb/account-templates/$TEMPLATE_ID" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" | jq '.dat | {id, name, username, comment}' 2>/dev/null || echo "无法查询模板状态"

echo ""

# 模拟模板更新
echo "2. 更新模板名称..."

# 创建更新数据
cat > /tmp/template_update.json << EOF
{
  "name": "$NEW_NAME",
  "username": "root",
  "secret_type": "password",
  "secret": "test123",
  "comment": "更新后的测试模板"
}
EOF

echo "更新数据:"
cat /tmp/template_update.json | jq .
echo ""

# 发送更新请求
echo "3. 发送更新请求..."
UPDATE_RESULT=$(curl -s -X PUT "http://localhost:8002/api/rdb/account-templates/$TEMPLATE_ID" \
  -H "Content-Type: application/json" \
  -H "Cookie: ecmc-sid=test-session" \
  -d @/tmp/template_update.json)

echo "更新结果:"
echo "$UPDATE_RESULT" | jq . 2>/dev/null || echo "$UPDATE_RESULT"
echo ""

# 等待事件处理
echo "4. 等待事件处理（5秒）..."
sleep 5

echo "5. 检查jumpserver-sync日志..."
echo "最新的模板更新事件处理日志:"
tail -20 logs/jumpserver-sync/INFO.log | grep -E "(account_template\.update|template|original_name)" || echo "没有找到相关日志"

echo ""
echo "6. 检查RDB日志..."
echo "最新的模板更新事件发布日志:"
tail -20 logs/rdb/INFO.log | grep -E "(template|update|event)" || echo "没有找到相关日志"

echo ""
echo "期望看到的关键日志:"
echo "1. RDB日志: 发布账号模板更新事件成功"
echo "2. jumpserver-sync日志: Processing account template update: ...original_name=$ORIGINAL_NAME"
echo "3. jumpserver-sync日志: Searching template by original name: original_name=$ORIGINAL_NAME"
echo "4. jumpserver-sync日志: Found template by original name: js_id=xxx, name=xxx"
echo "5. jumpserver-sync日志: Account template updated successfully"

echo ""
echo "如果看到以上日志，说明模板更新逻辑已经修复成功！"

# 清理临时文件
rm -f /tmp/template_update.json
